import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Alert,
  Divider,
} from '@mui/material';
import { Error, Refresh, ArrowBack, Support } from '@mui/icons-material';
import { usePayment } from '../../hooks/payment/usePayment';
import styles from './PaymentError.module.css';

const PaymentError: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { retryPayment, cancelPayment, currentPlan } = usePayment();

  const error = location.state?.error as string;
  const errorMessage =
    error || 'An unexpected error occurred during payment processing.';

  const handleRetry = () => {
    retryPayment();
  };

  const handleGoBack = () => {
    navigate('/price');
  };

  const handleContactSupport = () => {
    // Open email client or navigate to support page
    window.location.href =
      'mailto:<EMAIL>?subject=Payment Issue&body=I encountered an issue with my payment. Error: ' +
      encodeURIComponent(errorMessage);
  };

  const getErrorSuggestion = (errorMessage: string): string => {
    const lowerError = errorMessage.toLowerCase();

    if (lowerError.includes('card') && lowerError.includes('declined')) {
      return 'Your card was declined. Please try a different payment method or contact your bank.';
    }

    if (lowerError.includes('insufficient')) {
      return 'Your card has insufficient funds. Please try a different payment method.';
    }

    if (lowerError.includes('expired')) {
      return 'Your card has expired. Please update your payment information.';
    }

    if (lowerError.includes('cvc') || lowerError.includes('security')) {
      return 'The security code (CVC) is incorrect. Please check your card and try again.';
    }

    if (lowerError.includes('network') || lowerError.includes('connection')) {
      return 'There was a network issue. Please check your internet connection and try again.';
    }

    return 'Please try again or contact support if the problem persists.';
  };

  return (
    <Box className={styles.container}>
      <Card className={styles.errorCard}>
        <CardContent className={styles.content}>
          {/* Error Icon */}
          <div className={styles.iconContainer}>
            <Error className={styles.errorIcon} />
          </div>

          {/* Error Message */}
          <Typography variant="h4" className={styles.title}>
            Payment Failed
          </Typography>

          <Typography variant="body1" className={styles.subtitle}>
            We encountered an issue processing your payment.
          </Typography>

          {/* Error Details */}
          <Alert severity="error" className={styles.errorAlert}>
            <Typography variant="body2">
              <strong>Error:</strong> {errorMessage}
            </Typography>
          </Alert>

          {/* Suggestion */}
          <Alert severity="info" className={styles.suggestionAlert}>
            <Typography variant="body2">
              <strong>Suggestion:</strong> {getErrorSuggestion(errorMessage)}
            </Typography>
          </Alert>

          <Divider className={styles.divider} />

          {/* Plan Information */}
          {currentPlan && (
            <div className={styles.planInfo}>
              <Typography variant="h6" className={styles.planTitle}>
                Selected Plan
              </Typography>
              <div className={styles.planDetails}>
                <Typography variant="body1" className={styles.planName}>
                  {currentPlan.name}
                </Typography>
                <Typography variant="body2" className={styles.planPrice}>
                  ${currentPlan.price.toFixed(2)}/{currentPlan.interval}
                </Typography>
              </div>
            </div>
          )}

          <Divider className={styles.divider} />

          {/* Troubleshooting Tips */}
          <div className={styles.troubleshooting}>
            <Typography variant="h6" className={styles.troubleshootingTitle}>
              Troubleshooting Tips
            </Typography>

            <ul className={styles.tipsList}>
              <li>Check that your card information is entered correctly</li>
              <li>Ensure your card has sufficient funds</li>
              <li>Verify that your card is not expired</li>
              <li>Try using a different payment method</li>
              <li>Check your internet connection</li>
              <li>Contact your bank if the issue persists</li>
            </ul>
          </div>

          {/* Action Buttons */}
          <div className={styles.actions}>
            <Button
              variant="outlined"
              onClick={handleGoBack}
              startIcon={<ArrowBack />}
              className={styles.secondaryButton}
            >
              Choose Different Plan
            </Button>

            <Button
              variant="contained"
              onClick={handleRetry}
              startIcon={<Refresh />}
              className={styles.primaryButton}
            >
              Try Again
            </Button>
          </div>

          {/* Support Section */}
          <div className={styles.support}>
            <Typography variant="body2" className={styles.supportText}>
              Still having trouble?
            </Typography>

            <Button
              variant="text"
              onClick={handleContactSupport}
              startIcon={<Support />}
              className={styles.supportButton}
            >
              Contact Support
            </Button>
          </div>

          {/* Additional Info */}
          <Typography variant="body2" className={styles.additionalInfo}>
            No charges were made to your account. You can safely try again.
          </Typography>
        </CardContent>
      </Card>
    </Box>
  );
};

export default PaymentError;

import React, { useState, useEffect } from 'react';
import styles from './ProjectDialog.module.css';
import { PredefinedPrompt } from '../../../types/predefinedPrompts';
import { FiUploadCloud, FiFile } from 'react-icons/fi';
import { useDropzone } from 'react-dropzone';
import LoadingSpinner from '../loading/LoadingSpinner';

interface ProjectDialogProps {
  onClose: () => void;
  onSelectProject: (
    projectId: string,
    pageRanges: string[],
    promptText: string,
    file: File,
    promptId: string
  ) => void;
  rangeInput: string;
  setRangeInput: (value: string) => void;
  title: string;
  setTitle: (value: string) => void;
  selectedPrompt?: PredefinedPrompt | null;
  uploadedFile?: File | null;
  isLoading?: boolean;
}

const ProjectDialog: React.FC<ProjectDialogProps> = ({
  onClose,
  onSelectProject,
  title,
  setTitle,
  selectedPrompt,
  uploadedFile: initialUploadedFile,
  isLoading = false,
}) => {
  const [uploadedFile, setUploadedFile] = useState<File | null>(
    initialUploadedFile || null
  );
  const [selectedSubPrompt, setSelectedSubPrompt] = useState<string | null>(
    null
  );
  const [customPrompt, setCustomPrompt] = useState('');
  const [isProjectProcessing, setIsProjectProcessing] = useState(false);
  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    accept: {
      'application/pdf': ['.pdf'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
        ['.docx'],
      'application/msword': ['.doc'],
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': [
        '.xlsx',
      ],
      'text/csv': ['.csv'],
      'text/plain': ['.txt'],
    },
    onDrop: (acceptedFiles) => {
      if (acceptedFiles?.[0]) {
        setUploadedFile(acceptedFiles[0]);
        // Don't auto-fill title - let user type their own project name
      }
    },
  });

  // Removed auto-title setting - let users type their own project name

  // If there's only one prompt, auto-select it
  useEffect(() => {
    if (selectedPrompt && Object.keys(selectedPrompt.prompts).length === 1) {
      setSelectedSubPrompt(Object.keys(selectedPrompt.prompts)[0]);
    } else {
      setSelectedSubPrompt(null);
    }
  }, [selectedPrompt]);

  useEffect(() => {
    if (customPrompt.trim()) {
      setSelectedSubPrompt(null);
    }
  }, [customPrompt]);

  useEffect(() => {
    if (selectedSubPrompt) {
      setCustomPrompt('');
    }
  }, [selectedSubPrompt]);

  const handleCreateProject = () => {
    if (!title.trim() || !uploadedFile) return;
    setIsProjectProcessing(true);

    let promptText = '';
    let promptId = '';

    if (customPrompt.trim()) {
      promptText = `${selectedPrompt?.category} ||| ${selectedPrompt?.id} ||| ${customPrompt.trim()}`;
      promptId = `${selectedPrompt?.category} ||| ${selectedPrompt?.id} ||| ${customPrompt.trim()}`;
    } else if (selectedSubPrompt && selectedPrompt) {
      promptText = selectedPrompt.prompts[selectedSubPrompt];
      promptId = selectedSubPrompt;
    }

    const newProjectId = `project-${Date.now()}`;
    onSelectProject(newProjectId, [], promptText, uploadedFile, promptId);
  };

  return (
    <div className={styles.dialogOverlay}>
      <div className={styles.dialog}>
        <div className={styles.dialogHeader}>
          <h2>
            Create New Project
            {` ${selectedPrompt?.title ? selectedPrompt.title : ''}`}
          </h2>
          <button className={styles.closeButton} onClick={onClose}>
            ×
          </button>
        </div>

        <div className={styles.dialogContent}>
          <div className={styles.twoColumnLayout}>
            {/* Left Column */}
            <div className={styles.leftColumn}>
              <div className={styles.titleSection}>
                <h3
                  style={{ marginBottom: '5px' }}
                  className={styles.promptHeading}
                >
                  Project Name
                </h3>
                <input
                  type="text"
                  className={styles.titleInput}
                  placeholder="Enter your project name..."
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                />
              </div>

              <div className={styles.uploadSection}>
                {!uploadedFile ? (
                  <div
                    id="dropzone-container"
                    {...getRootProps()}
                    className={`${styles.dropZone} ${isDragActive ? styles.dragActive : ''}`}
                  >
                    <input {...getInputProps()} />
                    <FiUploadCloud size={24} />
                    <p className={styles.dropText}>
                      Drop file here or click to browse
                    </p>
                    <span className={styles.supportedFormats}>
                      PDF, DOCX, DOC, XLSX, CSV, TXT
                    </span>
                  </div>
                ) : (
                  <div className={styles.filePreview}>
                    <FiFile size={20} />
                    <span className={styles.fileName}>{uploadedFile.name}</span>
                    <button
                      className={styles.removeButton}
                      onClick={() => setUploadedFile(null)}
                    >
                      ×
                    </button>
                  </div>
                )}
              </div>
            </div>

            {/* Right Column */}
            <div className={styles.rightColumn}>
              {selectedPrompt && (
                // <div className={styles.promptSection}>
                <div className={styles.promptSection}>
                  {/* </div> */}

                  <h3 className={styles.promptHeading}>Available Prompts</h3>
                  <div id="availablePrompts" className={styles.promptCards}>
                    {Object.entries(selectedPrompt.prompts).map(
                      ([key, prompt]) => (
                        <div
                          key={key}
                          className={`${styles.promptCard} ${
                            selectedSubPrompt === key ? styles.selectedCard : ''
                          }`}
                          onClick={() => setSelectedSubPrompt(key)}
                        >
                          <p className={styles.promptText}>{prompt}</p>
                        </div>
                      )
                    )}
                  </div>
                  <h3 className={styles.promptHeading}>
                    Or Write Your Own Prompt
                  </h3>
                  <textarea
                    id="customPromptInput"
                    className={styles.customPromptInput}
                    placeholder="Type your custom prompt here..."
                    value={customPrompt}
                    onChange={(e) => setCustomPrompt(e.target.value)}
                    rows={4}
                    style={{
                      width: '100%',
                      padding: '8px',
                      borderRadius: '8px',
                    }}
                  />
                </div>
              )}
            </div>
          </div>
        </div>

        {!isProjectProcessing && !isLoading && (
          <div className={styles.dialogFooter}>
            <button className={styles.secondaryButton} onClick={onClose}>
              Cancel
            </button>
            <button
              id="createProjectButton"
              className={styles.primaryButton}
              onClick={handleCreateProject}
              disabled={!title.trim() || !uploadedFile}
            >
              Create Project
            </button>
          </div>
        )}

        {/* Loading overlay */}
        {isLoading && (
          <div className={styles.loadingOverlay}>
            <LoadingSpinner
              size="large"
              message="Creating project from library document..."
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default ProjectDialog;

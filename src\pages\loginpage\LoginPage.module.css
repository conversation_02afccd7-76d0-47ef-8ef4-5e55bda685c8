.loginContainer {
  position: fixed;
  background-color: white;
  background-image: url('../../assets/images/Login.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: right;
  overflow-y: auto;
  height: 100vh;
  width: 100vw;
}

.headingContainer {
  margin-top: 30px;
  margin-left: 132px;
  text-align: left;
  display: flex;
  flex-direction: column;
  width: 50%;
}

.heading {
  font-family: var(--font-family-primary);
  font-size: 48px;
  font-weight: var(--font-weight-semibold);
  color: #0e2f51;
}

.subHeading {
  font-family: var(--font-family-primary);
  font-size: 16px;
  font-weight: var(--font-weight-normal);
  color: #667085;
}

.formContainer {
  width: 30%;
  margin-left: 132px;
  margin-top: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.vectorLineContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
}

.linkContainer {
  font-family: var(--font-family-primary);
  font-size: 14px;
  color: #0e2f51;
  font-weight: var(--font-weight-medium);
  margin-top: 5px;
  margin-bottom: 10px;
}

.link {
  color: #1b5ea1;
  font-size: 14px;
  font-weight: var(--font-weight-semibold);
  text-decoration: none;
  margin-left: 4px;
}

.link:hover {
  text-decoration: underline;
}

.checkboxContainer {
  display: flex;
  gap: 10px;
}

.error {
  color: var(--color-error-main);
  font-family: var(--font-family-primary);
  font-size: 14px;
  font-weight: var(--font-weight-normal);
}

.optionalText {
  font-family: var(--font-family-primary);
  font-size: 14px;
  font-weight: var(--font-weight-semibold);
  font-style: italic;
  color: #667085;
}

/* Force light theme for login page - completely override dark theme styles */
.loginContainer [data-theme='dark'] .inputLabel,
.loginContainer .inputLabel,
.loginContainer [data-theme='dark'] .force-light-theme .inputLabel,
.loginContainer .force-light-theme .inputLabel,
.loginContainer [data-force-light-theme='true'] .inputLabel {
  color: #0e2f51 !important;
}

.loginContainer [data-theme='dark'] .input,
.loginContainer .input,
.loginContainer [data-theme='dark'] .force-light-theme .input,
.loginContainer .force-light-theme .input,
.loginContainer [data-force-light-theme='true'] .input {
  background-color: #ffffff !important;
  border-color: #e0e0e0 !important;
  color: #0e2f51 !important;
}

.loginContainer [data-theme='dark'] .input::placeholder,
.loginContainer .input::placeholder,
.loginContainer [data-theme='dark'] .force-light-theme .input::placeholder,
.loginContainer .force-light-theme .input::placeholder,
.loginContainer [data-force-light-theme='true'] .input::placeholder {
  color: #667085 !important;
}

.loginContainer [data-theme='dark'] .helperText,
.loginContainer .helperText,
.loginContainer [data-theme='dark'] .force-light-theme .helperText,
.loginContainer .force-light-theme .helperText,
.loginContainer [data-force-light-theme='true'] .helperText {
  color: #667085 !important;
}

.loginContainer [data-theme='dark'] .eyeIcon,
.loginContainer .eyeIcon,
.loginContainer [data-theme='dark'] .force-light-theme .eyeIcon,
.loginContainer .force-light-theme .eyeIcon,
.loginContainer [data-force-light-theme='true'] .eyeIcon {
  color: #1b5ea1 !important;
}

.loginContainer [data-theme='dark'] .required,
.loginContainer .required,
.loginContainer [data-theme='dark'] .force-light-theme .required,
.loginContainer .force-light-theme .required,
.loginContainer [data-force-light-theme='true'] .required {
  color: #d32f2f !important;
}

/* Focus and error states */
.loginContainer [data-theme='dark'] .input:focus,
.loginContainer .input:focus,
.loginContainer [data-theme='dark'] .force-light-theme .input:focus,
.loginContainer .force-light-theme .input:focus,
.loginContainer [data-force-light-theme='true'] .input:focus {
  border-color: #1b5ea1 !important;
  box-shadow: 0 0 0 2px rgba(27, 94, 161, 0.1) !important;
}

.loginContainer [data-theme='dark'] .inputError,
.loginContainer .inputError,
.loginContainer [data-theme='dark'] .force-light-theme .inputError,
.loginContainer .force-light-theme .inputError,
.loginContainer [data-force-light-theme='true'] .inputError {
  border-color: #d32f2f !important;
}

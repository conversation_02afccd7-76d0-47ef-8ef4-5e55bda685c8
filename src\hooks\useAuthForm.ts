import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

const loginSchema = z.object({
  email: z.string().min(1, 'User Id is required'),
  password: z.string().min(1, 'Password is required'),
});

const registrationSchema = z.object({
  name: z.string().min(3, 'Name must contain at least 3 characters'),
  email: z.string().email('Invalid email address'),
  organization: z.string().optional(),
  password: z
    .string()
    .min(8, 'Password must be at least 8 characters long')
    .max(30, 'Password must be less than 30 characters long')
    .regex(/[A-Z]/, 'Password must contain at least one uppercase letter.')
    .regex(/[a-z]/, 'Password must contain at least one lowercase letter.')
    .regex(/\d/, 'Password must contain at least one digit.')
    .regex(
      /[^A-Za-z0-9]/,
      'Password must contain at least one special character.'
    )
    .regex(/^\S*$/, 'Password must not contain any whitespace characters.'),
  agreeTerms: z
    .boolean()
    .refine(
      (val) => val === true,
      'You must agree to the terms and conditions'
    ),
});

const forgotPasswordSchema = z.object({
  email: z.string().email('Invalid email address'),
});

const resetPasswordSchema = z.object({
  password: z
    .string()
    .min(8, 'Password must be at least 8 characters long')
    .max(30, 'Password must be less than 30 characters long')
    .regex(/[A-Z]/, 'Password must contain at least one uppercase letter.')
    .regex(/[a-z]/, 'Password must contain at least one lowercase letter.')
    .regex(/\d/, 'Password must contain at least one digit.')
    .regex(
      /[^A-Za-z0-9]/,
      'Password must contain at least one special character.'
    )
    .regex(/^\S*$/, 'Password must not contain any whitespace characters.'),
  confirmPassword: z.string().min(1, 'Password is required'),
});

export type LoginFormData = z.infer<typeof loginSchema>;
export type RegistrationFormData = z.infer<typeof registrationSchema>;
export type ForgotPasswordFormData = z.infer<typeof forgotPasswordSchema>;
export type ResetPasswordFormData = z.infer<typeof resetPasswordSchema>;

export const useLoginForm = () => {
  return useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  });
};

export const useRegistrationForm = () => {
  return useForm<RegistrationFormData>({
    resolver: zodResolver(registrationSchema),
  });
};

export const useForgotPasswordForm = () => {
  return useForm<ForgotPasswordFormData>({
    resolver: zodResolver(forgotPasswordSchema),
  });
};

export const useResetPasswordForm = () => {
  return useForm<ResetPasswordFormData>({
    resolver: zodResolver(resetPasswordSchema),
  });
};

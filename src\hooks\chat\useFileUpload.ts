import { useState } from 'react';
import { countPagesInFile } from '../../utils/pageCounter';
import { getFileIcon } from '../../utils/getFileExtention';
import { createFormData } from '../../utils/formDataHelper';
import { getSimplifiedFileExtension } from '../../utils/getFileExtention';
import toast from 'react-hot-toast';
import { checkFileInBrowser } from '../../utils/extractFileSize';
import { trackContent, trackError } from '../../utils/analytics';

interface UseFileUploadProps {
  userInLocalStorage: any;
  projectID: string;
  pageRangeChanges: string;
  newProfileImage: string;
  senderIcon: string;
  botIcon: string;
  addMessage: (message: any) => void;
  setMessages: (updater: any) => void;
  uploadFile: any;
  dispatch: any;
  complianceService: any;
  setIsProjectWindowOpen: any;
  setPageRangeChanges?: (pageRange: string) => void;
}

export const useFileUpload = ({
  userInLocalStorage,
  projectID,
  pageRangeChanges,
  newProfileImage,
  senderIcon,
  botIcon,
  addMessage,
  setMessages,
  uploadFile,
  dispatch,
  complianceService,
  setIsProjectWindowOpen,
  setPageRangeChanges,
}: UseFileUploadProps) => {
  const [isProcessing, setIsProcessing] = useState(false);
  const [uploadFileWithinChat, setUploadFileWithinChat] = useState<File>();

  const handleFileUpload = async (file: File) => {
    const fileSizeSupportForFreeUsers = await checkFileInBrowser(file);
    console.log(fileSizeSupportForFreeUsers, 'hello');
    if (!fileSizeSupportForFreeUsers.isAllowed) {
      toast.error(fileSizeSupportForFreeUsers.message, {
        duration: 6000,
      });
      return;
    }
    if (isProcessing) return;

    if (!userInLocalStorage?.id) {
      toast.error('User ID is required');
      throw new Error('User ID is required');
    }

    setIsProcessing(true);
    setUploadFileWithinChat(file);

    // Track file upload
    if (userInLocalStorage?.id) {
      trackContent.uploadFile(
        file.type || 'unknown',
        file.size,
        userInLocalStorage.id
      );
    }

    const numberOfPages = await countPagesInFile(file);

    const storedPageRange = localStorage.getItem('pageRangeChanges');
    const currentPageRange =
      storedPageRange !== null && storedPageRange !== 'undefined'
        ? storedPageRange
        : pageRangeChanges || '';

    const currentResponseType = localStorage.getItem('responseType') || 'brief';

    // Display the file upload message
    addMessage({
      sender: 'user',
      content: file.name,
      type: 'light',
      icon: getFileIcon(file.name),
      image: newProfileImage || senderIcon,
      timestamp: new Date().toLocaleTimeString(),
      noOfPages: numberOfPages,
    });

    // Display the loading message
    addMessage({
      sender: 'bot',
      content: 'Processing your file...',
      type: 'light',
      image: botIcon,
      timestamp: new Date().toLocaleTimeString(),
    });

    const fileExtension = getSimplifiedFileExtension(file);

    const formData = createFormData({
      userId: userInLocalStorage?.id,
      projectId: projectID,
      pageNumber: currentPageRange,
      fileName: file.name,
      fileType: fileExtension,
      prompt: ' ',
      file: file,
      response_type: currentResponseType,
    });

    try {
      const response: any = await uploadFile({ formData }).unwrap();

      dispatch(complianceService.util.invalidateTags(['Files']));
      dispatch(setIsProjectWindowOpen(true));

      // Update the loading message with success
      setMessages((prevMessages: any) => {
        const updatedMessages = prevMessages.map((message: any) => {
          if (message.content === 'Processing your file...') {
            return {
              ...message,
              content:
                'Thank you for uploading your document. What would you like to do next ?',
            };
          }
          return message;
        });
        return updatedMessages;
      });

      // Handle referenced page range from response (like the old implementation)
      if (response.page_no && setPageRangeChanges) {
        setPageRangeChanges(response.page_no);
      }

      return response;
    } catch (error) {
      console.error('Error processing your file:', error);
      // Update the loading message with error
      setMessages((prevMessages: any) => {
        const updatedMessages = prevMessages.map((message: any) => {
          if (message.content === 'Processing your file...') {
            return {
              ...message,
              content:
                'An error occurred while processing your file. Please try again.',
            };
          }
          return message;
        });
        return updatedMessages;
      });
      throw error;
    } finally {
      setIsProcessing(false);
    }
  };

  return {
    isProcessing,
    uploadFileWithinChat,
    setUploadFileWithinChat,
    handleFileUpload,
  };
};

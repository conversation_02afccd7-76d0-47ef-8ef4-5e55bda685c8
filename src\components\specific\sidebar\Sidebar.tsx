import { useEffect, useState } from 'react';
import CustomIconButton from '../../common/button/IconButton';
import {
  IoCheckmarkDoneCircleOutline,
  IoShieldCheckmarkOutline,
  IoDownloadOutline,
  IoCloseOutline,
} from 'react-icons/io5';
import {
  PiHouseLight,
  PiFolderSimpleLight,
  PiClockCounterClockwiseLight,
  PiNotebookLight,
  PiShieldCheckLight,
  PiBookOpenLight,
  PiGraduationCapLight,
  PiUserLight,
  PiCreditCardLight,
  PiReceiptLight,
  PiUsersLight,
  PiGearLight,
  PiGithubLogoLight,
  PiTwitterLogoLight,
  PiLinkedinLogoLight,
  PiDiscordLogoLight,
  PiBooksLight,
  PiInstagramLogo,
  PiInstagramLogoLight,
  PiFacebookLogoLight,
  PiYoutubeLogoLight,
} from 'react-icons/pi';
import { GoSidebarCollapse, GoSidebarExpand } from 'react-icons/go';
import { Bs<PERSON>ersonWorkspace } from 'react-icons/bs';
import Select from 'react-select';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  Paper,
  Typography,
  IconButton,
  Box,
  Tooltip,
} from '@mui/material';
import { styled, useTheme, alpha, duration } from '@mui/material/styles';

import styles from './Sidebar.module.css';

import { NavLink, useLocation } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { useAnalyticsEvents } from '../../../hooks/useAnalytics';
import { setResponseType } from '../../../store/settingsSlice';
import {
  useGenerateComplianceReportMutation,
  useGetFilesForComplianceQuery,
  useGenerateCompliancePDFMutation,
  useGetPreviousComplianceReportsQuery,
  useDownloadPreviousComplianceReportMutation,
} from '../../../services/complianceServices';
import useLocalStorage from '../../../hooks/useLocalStorage';
import toast from 'react-hot-toast';
import CustomButton from '../../common/button/CustomButton';
import { MdDeselect } from 'react-icons/md';
import { BiTime } from 'react-icons/bi';
import { LibraryBooks, LibraryBooksOutlined } from '@mui/icons-material';
import { formatTimestamp } from '../../../utils/timesAgo';

type FileOption = {
  value: string;
  label: string;
};

interface SidebarProps {
  onCollapse?: (collapsed: boolean) => void;
}

// Styled components for consistent theming
const StyledDialog = styled(Dialog)(({ theme }) => ({
  '& .MuiDialog-paper': {
    borderRadius: '12px',
    backgroundColor: theme.palette.background.paper,
    boxShadow:
      theme.palette.mode === 'dark'
        ? '0 8px 32px rgba(0, 0, 0, 0.3)'
        : '0 8px 32px rgba(0, 0, 0, 0.08)',
  },
}));

const StyledDialogTitle = styled(DialogTitle)(({ theme }) => ({
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  padding: theme.spacing(2, 3),
  backgroundColor:
    theme.palette.mode === 'dark'
      ? theme.palette.background.default
      : theme.palette.grey[50],
  color: theme.palette.text.primary,
}));

const StyledDialogContent = styled(DialogContent)(({ theme }) => ({
  padding: theme.spacing(3),
  backgroundColor: theme.palette.background.paper,
  color: theme.palette.text.primary,
}));

// Create a custom close button component that wraps MUI IconButton
const CloseButton = ({
  onClick,
  size,
  'aria-label': ariaLabel,
  children,
}: any) => {
  const theme = useTheme();
  const isDark = theme.palette.mode === 'dark';

  return (
    <IconButton
      onClick={onClick}
      size={size}
      aria-label={ariaLabel}
      sx={{
        color: isDark ? '#ffffff' : theme.palette.text.secondary,
        backgroundColor: isDark
          ? 'rgba(255, 255, 255, 0.1)'
          : 'rgba(0, 0, 0, 0.05)',
        padding: '8px',
        '&:hover': {
          color: isDark ? '#ffffff' : theme.palette.text.primary,
          backgroundColor: isDark
            ? 'rgba(255, 255, 255, 0.2)'
            : 'rgba(0, 0, 0, 0.1)',
        },
      }}
    >
      {children}
    </IconButton>
  );
};

const ResultCard = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  borderRadius: theme.spacing(2),
  backgroundColor: theme.palette.background.paper,
  boxShadow:
    theme.palette.mode === 'dark'
      ? '0 2px 8px rgba(0, 0, 0, 0.2)'
      : '0 2px 8px rgba(0, 0, 0, 0.05)',
  marginBottom: theme.spacing(3),
}));

const Sidebar: React.FC<SidebarProps> = ({ onCollapse }) => {
  const theme = useTheme(); // Make sure this is at the component level
  const [currentUser] = useLocalStorage('user', null);
  const dispatch = useDispatch();

  const currentProjectId = useSelector(
    (state: any) => state.project.selectedProjectId
  );
  const isProjectWindowOpen = useSelector(
    (state: any) => state.project.isProjectWindowOpen
  );
  const { data: files, isLoading: isFilesLoading } =
    useGetFilesForComplianceQuery(
      { userId: currentUser?.id, projectId: currentProjectId },
      { skip: !currentProjectId }
    );
  const [complianceReport] = useGenerateComplianceReportMutation();
  const [generatePDF] = useGenerateCompliancePDFMutation();
  const {
    data: previousReports = [],
    isLoading: isPreviousReportsLoading,
    error: previousReportsError,
  } = useGetPreviousComplianceReportsQuery(
    {
      userId: currentUser?.id,
      projectId: currentProjectId,
    },
    {
      skip: !currentProjectId,
    }
  );
  const [downloadPreviousComplianceReport] =
    useDownloadPreviousComplianceReportMutation();

  const [activeIndex, setActiveIndex] = useState<number | null>();
  const [selectedCompliance, setSelectedCompliance] = useState<any>(null);
  const [selectedFiles, setSelectedFiles] = useState<FileOption[]>([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [complianceResults, setComplianceResults] = useState<any>({
    files: [],
    timestamp: new Date().toLocaleString(),
  });
  const [openPreviousReports, setOpenPreviousReports] = useState(false);
  const [isFrameworkSelected, setIsFrameworkSelected] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [expandedSections, setExpandedSections] = useState<string[]>([
    'workspace',
  ]);
  const { trackMenuClick } = useAnalyticsEvents();

  const src =
    'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADIAAAAyCAYAAAAeP4ixAAAACXBIWXMAAAsTAAALEwEAmpwYAAADp0lEQVR4nO2ZaYhOURzGf/Z95wvJlqyRXfmCKCENSRpSZCkfkK0sI1k/KVL2kPANWUqyjA8UJqQYSxQzlomx74y5Onlund7uve/7Xq/7vlfz1G3ue8557j3PPf/znP85A1WIH+oBi4Ai4BPgZPkKhQ7A3RzovPM3QsxI3BH5EjAcqE/24IQVslDEy0Btsg8nrJBrIo4iN+CEFeJO7FbEXIiThDgE6BlQ3wAYDawCNgE7gXXATKDrP+hPKGJNoFhm0CihrhNwEPiexH0eAHMlOGtCZlv1ZywzWAH8UPkv4DywFpgGTAWWAYeA5xa/VKObFSHFqvumv0eA/bqv1Ii0DXh2dSAPuCrOT2BG1EJ6q7xc9y+ttiacpnhwNsg81nuE6EaJN2JGRSlkscr36Pcg4LXKyrRwJuKj6j/4vGu16t8CLaMSslflxn1cdAZuWaG1WV/bxXqJMa7lhWrAafG3RCXkmMqNtdqoBRRYbrUv4NleoWasvEL8plEIOaDyyT68wcB7tRnr08Yv1M6qPD+N/iSFH7FA5btTyNOMk3nBL9TmBYxmxoV01zz4AvTw4eaJ+xBonsY7h4l3IY3+JEUQ8aDq7gNdPCbuKYtvks8mKb6zmzjFafYnEEHExsAN1X8GdimuJ1quZiz5kbUVaJjCpO+v9kVRCUE50n6lIol51Dctbu2AxyorBOommfR5+n08SiEuTGgtBw4DF8X5atmzSSKfWalMjYBJv1Xt1mRDSOL8cO35u0LNNYhylW/z4ZoFtERt+maoP6GJHYGnFtcscNNVN9AKpZUeXJMpmLp7+iCZ6E8oYi8rPT+qlL5S13y1GaH5U5mQ7bax8rX8DPUnFNGkF2+s/Yk5gXG/ckVC3E+SQZhsdwzQzMrTzvmMRmRCrqjtaY8Tl/Ga/I5s2kz2JZZ139S9MYTWGepPKGILKwX327YOBd6p3SmtKbutd7zS3oZsCqktEY7PpspFH+1XHI1CR8uuCwNCKtLQmmXtRQ4r9tso/s06MkFrxBPrucaO51i2vCCD/fkr4vwUTk7cxfK2JfyBVd46F4SgjhTIfcrkZC+A69oaT9OmyYTRUuvExb1W5IqQdDEo4bS/IK5C3KOhcVpz6sRZSKpwqoTw5yt00AF0qc/eI+orbTjWpifbnXcyIcTdsfXPkf9cpQ1XxAliDkdXP2KOCgnxOv2IFUr+lxHZ8b/MkfaW9Z4EBsTVtQxGWifruXKFhjkt3K5NUUWchVSBCPEb85JyLgw1NtcAAAAASUVORK5CYII=';

  const location = useLocation();

  const toggleSection = (section: string) => {
    setExpandedSections((prev) =>
      prev.includes(section)
        ? prev.filter((s) => s !== section)
        : [...prev, section]
    );
  };

  const sidebarStructure = [
    {
      id: 'workspace',
      label: 'Workspace',
      icon: <BsPersonWorkspace className={styles.icon} />,
      children: [
        {
          label: 'Home',
          icon: <PiHouseLight size={22} className={styles.icon} />,
          path: '/',
        },
        {
          label: 'Library',
          icon: <PiBooksLight size={22} className={styles.icon} />,
          path: '/library',
        },
        {
          label: 'My Projects',
          icon: <PiFolderSimpleLight size={22} className={styles.icon} />,
          path: '/my-projects',
        },
        {
          label: 'Awaiting Actions',
          icon: (
            <PiClockCounterClockwiseLight size={22} className={styles.icon} />
          ),
          path: '/awaiting-actions',
        },
        {
          label: 'Sketchbooks',
          icon: <PiNotebookLight size={22} className={styles.icon} />,
          path: '/sketchbooklists',
        },
      ],
    },
    {
      id: 'resources',
      label: 'Resources',
      icon: <PiBookOpenLight className={styles.icon} />,
      children: [
        {
          label: 'Learning Center',
          icon: <PiGraduationCapLight className={styles.icon} />,
          path: '/learning',
        },
        {
          label: 'Documentation',
          icon: <PiBookOpenLight className={styles.icon} />,
          path: '/docs',
        },

        {
          label: 'Community',
          icon: <PiUsersLight className={styles.icon} />,
          path: '/community',
        },
      ],
    },
    {
      id: 'account',
      label: 'Account & Billing',
      icon: <PiUserLight className={styles.icon} />,
      children: [
        {
          label: 'Profile Settings',
          icon: <PiGearLight className={styles.icon} />,
          path: '/settings',
        },
        {
          label: 'Billing',
          icon: <PiCreditCardLight className={styles.icon} />,
          path: '/billing',
        },
        {
          label: 'Invoices',
          icon: <PiReceiptLight className={styles.icon} />,
          path: '/invoices',
        },
      ],
    },
  ];

  const socialLinks = [
    {
      icon: <PiFacebookLogoLight size={22} className={styles.icon} />,
      url: 'https://www.facebook.com/neuquip.official ',
      label: 'Facebook',
    },
    {
      icon: <PiInstagramLogoLight size={22} className={styles.icon} />,
      url: 'https://www.instagram.com/neuquip.official/',
      label: 'Instagram',
    },
    {
      icon: <PiLinkedinLogoLight size={22} className={styles.icon} />,
      url: 'https://www.linkedin.com/company/neuquip/',
      label: 'LinkedIn',
    },
    {
      icon: <PiYoutubeLogoLight size={22} className={styles.icon} />,
      url: ' https://www.youtube.com/@Neuquip',
      label: 'Youtube',
    },
  ];

  const complianceOptions = [
    { value: 'gdpr', label: 'GDPR' },
    { value: 'hipaa', label: 'HIPAA' },
    { value: 'ccpa', label: 'CCPA' },
    { value: 'iso27001', label: 'ISO 27001' },
    { value: 'pci_dss', label: 'PCI DSS' },
    { value: 'sox', label: 'Sarbanes-Oxley (SOX)' },
    { value: 'nist', label: 'NIST Cybersecurity Framework' },
    { value: 'ferpa', label: 'FERPA' },
    { value: 'glba', label: 'GLBA' },
    { value: 'coppa', label: 'COPPA' },
    { value: 'fisma', label: 'FISMA' },
    { value: 'cmmc', label: 'CMMC' },
    { value: 'fedramp', label: 'FedRAMP' },
    { value: 'pipeda', label: 'PIPEDA' },
    { value: 'lgpd', label: 'LGPD' },
    { value: 'pdpa', label: 'PDPA' },
    { value: 'shield', label: 'Privacy Shield' },
    { value: 'appi', label: 'APPI' },
  ];

  const fileOptions: FileOption[] =
    files?.map((file: any) => ({
      value: file.file_id,
      label: file.file_name,
    })) || [];

  const getScoreColor = (score: number) => {
    const isDarkMode =
      document.documentElement.getAttribute('data-theme') === 'dark';

    if (score >= 8) return isDarkMode ? 'rgba(30, 142, 62, 0.2)' : '#e6f4ea';
    if (score >= 6) return isDarkMode ? 'rgba(249, 168, 37, 0.2)' : '#fff8e1';
    return isDarkMode ? 'rgba(211, 47, 47, 0.2)' : '#fde9e9';
  };

  const getTextColor = (score: number) => {
    const isDarkMode =
      document.documentElement.getAttribute('data-theme') === 'dark';

    if (score >= 8) return isDarkMode ? '#4caf50' : '#1e8e3e';
    if (score >= 6) return isDarkMode ? '#ffb74d' : '#f9a825';
    return isDarkMode ? '#ef5350' : '#d32f2f';
  };

  const handleGenerate = async () => {
    try {
      // Validate required fields
      if (!selectedCompliance || selectedFiles.length === 0) {
        toast('Please select both a framework and at least one file', {
          icon: <span style={{ fontSize: '30px' }}>🛈</span>,
        });
        return;
      }

      setIsGenerating(true); // Start loading

      const formData = new FormData();
      formData.append('project_id', currentProjectId);
      formData.append(
        'file_ids',
        JSON.stringify(selectedFiles.map((file: any) => file.value))
      );
      formData.append('framework_type', selectedCompliance.value);
      formData.append('user_id', currentUser?.id);

      const response = await complianceReport({ formData }).unwrap();

      if (response) {
        const results = response.map((item: any) => ({
          fileName: item.file_name,
          score: item.average_score.toFixed(1),
          framework: item.framework_type,
          created_at: item.created_at,
        }));

        setComplianceResults({
          files: results,
          timestamp: new Date().toLocaleString(),
        });

        setOpenDialog(true);
      }
    } catch (error: any) {
      console.error('Error generating compliance report:', error);
      toast.error(error?.data?.detail, { duration: 10000 });
    } finally {
      setIsGenerating(false); // Stop loading regardless of success or failure
    }
  };

  const handleDownloadPDF = async () => {
    try {
      if (!selectedCompliance || selectedFiles.length === 0) {
        toast.error('Please select both a framework and at least one file');
        return;
      }

      setIsGenerating(true);

      const formData = new FormData();
      formData.append('project_id', currentProjectId);
      formData.append(
        'file_ids',
        JSON.stringify(selectedFiles.map((file: any) => file.value))
      );
      formData.append('framework_type', selectedCompliance.value);
      formData.append('user_id', currentUser?.id);
      formData.append('generate_pdf', 'true');

      const blob = await generatePDF({ formData }).unwrap();
      const url = window.URL.createObjectURL(blob);

      const link = document.createElement('a');
      link.href = url;
      link.download = `compliance-report-${new Date().toISOString().split('T')[0]}.pdf`;

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      window.URL.revokeObjectURL(url);

      toast.success('PDF downloaded successfully');
    } catch (error: any) {
      console.error('Error generating compliance report:', error);
      toast.error(error?.data?.detail);
    } finally {
      setIsGenerating(false);
    }
  };

  useEffect(() => {
    const currentPath = location.pathname;
    const activeItem = sidebarStructure.findIndex((section) =>
      section.children.some((item) => item.path === currentPath)
    );
    setActiveIndex(activeItem !== -1 ? activeItem : null);
  }, [location.pathname]);

  useEffect(() => {
    if (!isProjectWindowOpen) {
      // Reset all compliance-related states
      setSelectedCompliance(null);
      setSelectedFiles([]);
      setIsFrameworkSelected(false);
      setComplianceResults({
        files: [],
        timestamp: new Date().toLocaleString(),
      });
      setOpenDialog(false);
      setOpenPreviousReports(false);
    }
  }, [isProjectWindowOpen]);

  useEffect(() => {
    const savedResponseType = localStorage.getItem('responseType');
    if (savedResponseType) {
      dispatch(setResponseType(savedResponseType));
    }
  }, [dispatch]);

  // Add scroll to compliance functionality
  useEffect(() => {
    const handleScrollToCompliance = () => {
      setIsCollapsed(false);
      if (onCollapse) {
        onCollapse(false);
      }

      // Use setTimeout to ensure the sidebar is expanded before scrolling
      setTimeout(() => {
        const complianceSection = document.querySelector(
          `.${styles.compliance}`
        );
        if (complianceSection) {
          complianceSection.scrollIntoView({
            behavior: 'smooth',
            block: 'start',
          });
        }
      }, 100);
    };

    window.addEventListener('scrollToCompliance', handleScrollToCompliance);
    return () =>
      window.removeEventListener(
        'scrollToCompliance',
        handleScrollToCompliance
      );
  }, [onCollapse]);

  const complianceSelectStyles = {
    control: (baseStyles: any, state: any) => ({
      ...baseStyles,
      background:
        theme.palette.mode === 'dark'
          ? theme.palette.background.paper
          : '#f8f9fa',
      borderColor:
        theme.palette.mode === 'dark' ? theme.palette.divider : '#6c757d',
      '&:hover': {
        borderColor: theme.palette.primary.main,
      },
      boxShadow: 'none',
      borderRadius: '4px',
      padding: '2px',
    }),
    menu: (baseStyles: any) => ({
      ...baseStyles,
      background:
        theme.palette.mode === 'dark'
          ? theme.palette.background.paper
          : '#ffffff',
      zIndex: 9999,
    }),
    menuList: (baseStyles: any) => ({
      ...baseStyles,
      padding: '4px',
    }),
    option: (baseStyles: any, state: any) => ({
      ...baseStyles,
      backgroundColor: state.isSelected
        ? theme.palette.mode === 'dark'
          ? theme.palette.primary.dark
          : theme.palette.primary.light
        : state.isFocused
          ? theme.palette.mode === 'dark'
            ? alpha(theme.palette.primary.main, 0.2)
            : alpha(theme.palette.primary.main, 0.1)
          : 'transparent',
      color:
        theme.palette.mode === 'dark'
          ? theme.palette.text.primary
          : state.isSelected
            ? theme.palette.primary.contrastText
            : theme.palette.text.primary,
      '&:hover': {
        backgroundColor:
          theme.palette.mode === 'dark'
            ? alpha(theme.palette.primary.main, 0.2)
            : alpha(theme.palette.primary.main, 0.1),
      },
      cursor: 'pointer',
    }),
    singleValue: (baseStyles: any) => ({
      ...baseStyles,
      color:
        theme.palette.mode === 'dark' ? theme.palette.text.primary : '#0E2F51',
    }),
    input: (baseStyles: any) => ({
      ...baseStyles,
      color:
        theme.palette.mode === 'dark' ? theme.palette.text.primary : '#0E2F51',
    }),
    placeholder: (baseStyles: any) => ({
      ...baseStyles,
      color:
        theme.palette.mode === 'dark'
          ? theme.palette.text.secondary
          : '#6c757d',
    }),
  };

  const handleSelectAll = () => {
    if (selectedFiles.length === fileOptions.length) {
      setSelectedFiles([]);
    } else {
      setSelectedFiles(fileOptions);
    }
  };

  const handleDownloadPreviousReport = async () => {
    try {
      const blob = await downloadPreviousComplianceReport({
        userId: currentUser?.id,
        projectId: currentProjectId,
      }).unwrap();

      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `compliance-report-${new Date().toISOString().split('T')[0]}.pdf`;

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      window.URL.revokeObjectURL(url);
      toast.success('Previous report downloaded successfully');
    } catch (error) {
      console.error('Error downloading previous report:', error);
      toast.error('Failed to download previous report');
    }
  };

  // Styled components for compliance indicators with dark mode support
  const IndicatorContainer = styled(Box)(({ theme }) => ({
    display: 'flex',
    gap: '20px',
    marginBottom: '16px',
    justifyContent: 'center',
    flexWrap: 'wrap',
    color: theme.palette.text.primary,
  }));

  const Indicator = styled(Box)(() => ({
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
  }));

  // Create a styled component for the color box
  const StyledColorBox = styled(Box)({
    width: '20px',
    height: '20px',
    borderRadius: '4px',
  });

  const ComplianceIndicators = () => {
    // Get current theme mode
    const isDarkMode =
      document.documentElement.getAttribute('data-theme') === 'dark';

    // Define colors based on theme
    const goodBg = isDarkMode ? 'rgba(30, 142, 62, 0.2)' : '#e6f4ea';
    const goodBorder = isDarkMode ? '#2e7d32' : '#1e8e3e';

    const moderateBg = isDarkMode ? 'rgba(249, 168, 37, 0.2)' : '#fff8e1';
    const moderateBorder = isDarkMode ? '#f9a825' : '#f9a825';

    const needsImprovementBg = isDarkMode
      ? 'rgba(211, 47, 47, 0.2)'
      : '#fde9e9';
    const needsImprovementBorder = isDarkMode ? '#d32f2f' : '#d32f2f';

    return (
      <IndicatorContainer>
        <Indicator>
          <StyledColorBox
            sx={{
              backgroundColor: goodBg,
              border: `1px solid ${goodBorder}`,
            }}
          />
          <Typography variant="body2">Good (8-10)</Typography>
        </Indicator>
        <Indicator>
          <StyledColorBox
            sx={{
              backgroundColor: moderateBg,
              border: `1px solid ${moderateBorder}`,
            }}
          />
          <Typography variant="body2">Moderate (6-7.9)</Typography>
        </Indicator>
        <Indicator>
          <StyledColorBox
            sx={{
              backgroundColor: needsImprovementBg,
              border: `1px solid ${needsImprovementBorder}`,
            }}
          />
          <Typography variant="body2">Needs Improvement (0-5.9)</Typography>
        </Indicator>
      </IndicatorContainer>
    );
  };

  const toggleSidebar = () => {
    const newCollapsedState = !isCollapsed;
    setIsCollapsed(newCollapsedState);
    onCollapse?.(newCollapsedState);
  };

  return (
    <div
      id="sidebar"
      className={`${styles.sidebar} ${isCollapsed ? styles.collapsed : ''}`}
    >
      <div className={styles.stickyTopContainer}>
        <div className={styles.collapseIconWrapper}>
          <CustomIconButton
            icon={
              isCollapsed ? (
                <GoSidebarCollapse size={22} />
              ) : (
                <GoSidebarExpand size={22} />
              )
            }
            onClick={toggleSidebar}
            type="secondary"
            size="medium"
            title={isCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}
            tooltipPosition="right"
          />
        </div>
      </div>

      <div className={styles.sidebarContent}>
        <div className={styles.sidebarList}>
          {isCollapsed ? (
            // When collapsed, show sections with dividers
            <>
              {sidebarStructure.map((section, index) => (
                <div key={section.id} className={styles.collapsedSection}>
                  {section.children.map((item) => (
                    <div
                      id={`sidebar-${item.path.replace('/', '')}`}
                      data-driver={`sidebar-${item.path.replace('/', '')}`}
                      key={item.path}
                      className={styles.iconButtonWrapper}
                    >
                      <NavLink
                        to={item.path}
                        className={({ isActive }) =>
                          `${styles.sidebarItem} ${isActive ? styles.active : ''}`
                        }
                        onClick={() => trackMenuClick(item.label)}
                      >
                        <CustomIconButton
                          icon={item.icon}
                          type="secondary"
                          size="medium"
                          title={item.label}
                          tooltipPosition="right"
                        />
                      </NavLink>
                    </div>
                  ))}
                  {index < sidebarStructure.length - 1 && (
                    <div className={styles.sidebarHr} />
                  )}
                </div>
              ))}
            </>
          ) : (
            // When expanded, show all items in a single level with section headers
            sidebarStructure.map((section, sectionIndex) => (
              <div key={section.id} className={styles.sidebarSection}>
                <div className={styles.sectionHeader}>
                  {section.icon}
                  <span className={styles.label}>{section.label}</span>
                </div>
                <div className={styles.sectionItems}>
                  {section.children.map((item) => (
                    <NavLink
                      key={item.path}
                      to={item.path}
                      className={({ isActive }) =>
                        `${styles.sidebarItem} ${isActive ? styles.active : ''}`
                      }
                      onClick={() => trackMenuClick(item.label)}
                    >
                      {item.icon}
                      <span className={styles.label}>{item.label}</span>
                    </NavLink>
                  ))}
                </div>
                {sectionIndex < sidebarStructure.length - 1 && (
                  <div className={styles.sidebarHr} />
                )}
              </div>
            ))
          )}
        </div>

        {!isCollapsed && currentProjectId && (
          <>
            <hr className={styles.sidebarHr} />
            <div id="complianceSection" className={styles.compliance}>
              <div className={styles.complianceHeader}>
                <PiShieldCheckLight size={24} />
                <h3>Compliance Check</h3>
              </div>

              <div className={styles.complianceControls}>
                <Select
                  className={styles.select}
                  placeholder="Select Framework"
                  options={complianceOptions}
                  value={selectedCompliance}
                  onChange={(newValue: any) => {
                    setIsFrameworkSelected(true);
                    setSelectedCompliance(newValue);
                  }}
                  styles={complianceSelectStyles}
                />
                <div className={styles.multiSelectContainer}>
                  <Select
                    isDisabled={!isFrameworkSelected || isGenerating}
                    className={styles.select}
                    placeholder="Select Files"
                    options={fileOptions}
                    isMulti
                    value={selectedFiles}
                    onChange={(newValue: any) => setSelectedFiles(newValue)}
                    styles={complianceSelectStyles}
                    isSearchable={false}
                  />
                </div>
                <button
                  disabled={!isFrameworkSelected || isGenerating}
                  className={styles.selectAllButton}
                  onClick={handleSelectAll}
                  data-tooltip={
                    selectedFiles.length === fileOptions.length
                      ? 'Deselect All'
                      : 'Select All'
                  }
                >
                  {selectedFiles.length === fileOptions.length ? (
                    <MdDeselect size={20} />
                  ) : (
                    <IoCheckmarkDoneCircleOutline size={20} />
                  )}
                  {selectedFiles.length === fileOptions.length
                    ? 'Deselect All'
                    : 'Select All'}
                </button>
                <CustomButton
                  type="primary"
                  label={isGenerating ? 'Generating...' : 'Compliance Check'}
                  onClick={handleGenerate}
                  disabled={isGenerating}
                  leftIcon={<IoShieldCheckmarkOutline size={20} />}
                />
                {!isPreviousReportsLoading &&
                  previousReports &&
                  previousReports.length > 0 &&
                  !previousReportsError && (
                    <CustomButton
                      type="secondary"
                      label="Previous Checks"
                      onClick={() => setOpenPreviousReports(true)}
                      leftIcon={<BiTime size={20} />}
                    />
                  )}
              </div>
            </div>
          </>
        )}

        <div className={styles.socialLinks}>
          {socialLinks.map((link, index) => (
            <a
              key={index}
              href={link.url}
              target="_blank"
              rel="noopener noreferrer"
              className={styles.socialLink}
            >
              <CustomIconButton
                icon={link.icon}
                type="secondary"
                size="small"
                title={link.label}
                tooltipPosition="right"
              />
            </a>
          ))}
        </div>
      </div>

      <StyledDialog
        open={openDialog}
        onClose={() => setOpenDialog(false)}
        maxWidth="md"
        fullWidth
      >
        <StyledDialogTitle>
          <Typography variant="h6">Compliance Check Results</Typography>
          <CloseButton
            onClick={() => setOpenDialog(false)}
            size="small"
            aria-label="close"
          >
            <IoCloseOutline size={24} color="inherit" />
          </CloseButton>
        </StyledDialogTitle>
        <StyledDialogContent>
          <ComplianceIndicators />
          <ResultCard elevation={1}>
            <div className={styles.filesResults}>
              {complianceResults.files.map((file: any, index: any) => {
                return (
                  <Paper
                    key={index}
                    elevation={0}
                    sx={{
                      p: 2,
                      borderRadius: 2,
                      backgroundColor: getScoreColor(Number(file.score)),
                      border: `1px solid ${getTextColor(Number(file.score))}`,
                      transition: 'transform 0.2s ease',
                      '&:hover': {
                        transform: 'translateY(-2px)',
                      },
                    }}
                  >
                    <Typography variant="h6" gutterBottom>
                      Compliance Score:{' '}
                      <Typography
                        component="span"
                        variant="h6"
                        sx={{ color: getTextColor(Number(file.score)) }}
                      >
                        {file.score}/10
                      </Typography>
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      {file.fileName}
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      {file.framework}
                    </Typography>
                  </Paper>
                );
              })}
            </div>
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                mt: 3,
                pt: 2,
                borderTop: '1px solid',
                borderColor: 'divider',
              }}
            >
              <Typography variant="caption" color="textSecondary">
                Analyzed on: {complianceResults.timestamp}
              </Typography>
              <CustomButton
                style={{ width: '30%' }}
                type="secondary"
                label="Download Complete Report"
                onClick={() => handleDownloadPDF()}
                leftIcon={<IoDownloadOutline size={20} />}
              />
            </Box>
          </ResultCard>
        </StyledDialogContent>
      </StyledDialog>
      <StyledDialog
        open={openPreviousReports}
        onClose={() => setOpenPreviousReports(false)}
        maxWidth="md"
        fullWidth
      >
        <StyledDialogTitle>
          <Typography variant="h6">Previous Compliance Checks</Typography>
          <CloseButton
            onClick={() => setOpenPreviousReports(false)}
            size="small"
            aria-label="close"
          >
            <IoCloseOutline size={24} color="inherit" />
          </CloseButton>
        </StyledDialogTitle>
        <StyledDialogContent>
          <ResultCard elevation={1}>
            <div className={styles.filesResults}>
              {previousReports.map((file: any, index: any) => {
                return (
                  <Paper
                    key={index}
                    elevation={0}
                    sx={{
                      p: 2,
                      borderRadius: 2,
                      backgroundColor: getScoreColor(
                        Number(file.average_score)
                      ),
                      border: `1px solid ${getTextColor(Number(file.average_score))}`,
                      transition: 'transform 0.2s ease',
                      '&:hover': {
                        transform: 'translateY(-2px)',
                      },
                    }}
                  >
                    <Typography variant="h6" gutterBottom>
                      Compliance Score:{' '}
                      <Typography
                        component="span"
                        variant="h6"
                        sx={{ color: getTextColor(Number(file.average_score)) }}
                      >
                        {file.average_score}/10
                      </Typography>
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      {file.file_name}
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      {file.framework_type.toUpperCase()}
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      {formatTimestamp(file.created_at)}
                    </Typography>
                  </Paper>
                );
              })}
            </div>
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                mt: 3,
                pt: 2,
                borderTop: '1px solid',
                borderColor: 'divider',
              }}
            >
              <ComplianceIndicators />
              <CustomButton
                style={{ width: '30%' }}
                type="secondary"
                label="Download Complete Report"
                onClick={() => handleDownloadPreviousReport()}
                leftIcon={<IoDownloadOutline size={20} />}
              />
            </Box>
          </ResultCard>
        </StyledDialogContent>
      </StyledDialog>
    </div>
  );
};

export default Sidebar;

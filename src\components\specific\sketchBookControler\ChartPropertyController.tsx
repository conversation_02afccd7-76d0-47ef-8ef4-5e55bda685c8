import { useState, useEffect, useCallback, useRef } from 'react';
import styles from './ChartPropertyController.module.css';
import { AgGridReact } from 'ag-grid-react';
import 'ag-grid-community/styles/ag-grid.css';
import 'ag-grid-community/styles/ag-theme-alpine.css';
import { ColDef } from 'ag-grid-community';
import { read, utils } from 'xlsx';
import toast from 'react-hot-toast';
import 'react-datepicker/dist/react-datepicker.css';
import {
  useUpdateCustomChartsMutation,
  useUpdateSketchbookMutation,
} from '../../../services/sketchbookServices';
import { exportToExcel } from './utils/chartPropertyControlerUtils';
import { GanttControls } from './components/GanttControls';
import { TextControls } from './components/TextControls';
import { DataSourceSection } from './components/DataSourceSection';
import { BurndownControls } from './components/BurndownControls';
import { TimelineControls } from './components/TimelineControls';
import { GaugeControls } from './components/GaugeControls';
import { ColorCellRenderer } from './components/ColorCellRenderer';
import { ImageControls } from './components/ImageControls';
import { ChartSettingsControls } from './components/ChartSettingsControls';

import { chartPayloadHandler } from './utils/chartPayloadHandler';
import IconButton from '../../common/button/IconButton';
import { FaFileExcel, FaPlus, FaTrash } from 'react-icons/fa';
import { IoDocumentText } from 'react-icons/io5';

interface DataSource {
  id: string;
  name: string;
  data: any[][];
  headers: string[];
}

interface ChartPropertyControllerProps {
  sketchbookId: string;
  selectedChart: any;
  activePage: string;
  onChartUpdate: (chart: any) => void;
}

const ChartPropertyController: React.FC<ChartPropertyControllerProps> = ({
  sketchbookId,
  selectedChart,
  activePage,
  onChartUpdate,
}) => {
  const [colorPickerVisible, setColorPickerVisible] = useState<string | null>(
    null
  );
  const [dataSources, setDataSources] = useState<DataSource[]>([]);
  const [selectedDataSource, setSelectedDataSource] = useState<string | null>(
    null
  );

  const [updateCustomCharts] = useUpdateCustomChartsMutation();

  const gridRef = useRef<any>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        colorPickerVisible &&
        !(event.target as Element).closest('.react-colorful') &&
        !(event.target as Element).closest('.colorPreview')
      ) {
        setColorPickerVisible(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [colorPickerVisible]);

  const handleFileUpload = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (!file) return;

    try {
      const data: any = await file.arrayBuffer();
      const workbook = read(data);
      const worksheet = workbook.Sheets[workbook.SheetNames[0]];
      const jsonData = utils.sheet_to_json(worksheet, { header: 1 });

      const headers = jsonData[0] as string[];
      const filteredData = (jsonData.slice(1) as any[][]).filter((row: any[]) =>
        row.some((cell: any) => cell !== undefined && cell !== '')
      );

      const newDataSource: DataSource = {
        id: `ds-${Date.now()}`,
        name: file.name,
        data: filteredData as any[][],
        headers,
      };

      setDataSources((prev) => [...prev, newDataSource]);
      toast.success('Excel file uploaded successfully');
    } catch (error) {
      console.error('Error reading Excel file:', error);
      toast.error('Failed to read Excel file');
    }
  };

  const applyDataSource = async () => {
    if (!selectedDataSource || !selectedChart) return;

    const dataSource = dataSources.find((ds) => ds.id === selectedDataSource);
    if (!dataSource) return;

    // Create a deep copy of the chart
    const updatedChart = JSON.parse(JSON.stringify(selectedChart));

    // Create new data object
    updatedChart.data = {
      labels: dataSource.data.map((row) => row[0]?.toString() || ''),
      datasets: [
        {
          ...updatedChart.data.datasets[0],
          data: dataSource.data.map((row) => {
            if (selectedChart.type === 'bubble') {
              return {
                x: Number(row[1]) || 0,
                y: Number(row[2]) || 0,
                r: Number(row[3]) || 0,
              };
            } else if (selectedChart.type === 'scatter') {
              return {
                x: Number(row[1]) || 0,
                y: Number(row[2]) || 0,
              };
            } else {
              return Number(row[1]) || 0;
            }
          }),
        },
      ],
    };

    // Update colors if they exist in the Excel data
    if (
      dataSource.headers.includes('BorderColor') &&
      dataSource.headers.includes('FillColor')
    ) {
      const borderColorIndex = dataSource.headers.indexOf('BorderColor');
      const fillColorIndex = dataSource.headers.indexOf('FillColor');

      !['bubble', 'scatter'].includes(updatedChart.type) &&
        (updatedChart.data.datasets[0].borderColor = dataSource.data.map(
          (row) =>
            row[borderColorIndex] !== undefined && row[borderColorIndex] !== ''
              ? row[borderColorIndex]
              : updatedChart?.data?.datasets[0]?.borderColor[0] || '#FF1122' // Provide a default color
        ));

      updatedChart.data.datasets[0].backgroundColor = dataSource.data.map(
        (row) =>
          row[fillColorIndex] !== undefined && row[fillColorIndex] !== ''
            ? row[fillColorIndex]
            : updatedChart?.data?.datasets[0]?.backgroundColor[0] || '#FF4411' // Provide a default color
      );
    }

    const updatedChartPayload = chartPayloadHandler(
      updatedChart,
      selectedChart.type,
      sketchbookId,
      activePage
    );
    const response = await updateCustomCharts({
      id: selectedChart.id,
      payload: updatedChartPayload,
      chartType:
        selectedChart.options.indexAxis === 'y'
          ? 'horizontal'
          : selectedChart.type,
    }).unwrap();

    onChartUpdate(updatedChart);
    toast.success('Data source applied to chart');
  };

  const renderExportButtons = () => {
    return (
      <div className={styles.exportButtons}>
        <IconButton
          icon={<FaFileExcel />}
          onClick={() => exportToExcel(false, selectedChart, getRowData)}
          type="secondary"
          title="Export Data to Excel"
          tooltipPosition="top"
        />
        <IconButton
          icon={<IoDocumentText />}
          onClick={() => exportToExcel(true, selectedChart, getRowData)}
          type="secondary"
          title="Export Format to Excel"
          tooltipPosition="top"
        />
      </div>
    );
  };

  if (!selectedChart) {
    return (
      <div className={styles.colorController}>
        <div className={styles.section}>
          <div className={styles.heading}>No chart selected</div>
        </div>
      </div>
    );
  }

  const datasets = selectedChart.data.datasets;

  const getColumnDefs = (): ColDef[] => {
    const baseColumns: ColDef[] = [
      {
        width: 40,
        headerName: '',
        field: 'selection',
      },
      {
        field: 'index',
        headerName: '#',
        width: 70,
        editable: false,
        sortable: true,
        suppressSizeToFit: true,
        resizable: false,
      },
      {
        field: 'label',
        headerName: 'Label',
        editable: true,
        sortable: true,
        minWidth: 120,
        flex: 1,
      },
    ];

    // Add value columns based on chart type
    if (selectedChart.type === 'bubble') {
      baseColumns.push(
        {
          field: 'x',
          headerName: 'X',
          editable: true,
          type: 'numericColumn',
          minWidth: 100,
          flex: 1,
        },
        {
          field: 'y',
          headerName: 'Y',
          editable: true,
          type: 'numericColumn',
          minWidth: 100,
          flex: 1,
        },
        {
          field: 'r',
          headerName: 'Radius',
          editable: true,
          type: 'numericColumn',
          minWidth: 100,
          flex: 1,
        }
      );
    } else if (selectedChart.type === 'scatter') {
      baseColumns.push(
        {
          field: 'x',
          headerName: 'X',
          editable: true,
          type: 'numericColumn',
          minWidth: 100,
          flex: 1,
        },
        {
          field: 'y',
          headerName: 'Y',
          editable: true,
          type: 'numericColumn',
          minWidth: 100,
          flex: 1,
        }
      );
    } else if (
      selectedChart.type === 'line' &&
      datasets[0]?.data[0]?.x instanceof Date
    ) {
      baseColumns.push(
        {
          field: 'x',
          headerName: 'Date',
          editable: true,
          cellEditor: 'datePicker',
          valueFormatter: (params: any) =>
            params.value ? new Date(params.value).toLocaleString() : '',
          minWidth: 150,
          flex: 1,
        },
        {
          field: 'y',
          headerName: 'Value',
          editable: true,
          type: 'numericColumn',
          minWidth: 100,
          flex: 1,
        }
      );
    } else {
      baseColumns.push({
        field: 'value',
        headerName: 'Value',
        editable: true,
        minWidth: 100,
        flex: 1,
        valueFormatter: (params: any) => {
          if (!params.value && params.value !== 0) return '';
          return params.value.toString();
        },
      });
    }

    // Add color columns
    baseColumns.push(
      {
        field: 'backgroundColor',
        headerName: 'Fill Color',
        editable: true,
        cellRenderer: ColorCellRenderer,
        minWidth: 140,
        flex: 1,
      },
      {
        field: 'borderColor',
        headerName: 'Border Color',
        editable: true,
        cellRenderer: ColorCellRenderer,
        minWidth: 140,
        flex: 1,
      }
    );

    return baseColumns;
  };

  // Remove the rowData state and use direct data from selectedChart
  const getRowData = useCallback(() => {
    if (!selectedChart?.data?.datasets) return [];

    const datasets = selectedChart.data.datasets;
    const labels = selectedChart.data.labels || [];

    return datasets.flatMap((dataset: any, datasetIndex: number) =>
      dataset.data.map((point: any, index: number) => {
        // Helper function to get color based on chart type
        const getColor = (colorProp: 'backgroundColor' | 'borderColor') => {
          const color = dataset[colorProp];

          // Chart types where both colors are arrays
          if (['polarArea'].includes(selectedChart.type)) {
            return Array.isArray(color) ? color[index] : color;
          }

          // Chart types where backgroundColor is array and borderColor is string
          if (
            [
              'bar',
              'radar',
              'area',
              'horizontalBar',
              'line',
              'pie',
              'doughnut',
            ].includes(selectedChart.type)
          ) {
            if (colorProp === 'backgroundColor') {
              return Array.isArray(color) ? color[index] : color;
            } else {
              // borderColor
              return color; // Return as is (string)
            }
          }

          // Chart types with only backgroundColor as array
          if (['scatter', 'bubble'].includes(selectedChart.type)) {
            if (colorProp === 'backgroundColor') {
              return Array.isArray(color) ? color[index] : color;
            }
            return undefined; // No borderColor for scatter and bubble
          }

          // Default fallback
          return color;
        };

        const baseRow = {
          index: index + 1,
          label: labels[index] || `Point ${index + 1}`,
          datasetIndex,
          valueIndex: index,
          backgroundColor: getColor('backgroundColor'),
          borderColor: getColor('borderColor'),
        };

        if (selectedChart.type === 'bubble') {
          return {
            ...baseRow,
            x: point.x,
            y: point.y,
            r: point.r,
          };
        } else if (selectedChart.type === 'scatter') {
          return {
            ...baseRow,
            x: point.x,
            y: point.y,
          };
        } else if (selectedChart.type === 'line' && point.x instanceof Date) {
          return {
            ...baseRow,
            x: point.x,
            y: point.y,
          };
        } else {
          return {
            ...baseRow,
            value: point,
          };
        }
      })
    );
  }, [selectedChart]);

  const onCellValueChanged = useCallback(
    async (params: any) => {
      const { data, colDef, newValue } = params;
      const { datasetIndex, valueIndex } = data;

      // Store current scroll position
      const gridApi = gridRef.current?.api;
      const scrollPosition = gridApi?.getVerticalPixelRange();

      // Create a deep copy of the chart
      const updatedChart = JSON.parse(JSON.stringify(selectedChart));

      // Store the current value format with the chart
      updatedChart.valueFormat = selectedChart.valueFormat;

      switch (colDef.field) {
        case 'label':
          updatedChart.data.labels[valueIndex] = newValue;
          break;

        case 'backgroundColor':
        case 'borderColor':
          const isColorArray =
            ['polarArea'].includes(selectedChart.type) ||
            (colDef.field === 'backgroundColor' &&
              [
                'bar',
                'radar',
                'area',
                'horizontalBar',
                'line',
                'pie',
                'doughnut',
                'scatter',
                'bubble',
              ].includes(selectedChart.type));

          if (isColorArray) {
            // Ensure the color array exists
            if (
              !Array.isArray(
                updatedChart.data.datasets[datasetIndex][colDef.field]
              )
            ) {
              updatedChart.data.datasets[datasetIndex][colDef.field] = [];
            }
            updatedChart.data.datasets[datasetIndex][colDef.field][valueIndex] =
              newValue;
          } else {
            // For non-array colors (like borderColor in most charts)
            updatedChart.data.datasets[datasetIndex][colDef.field] = newValue;
          }
          break;

        case 'x':
        case 'y':
        case 'r':
          if (
            selectedChart.type === 'bubble' ||
            selectedChart.type === 'scatter'
          ) {
            updatedChart.data.datasets[datasetIndex].data[valueIndex] = {
              ...updatedChart.data.datasets[datasetIndex].data[valueIndex],
              [colDef.field]:
                colDef.field === 'x' && selectedChart.type === 'line'
                  ? new Date(newValue)
                  : Number(newValue),
            };
          }
          break;

        case 'value':
          // Simplify the value handling
          updatedChart.data.datasets[datasetIndex].data[valueIndex] =
            typeof newValue === 'string'
              ? parseFloat(newValue) || newValue
              : newValue;
          break;
      }
      const updatedChartPayload = chartPayloadHandler(
        updatedChart,
        selectedChart.type,
        sketchbookId,
        activePage
      );
      await updateCustomCharts({
        id: selectedChart.id,
        payload: updatedChartPayload,
        chartType:
          selectedChart.options.indexAxis === 'y'
            ? 'horizontal'
            : selectedChart.type,
      }).unwrap();

      onChartUpdate(updatedChart);

      // Restore scroll position after update
      if (scrollPosition) {
        requestAnimationFrame(() => {
          gridApi?.ensureIndexVisible(data.index - 1, 'middle');
        });
      }
    },
    [selectedChart, onChartUpdate]
  );

  const deleteSelectedDataPoints = async () => {
    const gridApi = gridRef.current?.api;
    const selectedRows = gridApi.getSelectedRows();

    if (selectedRows.length === 0) {
      toast.error('Please select rows to delete');
      return;
    }

    // Create a deep copy of the chart
    const updatedChart = JSON.parse(JSON.stringify(selectedChart));

    // Store the current value format with the chart
    updatedChart.valueFormat = selectedChart.valueFormat;

    // Get the indices to remove, sorted in descending order to avoid index shifting issues
    const rowIndices = selectedRows
      .map((row: any) => row.valueIndex)
      .sort((a: number, b: number) => b - a);

    // Remove the data points from the chart
    rowIndices.forEach((index: number) => {
      // Remove from labels
      updatedChart.data.labels.splice(index, 1);

      // Remove from each dataset's data
      updatedChart.data.datasets.forEach((dataset: any) => {
        dataset.data.splice(index, 1);

        // If backgroundColor is an array, remove the corresponding color
        if (Array.isArray(dataset.backgroundColor)) {
          dataset.backgroundColor.splice(index, 1);
        }

        // If borderColor is an array, remove the corresponding color
        if (Array.isArray(dataset.borderColor)) {
          dataset.borderColor.splice(index, 1);
        }
      });
    });

    try {
      const updatedChartPayload = chartPayloadHandler(
        updatedChart,
        selectedChart.type,
        sketchbookId,
        activePage
      );

      await updateCustomCharts({
        id: selectedChart.id,
        payload: updatedChartPayload,
        chartType:
          selectedChart.options.indexAxis === 'y'
            ? 'horizontal'
            : selectedChart.type,
      }).unwrap();

      onChartUpdate(updatedChart);
      toast.success(`${selectedRows.length} data point(s) removed`);
    } catch (error) {
      console.error('Error removing data points:', error);
      toast.error('Failed to remove data points');
    }
  };

  const addNewDataPoint = async () => {
    const updatedChart = {
      ...selectedChart,
      valueFormat: selectedChart.valueFormat, // Store the current value format with the chart
      data: {
        ...selectedChart.data,
        labels: [
          ...selectedChart.data.labels,
          `Point ${selectedChart.data.labels.length + 1}`,
        ],
        datasets: selectedChart.data.datasets.map((dataset: any) => ({
          ...dataset,
          data: [...dataset.data],
          backgroundColor: Array.isArray(dataset.backgroundColor)
            ? [...dataset.backgroundColor, dataset.backgroundColor[0]]
            : dataset.backgroundColor,
          borderColor: Array.isArray(dataset.borderColor)
            ? [...dataset.borderColor, dataset.borderColor[0]]
            : dataset.borderColor,
        })),
      },
    };

    // Add new data point based on chart type
    updatedChart.data.datasets.forEach((dataset: any) => {
      if (selectedChart.type === 'bubble') {
        dataset.data.push({ x: 0, y: 0, r: 0 });
      } else if (selectedChart.type === 'scatter') {
        dataset.data.push({ x: 0, y: 0 });
      } else if (
        selectedChart.type === 'line' &&
        dataset.data[0]?.x instanceof Date
      ) {
        dataset.data.push({ x: new Date(), y: 0 });
      } else {
        dataset.data.push(0);
      }
    });

    const updatedChartPayload = chartPayloadHandler(
      updatedChart,
      selectedChart.type,
      sketchbookId,
      activePage
    );

    await updateCustomCharts({
      id: selectedChart.id,
      payload: updatedChartPayload,
      chartType:
        selectedChart.options.indexAxis === 'y'
          ? 'horizontal'
          : selectedChart.type,
    }).unwrap();

    onChartUpdate(updatedChart);
  };

  return (
    <div className={styles.colorController}>
      {selectedChart.type === 'image' ||
      selectedChart.chartType !== 'custom-chart' ? (
        <ImageControls /> // showing message for image and AI chat items
      ) : selectedChart.type === 'gantt' ? (
        <GanttControls
          sketchbookId={sketchbookId}
          activePage={activePage}
          selectedChart={selectedChart}
          onChartUpdate={onChartUpdate}
        />
      ) : selectedChart.type === 'textarea' ? (
        <TextControls
          sketchbookId={sketchbookId}
          activePage={activePage}
          selectedChart={selectedChart}
          onChartUpdate={onChartUpdate}
        />
      ) : selectedChart.type === 'gauge' ? (
        <GaugeControls
          sketchbookId={sketchbookId}
          activePage={activePage}
          selectedChart={selectedChart}
          onChartUpdate={onChartUpdate}
        />
      ) : (selectedChart.type === 'line' && selectedChart.data.isBurndown) ||
        selectedChart.type === 'burndown' ? (
        <BurndownControls
          sketchbookId={sketchbookId}
          activePage={activePage}
          selectedChart={selectedChart}
          onChartUpdate={onChartUpdate}
        />
      ) : (selectedChart.type === 'line' &&
          selectedChart.data.datasets?.[0]?.isTimeline) ||
        selectedChart.type === 'timeline' ? (
        <TimelineControls
          selectedChart={selectedChart}
          onChartUpdate={onChartUpdate}
          sketchbookId={sketchbookId}
          activePage={activePage}
        />
      ) : (
        <>
          {[
            'bar',
            'line',
            'area',
            'pie',
            'doughnut',
            'polarArea',
            'radar',
            'bubble',
            'scatter',
            'horizontalBar',
          ].includes(selectedChart.type) && (
            <ChartSettingsControls
              sketchbookId={sketchbookId}
              activePage={activePage}
              selectedChart={selectedChart}
              onChartUpdate={onChartUpdate}
            />
          )}
          <DataSourceSection
            dataSources={dataSources}
            selectedDataSource={selectedDataSource || ''}
            setSelectedDataSource={setSelectedDataSource}
            handleFileUpload={handleFileUpload}
            applyDataSource={applyDataSource}
          />
          <div className={styles.section}>
            <div className={styles.heading}>Chart Data</div>
            <div
              className="ag-theme-alpine"
              style={{
                width: '100%',
                height: '400px',
                overflow: 'auto',
              }}
            >
              <AgGridReact
                ref={gridRef}
                columnDefs={getColumnDefs()}
                rowData={getRowData()}
                onCellValueChanged={onCellValueChanged}
                rowSelection="multiple"
                defaultColDef={{
                  sortable: true,
                  filter: true,
                  resizable: true,
                  flex: 1,
                  minWidth: 100,
                }}
                domLayout="autoHeight"
              />
            </div>
            <div className={styles.buttonContainer}>
              <IconButton
                icon={<FaPlus />}
                onClick={addNewDataPoint}
                type="primary"
                title="Add Data Point"
                tooltipPosition="top"
              />
              <IconButton
                icon={<FaTrash />}
                onClick={deleteSelectedDataPoints}
                type="secondary"
                title="Remove Selected Data Points"
                tooltipPosition="top"
              />
              {renderExportButtons()}
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default ChartPropertyController;

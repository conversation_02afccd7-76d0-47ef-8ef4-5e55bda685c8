import SpeechRecognition from 'react-speech-recognition';
import IconButton from '../common/button/IconButton';
import { IoMic, IoMicOff, IoMicOffOutline } from 'react-icons/io5';
import { useEffect, useState } from 'react';
import toast from 'react-hot-toast';
import {
  checkSpeechRecognitionSupport,
  checkSecureContext,
  checkMicrophonePermission,
  getSpeechRecognitionErrorMessage,
} from '../../utils/speechRecognitionUtils';

interface VoiceInputProps {
  isListening: boolean;
  setListening: (listening: boolean) => void;
}

const VoiceInput = ({ isListening, setListening }: VoiceInputProps) => {
  const [isSupported, setIsSupported] = useState(true);
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);

  // Check browser support and HTTPS requirement
  useEffect(() => {
    const checkSupport = async () => {
      const speechRecognitionSupported = checkSpeechRecognitionSupport();
      const isSecureContext = checkSecureContext();

      setIsSupported(speechRecognitionSupported && isSecureContext);

      // Log diagnostics in development

      if (!speechRecognitionSupported) {
        console.warn('Speech recognition not supported in this browser');
      }

      if (!isSecureContext) {
        console.warn('Speech recognition requires HTTPS or localhost');
      }
    };

    checkSupport();
  }, []);

  // Check microphone permission
  useEffect(() => {
    const checkPermission = async () => {
      if (isSupported) {
        const permission = await checkMicrophonePermission();
        setHasPermission(permission);
      }
    };

    checkPermission();
  }, [isSupported]);

  const handleListening = async () => {
    if (!isSupported) {
      if (!checkSecureContext()) {
        toast.error(
          'Speech recognition requires HTTPS. Please use a secure connection.'
        );
      } else {
        toast.error(
          'Speech recognition is not supported in this browser. Try Chrome, Edge, or Safari.'
        );
      }
      return;
    }

    if (isListening) {
      try {
        SpeechRecognition.stopListening();
        setListening(false);
      } catch (error) {
        console.error('Error stopping speech recognition:', error);
        setListening(false);
      }
    } else {
      try {
        await SpeechRecognition.startListening({
          continuous: true,
          language: 'en-US', // Specify language for better compatibility
        });
        setListening(true);
      } catch (error) {
        console.error('Error starting speech recognition:', error);

        // Provide specific error messages
        const errorMessage = getSpeechRecognitionErrorMessage(error);
        toast.error(errorMessage);

        setListening(false);
      }
    }
  };

  // Determine icon and tooltip based on support and permission status
  const getIconAndTooltip = () => {
    if (!isSupported) {
      return {
        icon: <IoMicOffOutline size={18} style={{ opacity: 0.5 }} />,
        tooltip: !checkSecureContext()
          ? 'Speech recognition requires HTTPS'
          : 'Speech recognition not supported in this browser',
      };
    }

    if (hasPermission === false) {
      return {
        icon: <IoMicOffOutline size={18} style={{ opacity: 0.7 }} />,
        tooltip: 'Microphone permission required',
      };
    }

    return {
      icon: isListening ? <IoMicOff size={18} /> : <IoMic size={18} />,
      tooltip: isListening ? 'Stop listening' : 'Start voice input',
    };
  };

  const { icon, tooltip } = getIconAndTooltip();

  return (
    <div title={tooltip}>
      <IconButton
        icon={icon}
        onClick={handleListening}
        disabled={!isSupported}
      />
    </div>
  );
};

export default VoiceInput;

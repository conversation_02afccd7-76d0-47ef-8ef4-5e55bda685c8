export interface Participant {
  userId: string;
  name: string;
  profileImg?: string;
  status: string;
  approval_message?: string;
  rejection_message?: string;
  conditional_approved_message?: string;
  note?: string;
}

export interface Workflow {
  id: string;
  title: string;
  creatorId: string;
  currentUserId: string;
  subject?: string;
  priority?: string;
  dueDate?: string;
  createdOn: string;
  notes?: string[];
  participants: Participant[];
  approvalStatus?: string;
  sketchbookId?: string;
  projectId?: string;
  fullyApproved?: boolean;
}

export type ActionType = 'approve' | 'reject' | 'conditional' | null;

export type ViewMode = 'grid' | 'list';

export interface FilterOption {
  id: string;
  label: string;
  icon: React.ComponentType;
}

export interface WorkflowFormData {
  creator: string;
  date: string;
  projectName: string;
  priority: string;
  dueDate: string;
  subject: string;
  requiredActions: string;
}

export interface WorkflowPerson {
  id: string | number;
  name: string;
  note?: string;
  role?: string;
}

export interface WorkflowUser {
  id: string;
  name: string;
  [key: string]: any;
}

export interface WorkflowPayload {
  title: string;
  sketchbookId: string;
  projectId: string;
  creatorId: string;
  createdDate: string;
  dueDate: string;
  notes: string[];
  participants: {
    userId: string | number;
    note: string;
    approval_message: string;
    rejection_message: string;
    conditional_approved_message: string;
    status: string;
  }[];
  priority: string;
  projectName: string;
  requiredActions: string;
  subject: string;
  status: string;
  fullyApproved: boolean;
  currentUserId: string | number;
  approvalStatus: string;
}

export const priorityOptions = [
  { value: 'Low', label: 'Low' },
  { value: 'Medium', label: 'Medium' },
  { value: 'High', label: 'High' },
] as const;

// Notification types for workflow system
export interface WorkflowNotification {
  id: string;
  type: 'assignment' | 'status_change' | 'workflow_created';
  workflowId: string;
  workflowTitle: string;
  message: string;
  actionRequired: boolean;
  status?: 'approved' | 'rejected' | 'conditional' | 'in-progress';
  createdAt: string;
  read: boolean;
  readAt?: string; // New field from backend API
  userId: string;
  creatorName?: string;
  priority?: 'Low' | 'Medium' | 'High';
  // Additional fields that might come from backend
  title?: string; // Backend uses 'title' field
  content?: string; // Backend uses 'content' field
}

export interface NotificationState {
  notifications: WorkflowNotification[];
  unreadCount: number;
  isLoading: boolean;
  lastFetched: string | null;
  error: string | null;
  isCreating: boolean;
  isMarkingAsRead: boolean;
}

export const roleOptions = [
  { value: 'read/write', label: 'Read/Write' },
  { value: 'read', label: 'Read' },
] as const;

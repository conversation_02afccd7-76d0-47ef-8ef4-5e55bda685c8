/**
 * Configuration options for the Enhanced Markdown Renderer
 */

export interface MarkdownConfig {
  // LaTeX and Math Processing
  enableLatexPreprocessing: boolean;
  enableGreekConversion: boolean;
  enableCurrencyProtection: boolean;
  singleDollarTextMath: boolean;

  // Content Processing
  baseUrl: string;
  enableInlineHtml: boolean;

  // Code Highlighting
  enableSyntaxHighlighting: boolean;
  showLineNumbers: boolean;
  lineNumberThreshold: number;

  // Table Enhancement
  enableTableStyling: boolean;
  enableTableHover: boolean;

  // Link Processing
  enableExternalLinkIndicators: boolean;
  openExternalLinksInNewTab: boolean;

  // UI/UX
  theme: 'light' | 'dark' | 'auto';
  enableResponsiveDesign: boolean;

  // Error Handling
  enableFallbackRendering: boolean;
  showErrorDetails: boolean;
}

export const DEFAULT_MARKDOWN_CONFIG: MarkdownConfig = {
  // LaTeX and Math Processing
  enableLatexPreprocessing: true,
  enableGreekConversion: true,
  enableCurrencyProtection: true,
  singleDollarTextMath: true,

  // Content Processing
  baseUrl: '',
  enableInlineHtml: false,

  // Code Highlighting
  enableSyntaxHighlighting: true,
  showLineNumbers: true,
  lineNumberThreshold: 5,

  // Table Enhancement
  enableTableStyling: true,
  enableTableHover: true,

  // Link Processing
  enableExternalLinkIndicators: true,
  openExternalLinksInNewTab: true,

  // UI/UX
  theme: 'light',
  enableResponsiveDesign: true,

  // Error Handling
  enableFallbackRendering: true,
  showErrorDetails: false,
};

/**
 * Preset configurations for different use cases
 */
export const MARKDOWN_PRESETS = {
  // Minimal configuration for basic markdown
  minimal: {
    ...DEFAULT_MARKDOWN_CONFIG,
    enableLatexPreprocessing: false,
    enableGreekConversion: false,
    enableSyntaxHighlighting: false,
    enableTableStyling: false,
    enableExternalLinkIndicators: false,
  } as MarkdownConfig,

  // Scientific/Academic configuration
  scientific: {
    ...DEFAULT_MARKDOWN_CONFIG,
    enableLatexPreprocessing: true,
    enableGreekConversion: true,
    enableCurrencyProtection: true,
    singleDollarTextMath: true,
    showLineNumbers: true,
    lineNumberThreshold: 3,
  } as MarkdownConfig,

  // Documentation configuration
  documentation: {
    ...DEFAULT_MARKDOWN_CONFIG,
    enableLatexPreprocessing: false,
    enableGreekConversion: false,
    enableSyntaxHighlighting: true,
    showLineNumbers: true,
    lineNumberThreshold: 5,
    enableTableStyling: true,
    enableExternalLinkIndicators: true,
  } as MarkdownConfig,

  // Blog/Content configuration
  blog: {
    ...DEFAULT_MARKDOWN_CONFIG,
    enableLatexPreprocessing: false,
    enableGreekConversion: false,
    enableSyntaxHighlighting: true,
    showLineNumbers: false,
    enableTableStyling: true,
    enableExternalLinkIndicators: true,
    openExternalLinksInNewTab: true,
  } as MarkdownConfig,

  // Full-featured configuration
  full: {
    ...DEFAULT_MARKDOWN_CONFIG,
    enableInlineHtml: true,
    showErrorDetails: true,
  } as MarkdownConfig,
};

/**
 * Merges user configuration with defaults
 */
export function mergeMarkdownConfig(
  userConfig: Partial<MarkdownConfig> = {},
  baseConfig: MarkdownConfig = DEFAULT_MARKDOWN_CONFIG
): MarkdownConfig {
  return {
    ...baseConfig,
    ...userConfig,
  };
}

/**
 * Gets a preset configuration by name
 */
export function getMarkdownPreset(
  presetName: keyof typeof MARKDOWN_PRESETS
): MarkdownConfig {
  return MARKDOWN_PRESETS[presetName] || DEFAULT_MARKDOWN_CONFIG;
}

/**
 * Validates markdown configuration
 */
export function validateMarkdownConfig(
  config: Partial<MarkdownConfig>
): string[] {
  const errors: string[] = [];

  if (
    config.lineNumberThreshold !== undefined &&
    config.lineNumberThreshold < 0
  ) {
    errors.push('lineNumberThreshold must be a non-negative number');
  }

  if (config.theme && !['light', 'dark', 'auto'].includes(config.theme)) {
    errors.push('theme must be one of: light, dark, auto');
  }

  if (config.baseUrl && typeof config.baseUrl !== 'string') {
    errors.push('baseUrl must be a string');
  }

  return errors;
}

/**
 * Creates a configuration object from environment variables or defaults
 */
export function createConfigFromEnvironment(): MarkdownConfig {
  const config: Partial<MarkdownConfig> = {};

  // Check for environment variables (useful for different deployment environments)
  if (typeof window !== 'undefined') {
    // Browser environment - could read from localStorage or data attributes
    const stored = localStorage.getItem('markdownConfig');
    if (stored) {
      try {
        Object.assign(config, JSON.parse(stored));
      } catch (e) {
        // Failed to parse stored markdown config - using defaults
      }
    }
  }

  return mergeMarkdownConfig(config);
}

/**
 * Saves configuration to localStorage (browser only)
 */
export function saveConfigToStorage(config: MarkdownConfig): void {
  if (typeof window !== 'undefined' && window.localStorage) {
    try {
      localStorage.setItem('markdownConfig', JSON.stringify(config));
    } catch (e) {
      // Failed to save markdown config to localStorage
    }
  }
}

/**
 * Theme-specific syntax highlighting styles
 */
export const SYNTAX_HIGHLIGHTING_THEMES = {
  light: 'solarizedlight',
  dark: 'solarizeddark',
  auto: 'solarizedlight', // Default to light, could be enhanced to detect system theme
} as const;

/**
 * Gets the appropriate syntax highlighting theme
 */
export function getSyntaxHighlightingTheme(
  theme: MarkdownConfig['theme']
): string {
  return SYNTAX_HIGHLIGHTING_THEMES[theme] || SYNTAX_HIGHLIGHTING_THEMES.light;
}

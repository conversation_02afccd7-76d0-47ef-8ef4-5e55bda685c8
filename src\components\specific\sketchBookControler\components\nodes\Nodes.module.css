.node {
  padding: 10px;
  border-radius: 5px;
  min-width: 150px;
  background: var(--color-background-secondary);
  color: var(--color-text-primary);
  transition:
    background-color 0.3s ease,
    color 0.3s ease,
    border-color 0.3s ease;
}

.startNode {
  border-radius: 25px;
  background: #4caf50;
  color: white;
  border: none;
}

.endNode {
  border-radius: 25px;
  background: #f44336;
  color: white;
  border: none;
}

.processNode {
  border: 1px solid var(--color-primary);
}

.decisionNode {
  position: relative;
  border: 1px solid var(--color-warning-main);
  transform: rotate(45deg);
  width: 100px;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--color-background-secondary);
  transition:
    background-color 0.3s ease,
    border-color 0.3s ease;
}

.decisionNode .content {
  transform: rotate(-45deg);
  width: 141px;
  height: 141px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
}

.decisionNode .nodeInput {
  transform: none;
  width: 100%;
  text-align: center;
  position: relative;
  padding: 4px;
  font-size: 14px;
  border: none;
  background: transparent;
  outline: none;
  pointer-events: all !important;
}

.decisionNode .nodeInput:focus {
  background: rgba(var(--color-warning-main-rgb), 0.1);
}

.databaseNode {
  border: 1px solid var(--color-success-main);
  border-radius: 5px 5px 25px 25px;
}

.cloudNode {
  border: 1px solid var(--color-info-main);
  border-radius: 25px;
}

.operationNode {
  border: 1px solid var(--color-error-main);
  clip-path: polygon(20% 0%, 80% 0%, 100% 50%, 80% 100%, 20% 100%, 0% 50%);
  padding: 20px;
}

.orgNode {
  background: var(--color-background-secondary);
  border: 2px solid var(--color-primary);
  padding: 15px;
  min-width: 200px;
  transition:
    background-color 0.3s ease,
    border-color 0.3s ease;
}

.orgNode .content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.orgNode .nodeInput {
  width: 100%;
  border: none;
  border-bottom: 1px solid #ddd;
  background: transparent;
  padding: 8px 4px;
  margin: 0;
  font-size: 14px;
  outline: none;
  text-align: center;
  pointer-events: all !important;
  z-index: 20;
}

.orgNode .nodeInput:focus {
  border-bottom-color: #4a90e2;
  background: rgba(74, 144, 226, 0.05);
}

.orgNode .nodeInput::placeholder {
  color: #999;
}

.content {
  position: relative;
  z-index: 10;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.nodeInput {
  width: 100%;
  border: none;
  background: transparent;
  padding: 4px;
  margin: 0;
  font-size: 14px;
  outline: none;
  text-align: center;
  pointer-events: all !important;
  z-index: 20;
  color: var(--color-text-primary);
}

.nodeInput:focus {
  background: rgba(0, 0, 0, 0.05);
}

.orgNode .nodeInput {
  border-bottom: 1px solid #ddd;
  margin-bottom: 4px;
}

.orgNode .nodeInput:focus {
  border-bottom-color: var(--color-primary);
}

/* Make nodes and content clickable */
.processNode,
.decisionNode,
.startNode,
.endNode,
.databaseNode,
.cloudNode,
.operationNode,
.orgNode {
  pointer-events: all;
}

.handle {
  width: 8px;
  height: 8px;
  background: var(--color-primary);
  border: 2px solid var(--color-background-secondary);
  transition:
    background-color 0.3s ease,
    border-color 0.3s ease;
}

/* Override any ReactFlow styles that might interfere */
.processNode [data-no-drag] {
  pointer-events: all !important;
}

/* Add these styles to your existing CSS */
.documentNode {
  background: var(--color-background-secondary);
  border: 1px solid var(--color-secondary);
  border-radius: 5px 5px 0 0;
  position: relative;
  padding: 10px;
  padding-bottom: 20px; /* Extra padding for the wavy bottom */
  transition:
    background-color 0.3s ease,
    border-color 0.3s ease;
}

.documentNode::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 0;
  width: 100%;
  height: 10px;
  background: linear-gradient(
    -45deg,
    transparent 33.33%,
    #9c27b0 33.33%,
    #9c27b0 66.66%,
    transparent 66.66%
  );
  background-size: 20px 10px;
  background-repeat: repeat-x;
}

.documentNode .content {
  position: relative;
  z-index: 1;
}

.documentNode .nodeInput {
  color: var(--color-text-primary);
  font-size: 14px;
}

.documentNode .nodeInput:focus {
  background: rgba(156, 39, 176, 0.1);
}

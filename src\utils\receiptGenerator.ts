import { PaymentRecord } from '../types/payment';

export interface ReceiptData {
  payment: PaymentRecord;
  companyName?: string;
  companyAddress?: string;
  companyEmail?: string;
  companyPhone?: string;
}

export const formatDate = (dateString: string | null | undefined): string => {
  if (!dateString) return 'N/A';
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      timeZoneName: 'short',
    });
  } catch (error) {
    return 'Invalid Date';
  }
};

export const formatCurrency = (amount: number, currency: string = 'USD'): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency.toUpperCase(),
  }).format(amount);
};

export const generateTextReceipt = (data: ReceiptData): string => {
  const { payment, companyName = 'Your Company', companyEmail = '<EMAIL>' } = data;
  
  return `
═══════════════════════════════════════════════════════════════
                           PAYMENT RECEIPT
═══════════════════════════════════════════════════════════════

Company: ${companyName}
Email: ${companyEmail}
Date: ${new Date().toLocaleDateString('en-US', { 
  year: 'numeric', 
  month: 'long', 
  day: 'numeric' 
})}

───────────────────────────────────────────────────────────────
                        TRANSACTION DETAILS
───────────────────────────────────────────────────────────────

Payment ID:           ${payment.paymentIntentId || payment.id}
Internal ID:          ${payment.id}
Amount:               ${formatCurrency(payment.amount, payment.currency)}
Currency:             ${payment.currency.toUpperCase()}
Status:               ${payment.status.toUpperCase()}
Plan ID:              ${payment.planId}
User ID:              ${payment.userId}

───────────────────────────────────────────────────────────────
                           TIMELINE
───────────────────────────────────────────────────────────────

Created:              ${formatDate(payment.createdOn)}
${payment.dated ? `Processed:            ${formatDate(payment.dated)}` : ''}
${payment.updatedOn && payment.updatedOn !== payment.createdOn ? `Last Updated:         ${formatDate(payment.updatedOn)}` : ''}

───────────────────────────────────────────────────────────────
                        PAYMENT STATUS
───────────────────────────────────────────────────────────────

${getStatusMessage(payment.status)}

───────────────────────────────────────────────────────────────

Thank you for your payment!

This receipt was generated on ${new Date().toLocaleString()}

For support, please contact: ${companyEmail}

═══════════════════════════════════════════════════════════════
  `.trim();
};

export const generateCSVReceipt = (data: ReceiptData): string => {
  const { payment } = data;
  
  const headers = [
    'Payment ID',
    'Internal ID',
    'Amount',
    'Currency',
    'Status',
    'Plan ID',
    'User ID',
    'Created Date',
    'Processed Date',
    'Updated Date'
  ];
  
  const values = [
    `"${payment.paymentIntentId || payment.id}"`,
    `"${payment.id}"`,
    payment.amount,
    `"${payment.currency.toUpperCase()}"`,
    `"${payment.status}"`,
    `"${payment.planId}"`,
    `"${payment.userId}"`,
    `"${payment.createdOn || ''}"`,
    `"${payment.dated || ''}"`,
    `"${payment.updatedOn || ''}"`
  ];
  
  return [headers.join(','), values.join(',')].join('\n');
};

export const generateHTMLReceipt = (data: ReceiptData): string => {
  const { payment, companyName = 'Your Company', companyEmail = '<EMAIL>' } = data;
  
  return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Receipt - ${payment.paymentIntentId || payment.id}</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { text-align: center; border-bottom: 2px solid #333; padding-bottom: 20px; margin-bottom: 30px; }
        .company-name { font-size: 24px; font-weight: bold; margin-bottom: 10px; }
        .receipt-title { font-size: 20px; color: #666; }
        .section { margin-bottom: 25px; }
        .section-title { font-size: 16px; font-weight: bold; margin-bottom: 10px; color: #333; border-bottom: 1px solid #ddd; padding-bottom: 5px; }
        .detail-row { display: flex; justify-content: space-between; margin-bottom: 8px; }
        .label { font-weight: bold; }
        .value { color: #666; }
        .amount { font-size: 18px; font-weight: bold; color: #2563eb; }
        .status { padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: bold; text-transform: uppercase; }
        .status-succeeded { background-color: #dcfce7; color: #166534; }
        .status-failed { background-color: #fee2e2; color: #991b1b; }
        .status-pending { background-color: #fef3c7; color: #92400e; }
        .footer { text-align: center; margin-top: 40px; padding-top: 20px; border-top: 1px solid #ddd; color: #666; font-size: 14px; }
    </style>
</head>
<body>
    <div class="header">
        <div class="company-name">${companyName}</div>
        <div class="receipt-title">Payment Receipt</div>
    </div>
    
    <div class="section">
        <div class="section-title">Transaction Details</div>
        <div class="detail-row">
            <span class="label">Payment ID:</span>
            <span class="value">${payment.paymentIntentId || payment.id}</span>
        </div>
        <div class="detail-row">
            <span class="label">Amount:</span>
            <span class="value amount">${formatCurrency(payment.amount, payment.currency)}</span>
        </div>
        <div class="detail-row">
            <span class="label">Currency:</span>
            <span class="value">${payment.currency.toUpperCase()}</span>
        </div>
        <div class="detail-row">
            <span class="label">Status:</span>
            <span class="status status-${payment.status}">${payment.status}</span>
        </div>
        <div class="detail-row">
            <span class="label">Plan ID:</span>
            <span class="value">${payment.planId}</span>
        </div>
    </div>
    
    <div class="section">
        <div class="section-title">Timeline</div>
        <div class="detail-row">
            <span class="label">Created:</span>
            <span class="value">${formatDate(payment.createdOn)}</span>
        </div>
        ${payment.dated ? `
        <div class="detail-row">
            <span class="label">Processed:</span>
            <span class="value">${formatDate(payment.dated)}</span>
        </div>
        ` : ''}
        ${payment.updatedOn && payment.updatedOn !== payment.createdOn ? `
        <div class="detail-row">
            <span class="label">Last Updated:</span>
            <span class="value">${formatDate(payment.updatedOn)}</span>
        </div>
        ` : ''}
    </div>
    
    <div class="footer">
        <p>Thank you for your payment!</p>
        <p>Generated on ${new Date().toLocaleString()}</p>
        <p>For support, contact: ${companyEmail}</p>
    </div>
</body>
</html>
  `.trim();
};

const getStatusMessage = (status: string): string => {
  switch (status.toLowerCase()) {
    case 'succeeded':
      return '✅ Payment completed successfully';
    case 'failed':
      return '❌ Payment failed';
    case 'pending':
      return '⏳ Payment is being processed';
    case 'created':
      return '📝 Payment has been created';
    case 'canceled':
    case 'cancelled':
      return '🚫 Payment was canceled';
    default:
      return `ℹ️ Payment status: ${status}`;
  }
};

export const downloadReceipt = (
  payment: PaymentRecord, 
  format: 'txt' | 'csv' | 'html' = 'txt',
  companyInfo?: Partial<ReceiptData>
): void => {
  const receiptData: ReceiptData = {
    payment,
    ...companyInfo,
  };

  let content: string;
  let mimeType: string;
  let extension: string;

  switch (format) {
    case 'csv':
      content = generateCSVReceipt(receiptData);
      mimeType = 'text/csv;charset=utf-8';
      extension = 'csv';
      break;
    case 'html':
      content = generateHTMLReceipt(receiptData);
      mimeType = 'text/html;charset=utf-8';
      extension = 'html';
      break;
    default:
      content = generateTextReceipt(receiptData);
      mimeType = 'text/plain;charset=utf-8';
      extension = 'txt';
  }

  try {
    const blob = new Blob([content], { type: mimeType });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `receipt-${payment.paymentIntentId || payment.id}-${new Date().toISOString().split('T')[0]}.${extension}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  } catch (error) {
    console.error('Error downloading receipt:', error);
    throw new Error('Failed to download receipt. Please try again.');
  }
};

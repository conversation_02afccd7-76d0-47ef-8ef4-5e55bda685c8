import React, { useState } from 'react';
import { PaymentRecord } from '../../types/payment';
import { useTheme } from '../../contexts/ThemeContext';
import InvoiceStatusBadge from './InvoiceStatusBadge';
import PaymentDetailsModal from './PaymentDetailsModal';
import { downloadReceipt } from '../../utils/receiptGenerator';
import styles from './InvoiceTable.module.css';

interface InvoiceTableProps {
  payments: PaymentRecord[];
  isLoading: boolean;
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
}

const InvoiceTable: React.FC<InvoiceTableProps> = ({
  payments,
  isLoading,
  currentPage,
  totalPages,
  onPageChange,
}) => {
  const { isDarkMode } = useTheme();
  const [selectedPayment, setSelectedPayment] = useState<PaymentRecord | null>(
    null
  );
  const [isModalOpen, setIsModalOpen] = useState(false);

  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return 'N/A';
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      });
    } catch (error) {
      return 'Invalid Date';
    }
  };

  const formatCurrency = (amount: number, currency: string = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency.toUpperCase(),
    }).format(amount);
  };

  const handleViewDetails = (payment: PaymentRecord) => {
    setSelectedPayment(payment);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedPayment(null);
  };

  const handleDownloadReceipt = (payment: PaymentRecord) => {
    try {
      downloadReceipt(payment, 'txt', {
        companyName: 'NeuQuip',
        companyEmail: '<EMAIL>',
      });
    } catch (error) {
      console.error('Error downloading receipt:', error);
      alert('Failed to download receipt. Please try again.');
    }
  };

  const generatePagination = () => {
    const pages = [];
    const maxVisiblePages = 5;

    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    // Previous button
    if (currentPage > 1) {
      pages.push(
        <button
          key="prev"
          onClick={() => onPageChange(currentPage - 1)}
          className={styles.paginationButton}
        >
          ‹
        </button>
      );
    }

    // First page
    if (startPage > 1) {
      pages.push(
        <button
          key={1}
          onClick={() => onPageChange(1)}
          className={styles.paginationButton}
        >
          1
        </button>
      );
      if (startPage > 2) {
        pages.push(
          <span key="ellipsis1" className={styles.paginationEllipsis}>
            ...
          </span>
        );
      }
    }

    // Page numbers
    for (let i = startPage; i <= endPage; i++) {
      pages.push(
        <button
          key={i}
          onClick={() => onPageChange(i)}
          className={`${styles.paginationButton} ${
            i === currentPage ? styles.active : ''
          }`}
        >
          {i}
        </button>
      );
    }

    // Last page
    if (endPage < totalPages) {
      if (endPage < totalPages - 1) {
        pages.push(
          <span key="ellipsis2" className={styles.paginationEllipsis}>
            ...
          </span>
        );
      }
      pages.push(
        <button
          key={totalPages}
          onClick={() => onPageChange(totalPages)}
          className={styles.paginationButton}
        >
          {totalPages}
        </button>
      );
    }

    // Next button
    if (currentPage < totalPages) {
      pages.push(
        <button
          key="next"
          onClick={() => onPageChange(currentPage + 1)}
          className={styles.paginationButton}
        >
          ›
        </button>
      );
    }

    return pages;
  };

  if (isLoading) {
    return (
      <div className={styles.loadingContainer}>
        <div className={styles.loadingSpinner}></div>
        <p>Loading payments...</p>
      </div>
    );
  }

  if (payments.length === 0) {
    return (
      <div className={styles.emptyState}>
        <div className={styles.emptyIcon}>📄</div>
        <h3>No payments found</h3>
        <p>
          You haven't made any payments yet or no payments match your current
          filters.
        </p>
      </div>
    );
  }

  return (
    <div className={`${styles.container} ${isDarkMode ? styles.dark : ''}`}>
      <div className={styles.tableWrapper}>
        <table className={styles.table}>
          <thead>
            <tr>
              <th>Payment ID</th>
              <th>Date</th>
              <th>Amount</th>
              <th>Status</th>
              <th>Plan</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {payments.map((payment) => (
              <tr key={payment.id}>
                <td>
                  <div className={styles.paymentId}>
                    <span className={styles.idText}>
                      {payment.paymentIntentId || payment.id}
                    </span>
                  </div>
                </td>
                <td>
                  <div className={styles.dateCell}>
                    <span className={styles.primaryDate}>
                      {formatDate(payment.dated || payment.createdOn)}
                    </span>
                    {payment.updatedOn &&
                      payment.updatedOn !== payment.createdOn && (
                        <span className={styles.secondaryDate}>
                          Updated: {formatDate(payment.updatedOn)}
                        </span>
                      )}
                  </div>
                </td>
                <td>
                  <span className={styles.amount}>
                    {formatCurrency(payment.amount, payment.currency)}
                  </span>
                </td>
                <td>
                  <InvoiceStatusBadge status={payment.status} />
                </td>
                <td>
                  <span className={styles.planId}>{payment.planId}</span>
                </td>
                <td>
                  <div className={styles.actions}>
                    <button
                      className={styles.actionButton}
                      onClick={() => handleViewDetails(payment)}
                      title="View Details"
                    >
                      <svg
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="currentColor"
                      >
                        <path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z" />
                      </svg>
                    </button>
                    {payment.status === 'succeeded' && (
                      <button
                        className={styles.actionButton}
                        onClick={() => handleDownloadReceipt(payment)}
                        title="Download Receipt"
                      >
                        <svg
                          width="16"
                          height="16"
                          viewBox="0 0 24 24"
                          fill="currentColor"
                        >
                          <path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z" />
                        </svg>
                      </button>
                    )}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {totalPages > 1 && (
        <div className={styles.pagination}>
          <div className={styles.paginationInfo}>
            Page {currentPage} of {totalPages}
          </div>
          <div className={styles.paginationControls}>
            {generatePagination()}
          </div>
        </div>
      )}

      {/* Payment Details Modal */}
      <PaymentDetailsModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        payment={selectedPayment}
      />
    </div>
  );
};

export default InvoiceTable;

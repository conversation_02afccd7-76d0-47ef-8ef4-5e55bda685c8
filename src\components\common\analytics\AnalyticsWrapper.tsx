/**
 * Analytics wrapper components for tracking user interactions
 * Provides easy-to-use components for tracking clicks, form submissions, and other events
 */

import React, { ReactElement, cloneElement } from 'react';
import { useAnalyticsEvents } from '../../../hooks/useAnalytics';

interface AnalyticsWrapperProps {
  children: ReactElement;
  eventName: string;
  eventCategory?: string;
  eventLabel?: string;
  customProperties?: Record<string, any>;
}

/**
 * Generic analytics wrapper for tracking click events
 */
export const AnalyticsClickWrapper: React.FC<AnalyticsWrapperProps> = ({
  children,
  eventName,
  eventCategory = 'interaction',
  eventLabel,
  customProperties,
}) => {
  const { trackButtonClick } = useAnalyticsEvents();

  const handleClick = (originalOnClick?: () => void) => {
    return (event: React.MouseEvent) => {
      // Track the analytics event
      trackButtonClick(eventName);

      // Call the original onClick handler if it exists
      if (originalOnClick) {
        originalOnClick();
      }

      // If the original element had an onClick, call it
      if (children.props.onClick) {
        children.props.onClick(event);
      }
    };
  };

  return cloneElement(children, {
    onClick: handleClick(children.props.onClick),
  });
};

/**
 * Wrapper for tracking navigation menu clicks
 */
interface MenuClickWrapperProps {
  children: ReactElement;
  menuItem: string;
}

export const MenuClickWrapper: React.FC<MenuClickWrapperProps> = ({
  children,
  menuItem,
}) => {
  const { trackMenuClick } = useAnalyticsEvents();

  const handleClick = (event: React.MouseEvent) => {
    trackMenuClick(menuItem);

    if (children.props.onClick) {
      children.props.onClick(event);
    }
  };

  return cloneElement(children, {
    onClick: handleClick,
  });
};

/**
 * Wrapper for tracking form submissions
 */
interface FormSubmitWrapperProps {
  children: ReactElement;
  formName: string;
  formType?: string;
}

export const FormSubmitWrapper: React.FC<FormSubmitWrapperProps> = ({
  children,
  formName,
  formType = 'form',
}) => {
  const { trackButtonClick } = useAnalyticsEvents();

  const handleSubmit = (event: React.FormEvent) => {
    trackButtonClick(`${formName}_submit`);

    if (children.props.onSubmit) {
      children.props.onSubmit(event);
    }
  };

  return cloneElement(children, {
    onSubmit: handleSubmit,
  });
};

/**
 * Wrapper for tracking search events
 */
interface SearchWrapperProps {
  children: ReactElement;
  searchType?: string;
}

export const SearchWrapper: React.FC<SearchWrapperProps> = ({
  children,
  searchType = 'general',
}) => {
  const { trackSearch } = useAnalyticsEvents();

  const handleSearch = (searchTerm: string) => {
    if (searchTerm.trim()) {
      trackSearch(searchTerm, searchType);
    }
  };

  const handleSubmit = (event: React.FormEvent) => {
    const formData = new FormData(event.target as HTMLFormElement);
    const searchTerm = formData.get('search') as string;
    
    if (searchTerm) {
      handleSearch(searchTerm);
    }

    if (children.props.onSubmit) {
      children.props.onSubmit(event);
    }
  };

  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter') {
      const target = event.target as HTMLInputElement;
      if (target.value) {
        handleSearch(target.value);
      }
    }

    if (children.props.onKeyPress) {
      children.props.onKeyPress(event);
    }
  };

  return cloneElement(children, {
    onSubmit: handleSubmit,
    onKeyPress: handleKeyPress,
  });
};

/**
 * Wrapper for tracking file upload events
 */
interface FileUploadWrapperProps {
  children: ReactElement;
  uploadType?: string;
}

export const FileUploadWrapper: React.FC<FileUploadWrapperProps> = ({
  children,
  uploadType = 'general',
}) => {
  const { trackFileUpload } = useAnalyticsEvents();

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      const file = files[0];
      const fileType = file.type || 'unknown';
      const fileSize = file.size;
      
      trackFileUpload(fileType, fileSize);
    }

    if (children.props.onChange) {
      children.props.onChange(event);
    }
  };

  return cloneElement(children, {
    onChange: handleFileChange,
  });
};

/**
 * Higher-order component for automatic analytics tracking
 */
export function withAnalytics<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  analyticsConfig: {
    trackPageView?: boolean;
    trackClicks?: boolean;
    componentName?: string;
  } = {}
) {
  const { trackPageView = false, trackClicks = false, componentName } = analyticsConfig;

  return function AnalyticsEnhancedComponent(props: P) {
    const analytics = useAnalyticsEvents();

    React.useEffect(() => {
      if (trackPageView && componentName) {
        // Track component mount as a virtual page view
        analytics.trackButtonClick(`${componentName}_view`);
      }
    }, [analytics, componentName]);

    const enhancedProps = trackClicks
      ? {
          ...props,
          onClick: (event: React.MouseEvent) => {
            if (componentName) {
              analytics.trackButtonClick(`${componentName}_click`);
            }
            // @ts-ignore
            if (props.onClick) {
              // @ts-ignore
              props.onClick(event);
            }
          },
        }
      : props;

    return <WrappedComponent {...enhancedProps} />;
  };
}

/**
 * Hook for manual analytics tracking within components
 */
export const useComponentAnalytics = (componentName: string) => {
  const analytics = useAnalyticsEvents();

  return {
    trackComponentView: () => {
      analytics.trackButtonClick(`${componentName}_view`);
    },
    
    trackComponentAction: (action: string) => {
      analytics.trackButtonClick(`${componentName}_${action}`);
    },
    
    trackComponentError: (error: string) => {
      analytics.trackJSError(`${componentName}: ${error}`);
    },
    
    ...analytics,
  };
};

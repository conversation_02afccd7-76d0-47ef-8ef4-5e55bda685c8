/* Custom styles for Driver.js tours */

/* Popover styling */
.neuquip-tour-popover {
  border-radius: 8px !important;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15) !important;
  border: none !important;
  overflow: hidden !important;
  max-width: 400px !important;
}

/* Popover title */
.neuquip-tour-popover .driver-popover-title {
  font-size: 18px !important;
  font-weight: 600 !important;
  padding: 16px 24px !important;
  margin: 0 !important;
  background-color: var(--color-primary) !important;
  color: white !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
}

/* Popover description */
.neuquip-tour-popover .driver-popover-description {
  font-size: 14px !important;
  line-height: 1.6 !important;
  padding: 20px 24px !important;
  /* color: var(--color-text-primary) !important; */
  margin: 0 !important;
}

/* Popover footer */
.neuquip-tour-popover .driver-popover-footer {
  padding: 12px 24px !important;
  border-top: 1px solid var(--color-border) !important;
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  background-color: var(--color-background-secondary) !important;
}

/* Progress text */
.neuquip-tour-popover .driver-popover-progress-text {
  font-size: 13px !important;
  color: var(--color-text-secondary) !important;
}

/* Buttons */
.neuquip-tour-popover .driver-popover-navigation-btns {
  display: flex !important;
  gap: 8px !important;
}

.neuquip-tour-popover .driver-popover-prev-btn,
.neuquip-tour-popover .driver-popover-next-btn {
  padding: 8px 16px !important;
  border-radius: 4px !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  border: none !important;
  outline: none !important;
}

.neuquip-tour-popover .driver-popover-prev-btn {
  /* background-color: var(--color-background-tertiary) !important; */
  /* color: var(--color-text-primary) !important; */
  border: 1px solid var(--color-border) !important;
}

.neuquip-tour-popover .driver-popover-next-btn {
  /* background-color: var(--color-primary) !important; */
  /* color: white !important; */
  border: 1px solid var(--color-border) !important;
}

/* .neuquip-tour-popover .driver-popover-prev-btn:hover {
  background-color: var(--color-background-primary) !important;
} */

/* .neuquip-tour-popover .driver-popover-next-btn:hover {
  background-color: var(--color-primary-dark) !important;
} */

.neuquip-tour-popover .driver-popover-close-btn {
  position: absolute !important;
  top: 12px !important;
  right: 12px !important;
  font-size: 18px !important;
  color: white !important;
  background: none !important;
  border: none !important;
  cursor: pointer !important;
  opacity: 0.8 !important;
  transition: opacity 0.2s ease !important;
  z-index: 10 !important;
}

.neuquip-tour-popover .driver-popover-close-btn:hover {
  opacity: 1 !important;
}

/* Highlighted element */
.driver-active-element {
  box-shadow: 0 0 0 4px rgba(25, 118, 210, 0.7) !important;
  border-radius: 4px !important;
  transition: all 0.3s ease !important;
  position: relative !important;
  z-index: 10000 !important;
  outline: 2px solid #1976d2 !important;
  animation: pulse-highlight 2s infinite !important;
}

@keyframes pulse-highlight {
  0% {
    box-shadow: 0 0 0 4px rgba(25, 118, 210, 0.7) !important;
  }
  50% {
    box-shadow: 0 0 0 6px rgba(25, 118, 210, 0.5) !important;
  }
  100% {
    box-shadow: 0 0 0 4px rgba(25, 118, 210, 0.7) !important;
  }
}

/* Overlay */
/* .driver-overlay {
  background-color: rgba(0, 0, 0, 0.5) !important;
  backdrop-filter: blur(2px) !important;
  z-index: 9999 !important;
} */

/* Stage */
.driver-stage-wrapper {
  z-index: 10001 !important;
}

/* Ensure sidebar elements are properly highlighted */
#sidebar.driver-active-element,
a[href='/'].driver-active-element,
a[href='/my-projects'].driver-active-element,
a[href='/awaiting-actions'].driver-active-element,
a[href='/sketchbooklists'].driver-active-element {
  z-index: 10002 !important;
  position: relative !important;
  outline: 2px solid #1976d2 !important;
  background-color: rgba(25, 118, 210, 0.1) !important;
}

/* Animation for popover */
@keyframes popoverFadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.neuquip-tour-popover {
  animation: popoverFadeIn 0.3s ease forwards !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .neuquip-tour-popover {
    max-width: 300px !important;
  }

  .neuquip-tour-popover .driver-popover-title {
    font-size: 16px !important;
    padding: 12px 20px !important;
  }

  .neuquip-tour-popover .driver-popover-description {
    font-size: 13px !important;
    padding: 16px 20px !important;
  }

  .neuquip-tour-popover .driver-popover-footer {
    padding: 10px 20px !important;
  }
}

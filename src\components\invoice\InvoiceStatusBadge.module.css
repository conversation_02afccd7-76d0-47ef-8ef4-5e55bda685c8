.badge {
  display: inline-flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.375rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  border: 1px solid transparent;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.icon {
  font-size: 0.875rem;
  line-height: 1;
}

.label {
  line-height: 1;
}

/* Success Status (Succeeded) */
.badge.success {
  background-color: #dcfce7;
  color: #166534;
  border-color: #bbf7d0;
}

.dark .badge.success {
  background-color: #14532d;
  color: #bbf7d0;
  border-color: #166534;
}

/* Error Status (Failed) */
.badge.error {
  background-color: #fee2e2;
  color: #991b1b;
  border-color: #fecaca;
}

.dark .badge.error {
  background-color: #7f1d1d;
  color: #fecaca;
  border-color: #991b1b;
}

/* Warning Status (Pending, Processing, Requires Action) */
.badge.warning {
  background-color: #fef3c7;
  color: #92400e;
  border-color: #fde68a;
}

.dark .badge.warning {
  background-color: #78350f;
  color: #fde68a;
  border-color: #92400e;
}

/* Info Status (Created) */
.badge.info {
  background-color: #dbeafe;
  color: #1e40af;
  border-color: #bfdbfe;
}

.dark .badge.info {
  background-color: #1e3a8a;
  color: #bfdbfe;
  border-color: #1e40af;
}

/* Neutral Status (Canceled, Unknown) */
.badge.neutral {
  background-color: #f3f4f6;
  color: #374151;
  border-color: #e5e7eb;
}

.dark .badge.neutral {
  background-color: #374151;
  color: #d1d5db;
  border-color: #4b5563;
}

/* Hover Effects */
.badge:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.dark .badge:hover {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Animation for processing status */
.badge.warning .icon {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .badge {
    padding: 0.25rem 0.5rem;
    font-size: 0.6875rem;
    gap: 0.25rem;
  }

  .icon {
    font-size: 0.75rem;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .badge {
    border-width: 2px;
  }

  .badge.success {
    background-color: #22c55e;
    color: #ffffff;
    border-color: #16a34a;
  }

  .badge.error {
    background-color: #ef4444;
    color: #ffffff;
    border-color: #dc2626;
  }

  .badge.warning {
    background-color: #f59e0b;
    color: #ffffff;
    border-color: #d97706;
  }

  .badge.info {
    background-color: #3b82f6;
    color: #ffffff;
    border-color: #2563eb;
  }

  .badge.neutral {
    background-color: #6b7280;
    color: #ffffff;
    border-color: #4b5563;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .badge {
    transition: none;
  }

  .badge:hover {
    transform: none;
  }

  .badge.warning .icon {
    animation: none;
  }
}

import React, { useEffect } from 'react';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';
import { Button, Box } from '@mui/material';
import { useTheme } from '../../hooks/useTheme';

interface WelcomeDialogProps {
  onClose: () => void;
}

const WelcomeDialog: React.FC<WelcomeDialogProps> = ({ onClose }) => {
  const theme = useTheme();

  const handleClose = () => {
    localStorage.setItem('hasSeenWelcome', 'true');
    onClose();
  };

  return (
    <Dialog
      open={true}
      onClose={handleClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          p: 2,
          backgroundColor: theme.colors.background.default,
          color: theme.colors.text.primary,
          boxShadow: theme.shadows.lg,
        },
      }}
    >
      <DialogTitle
        sx={{
          textAlign: 'center',
          fontSize: '1.5rem',
          fontWeight: 600,
          color: theme.colors.primary.main,
          pb: 1,
          fontFamily: theme.typography.fontFamily.primary,
        }}
      >
        Welcome to Neuquip!
      </DialogTitle>
      <DialogContent>
        <DialogContentText
          sx={{
            textAlign: 'center',
            fontSize: '1.1rem',
            mb: 3,
            color: theme.colors.text.secondary,
            fontFamily: theme.typography.fontFamily.primary,
            lineHeight: 1.6,
          }}
        >
          We're excited to have you on board!
          <br />
          Get started by exploring our features and tools.
        </DialogContentText>
        <Box display="flex" justifyContent="center">
          <Button
            onClick={handleClose}
            variant="contained"
            sx={{
              minWidth: 200,
              borderRadius: 2,
              textTransform: 'none',
              fontSize: '1rem',
              py: 1,
              backgroundColor: theme.colors.primary.main,
              color: theme.colors.primary.contrastText,
              fontFamily: theme.typography.fontFamily.primary,
              fontWeight: 600,
              '&:hover': {
                backgroundColor: theme.colors.primary.dark,
                transform: 'translateY(-1px)',
                boxShadow: theme.shadows.md,
              },
              transition: 'all 0.2s ease',
            }}
          >
            Get Started
          </Button>
        </Box>
      </DialogContent>
    </Dialog>
  );
};

export default WelcomeDialog;

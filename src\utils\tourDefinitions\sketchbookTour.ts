// Sketchbook page tour definition
export const sketchbookTour = {
  id: 'sketchbook-tour',
  name: 'Sketchbook Feature Tour',
  routes: ['/sketchbook', '/sketchbook/*', '/sketchbooklists'], // Available on sketchbook pages
  requiresNavigation: true, // Should navigate to sketchbook page if not already there
  // Add video tour information
  videoTour: {
    videoId: 'x2oZJKTN_TU?si=7c74j_rJwA_sumTJ', // Replace with your actual YouTube video ID
    description:
      'This video tutorial will guide you through all the features of the Sketchbook tool, showing you how to create and customize visual presentations effectively.',
  },
  // Keep the steps as a fallback in case video can't be loaded
  // steps: [
  //   {
  //     element: '#sketchbook-header',
  //     popover: {
  //       title: 'Sketchbook Canvas',
  //       description:
  //         'This is your canvas where you can create visual presentations.',
  //       position: 'bottom',
  //     },
  //   },
  //   {
  //     element: '#sketchbook-controller',
  //     popover: {
  //       title: 'Sketchbook Tools',
  //       description: 'Use these tools to add elements to your sketchbook.',
  //       position: 'right',
  //     },
  //   },
  //   {
  //     element: '.page-controls',
  //     popover: {
  //       title: 'Page Controls',
  //       description:
  //         'Add, remove, and navigate between pages in your sketchbook.',
  //       position: 'top',
  //     },
  //   },
  //   {
  //     element: '.chart-properties',
  //     popover: {
  //       title: 'Chart Properties',
  //       description: 'Customize your charts and visualizations here.',
  //       position: 'left',
  //     },
  //   },
  // ],
};

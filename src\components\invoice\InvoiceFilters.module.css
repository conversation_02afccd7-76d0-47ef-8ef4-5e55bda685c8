.container {
  background-color: var(--card-background, #ffffff);
  border: 1px solid var(--border-color, #e5e7eb);
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.dark .container {
  background-color: var(--dark-card-background, #2d2d2d);
  border-color: var(--dark-border, #404040);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  background-color: var(--filter-header-bg, #f9fafb);
  border-bottom: 1px solid var(--border-color, #e5e7eb);
}

.dark .header {
  background-color: var(--dark-filter-header-bg, #374151);
  border-bottom-color: var(--dark-border, #404040);
}

.toggleButton {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: none;
  border: none;
  color: var(--text-color, #374151);
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 0.375rem;
  transition: all 0.2s ease;
}

.toggleButton:hover {
  background-color: var(--hover-bg, #f3f4f6);
}

.dark .toggleButton {
  color: var(--dark-text, #f3f4f6);
}

.dark .toggleButton:hover {
  background-color: var(--dark-hover-bg, #4b5563);
}

.toggleIcon {
  transition: transform 0.2s ease;
}

.toggleIcon.expanded {
  transform: rotate(180deg);
}

.filterCount {
  background-color: var(--primary-color, #2563eb);
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.125rem 0.5rem;
  border-radius: 9999px;
  min-width: 1.25rem;
  text-align: center;
}

.dark .filterCount {
  background-color: var(--dark-primary, #60a5fa);
  color: var(--dark-text, #1f2937);
}

.resetButton {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.5rem 0.75rem;
  background-color: var(--error-bg, #fee2e2);
  color: var(--error-color, #dc2626);
  border: 1px solid var(--error-border, #fecaca);
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.resetButton:hover {
  background-color: var(--error-hover-bg, #fecaca);
  border-color: var(--error-color, #dc2626);
}

.dark .resetButton {
  background-color: var(--dark-error-bg, #7f1d1d);
  color: var(--dark-error-color, #fecaca);
  border-color: var(--dark-error-border, #991b1b);
}

.dark .resetButton:hover {
  background-color: var(--dark-error-hover-bg, #991b1b);
  border-color: var(--dark-error-color, #fecaca);
}

.filtersContent {
  padding: 1.5rem;
  border-top: 1px solid var(--border-color, #e5e7eb);
  background-color: var(--filter-content-bg, #ffffff);
}

.dark .filtersContent {
  border-top-color: var(--dark-border, #404040);
  background-color: var(--dark-filter-content-bg, #2d2d2d);
}

.filtersGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.filterGroup {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filterLabel {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-color, #374151);
}

.dark .filterLabel {
  color: var(--dark-text, #f3f4f6);
}

.filterSelect,
.filterInput {
  padding: 0.75rem;
  border: 1px solid var(--border-color, #d1d5db);
  border-radius: 0.5rem;
  background-color: var(--input-background, #ffffff);
  color: var(--text-color, #374151);
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.filterSelect:focus,
.filterInput:focus {
  outline: none;
  border-color: var(--primary-color, #2563eb);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.dark .filterSelect,
.dark .filterInput {
  background-color: var(--dark-input-background, #374151);
  color: var(--dark-text, #f3f4f6);
  border-color: var(--dark-border, #4b5563);
}

.dark .filterSelect:focus,
.dark .filterInput:focus {
  border-color: var(--dark-primary, #60a5fa);
  box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1);
}

.quickFilters {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  padding-top: 1rem;
  border-top: 1px solid var(--border-color, #e5e7eb);
}

.dark .quickFilters {
  border-top-color: var(--dark-border, #404040);
}

.quickFiltersLabel {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-secondary, #6b7280);
}

.dark .quickFiltersLabel {
  color: var(--dark-text-secondary, #9ca3af);
}

.quickFilterButtons {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.quickFilterButton {
  padding: 0.5rem 1rem;
  background-color: var(--quick-filter-bg, #f3f4f6);
  color: var(--text-color, #374151);
  border: 1px solid var(--border-color, #d1d5db);
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.quickFilterButton:hover {
  background-color: var(--quick-filter-hover-bg, #e5e7eb);
  border-color: var(--primary-color, #2563eb);
}

.quickFilterButton.active {
  background-color: var(--primary-color, #2563eb);
  color: white;
  border-color: var(--primary-color, #2563eb);
}

.dark .quickFilterButton {
  background-color: var(--dark-quick-filter-bg, #374151);
  color: var(--dark-text, #f3f4f6);
  border-color: var(--dark-border, #4b5563);
}

.dark .quickFilterButton:hover {
  background-color: var(--dark-quick-filter-hover-bg, #4b5563);
  border-color: var(--dark-primary, #60a5fa);
}

.dark .quickFilterButton.active {
  background-color: var(--dark-primary, #60a5fa);
  color: var(--dark-text, #1f2937);
  border-color: var(--dark-primary, #60a5fa);
}

/* Responsive Design */
@media (max-width: 768px) {
  .header {
    padding: 0.75rem 1rem;
  }

  .filtersContent {
    padding: 1rem;
  }

  .filtersGrid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
    margin-bottom: 1rem;
  }

  .quickFilterButtons {
    gap: 0.375rem;
  }

  .quickFilterButton {
    padding: 0.375rem 0.75rem;
    font-size: 0.6875rem;
  }

  .resetButton {
    padding: 0.375rem 0.5rem;
    font-size: 0.6875rem;
  }
}

@media (max-width: 480px) {
  .header {
    flex-direction: column;
    gap: 0.75rem;
    align-items: stretch;
  }

  .resetButton {
    align-self: flex-end;
  }

  .quickFilters {
    gap: 0.5rem;
  }

  .quickFilterButtons {
    justify-content: center;
  }
}

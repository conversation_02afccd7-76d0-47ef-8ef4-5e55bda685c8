import { useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import {
  setCurrentPlan,
  setLoading,
  setError,
  clearError,
  setSuccess,
  resetPaymentState,
  startPaymentFlow,
  clearPaymentLoading,
  selectPaymentState,
  selectCurrentPlan,
  selectPaymentError,
  selectPaymentSuccess,
  selectLastPayment,
} from '../../store/slices/paymentSlice';
import {
  useGetPaymentPlansQuery,
  useGetPaymentHistoryQuery,
} from '../../services/paymentService';
import {
  PaymentPlan,
  PaymentFormData,
  PaymentRecord,
} from '../../types/payment';
import { DEFAULT_PAYMENT_PLANS } from '../../services/constants/paymentServiceConstants';
import {
  getCurrentUserId,
  getCurrentUserEmail,
} from '../../utils/auth/userHelpers';
import { shouldFetchPaymentHistory } from '../../utils/payment/paymentFeatureFlags';

export const usePayment = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();

  // Get current user info
  const currentUserId = getCurrentUserId();
  const currentUserEmail = getCurrentUserEmail();

  // Selectors
  const paymentState = useSelector(selectPaymentState);
  const currentPlan = useSelector(selectCurrentPlan);
  const paymentError = useSelector(selectPaymentError);
  const paymentSuccess = useSelector(selectPaymentSuccess);
  const lastPayment = useSelector(selectLastPayment);

  // API queries with error handling
  const {
    data: plans = DEFAULT_PAYMENT_PLANS,
    isLoading: plansLoading,
    error: plansError,
    refetch: refetchPlans,
  } = useGetPaymentPlansQuery(undefined, {
    // Don't show plan loading errors in the payment error state
    // Plans will fall back to DEFAULT_PAYMENT_PLANS if API fails
    retry: 1, // Only retry once for plans
  });

  // Payment history is controlled by feature flags
  const shouldFetchHistoryFlag = shouldFetchPaymentHistory();
  const shouldFetchHistoryAuth = !!currentUserId;
  const shouldFetchHistoryFinal =
    shouldFetchHistoryFlag && shouldFetchHistoryAuth;

  const {
    data: paymentHistory = [],
    isLoading: historyLoading,
    error: historyError,
    refetch: refetchHistory,
  } = useGetPaymentHistoryQuery(
    {
      page: 1,
      size: 10,
      userId: currentUserId || undefined,
    },
    {
      skip: !shouldFetchHistoryFinal, // Skip based on feature flags and auth
    }
  );

  // Payment methods are not available in the current backend API
  // Removing this call to prevent 404 errors
  const paymentMethods: any[] = [];
  const methodsLoading = false;
  const methodsError = null;
  const refetchMethods = () => Promise.resolve();

  // Actions
  const selectPlan = useCallback(
    (plan: PaymentPlan) => {
      dispatch(setCurrentPlan(plan));
      dispatch(clearError());
    },
    [dispatch]
  );

  const startPayment = useCallback(
    (plan: PaymentPlan) => {
      dispatch(startPaymentFlow(plan));
      navigate('/payment');
    },
    [dispatch, navigate]
  );

  const clearPaymentError = useCallback(() => {
    dispatch(clearError());
  }, [dispatch]);

  const clearPaymentSuccess = useCallback(() => {
    dispatch(setSuccess(false));
  }, [dispatch]);

  const clearLoading = useCallback(() => {
    dispatch(clearPaymentLoading());
  }, [dispatch]);

  const resetPayment = useCallback(() => {
    dispatch(resetPaymentState());
  }, [dispatch]);

  const handlePaymentSuccess = useCallback(
    (paymentRecord: PaymentRecord) => {
      // Refresh data after successful payment only if queries were actually started
      try {
        // Only refetch history if the query was not skipped
        if (shouldFetchHistoryFinal) {
          refetchHistory();
        }

        // refetchMethods is a mock function, safe to call
        refetchMethods();
      } catch (error) {
        console.warn('Error refetching data after payment success:', error);
        // Don't let refetch errors block the success flow
      }

      // Navigate to success page
      navigate('/payment/success', {
        state: { paymentRecord },
      });
    },
    [shouldFetchHistoryFinal, refetchHistory, refetchMethods, navigate]
  );

  const handlePaymentError = useCallback(
    (error: string) => {
      dispatch(setError(error));

      // Navigate to error page
      navigate('/payment/error', {
        state: { error },
      });
    },
    [dispatch, navigate]
  );

  const retryPayment = useCallback(() => {
    if (currentPlan) {
      dispatch(resetPaymentState());
      navigate('/payment');
    }
  }, [currentPlan, dispatch, navigate]);

  const cancelPayment = useCallback(() => {
    dispatch(resetPaymentState());
    navigate('/price');
  }, [dispatch, navigate]);

  // Utility functions
  const getPlanById = useCallback(
    (planId: string): PaymentPlan | undefined => {
      return plans.find((plan) => plan.id === planId);
    },
    [plans]
  );

  const getPopularPlan = useCallback((): PaymentPlan | undefined => {
    return plans.find((plan) => plan.isPopular);
  }, [plans]);

  const getFreePlan = useCallback((): PaymentPlan | undefined => {
    return plans.find((plan) => plan.price === 0);
  }, [plans]);

  const formatPlanPrice = useCallback((plan: PaymentPlan): string => {
    if (plan.price === 0) {
      return 'Free';
    }
    // const interval = plan.interval || 'month';
    return `$${plan.price.toFixed(2)}`;
  }, []);

  const isCurrentPlan = useCallback(
    (planId: string): boolean => {
      return currentPlan?.id === planId;
    },
    [currentPlan]
  );

  const hasPaymentHistory = useCallback((): boolean => {
    return paymentHistory.length > 0;
  }, [paymentHistory]);

  const getLastSuccessfulPayment = useCallback(():
    | PaymentRecord
    | undefined => {
    return paymentHistory.find((payment) => payment.status === 'succeeded');
  }, [paymentHistory]);

  // Loading states - only include core functionality (plans)
  // Payment history loading is separate and optional
  const isLoading = plansLoading || paymentState.isLoading;
  const isProcessing = paymentState.isProcessing;

  // Error states - separate payment errors from plan loading errors
  // Plan loading errors don't block the UI since we have fallback plans
  const hasError = !!paymentError; // Only payment processing errors
  const errorMessage = paymentError;

  // Plan loading state - separate from payment errors
  const hasPlansError = !!plansError;
  const plansErrorMessage = (plansError as any)?.message;

  // Separate optional feature states
  const hasHistoryError = !!historyError;
  const historyErrorMessage = (historyError as any)?.message;

  return {
    // State
    paymentState,
    currentPlan,
    plans,
    paymentHistory,
    paymentMethods,
    lastPayment,

    // Loading states
    isLoading,
    isProcessing,
    plansLoading,
    historyLoading,
    methodsLoading,

    // Error states (payment processing only)
    hasError,
    errorMessage,
    paymentError,
    paymentSuccess,

    // Plan loading error states (non-blocking)
    hasPlansError,
    plansErrorMessage,

    // Optional feature error states
    hasHistoryError,
    historyErrorMessage,

    // Actions
    selectPlan,
    startPayment,
    clearPaymentError,
    clearPaymentSuccess,
    clearLoading,
    resetPayment,
    handlePaymentSuccess,
    handlePaymentError,
    retryPayment,
    cancelPayment,

    // Utility functions
    getPlanById,
    getPopularPlan,
    getFreePlan,
    formatPlanPrice,
    isCurrentPlan,
    hasPaymentHistory,
    getLastSuccessfulPayment,

    // Refetch functions (with safety checks)
    refetchPlans,
    refetchHistory: shouldFetchHistoryFinal
      ? refetchHistory
      : () => Promise.resolve(),
    refetchMethods,
  };
};

export default usePayment;

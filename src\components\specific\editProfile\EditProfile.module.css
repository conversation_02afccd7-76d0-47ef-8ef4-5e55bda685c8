.editProfile {
  color: #0e2f51;
  width: 100%;
  height: 100%;
  max-width: 500px;
  margin: auto;
  padding: 20px;
  border-bottom: 1px solid #e0e0e0;
  border-radius: 8px;
}

.editProfile h2 {
  text-align: center;
  margin-bottom: 20px;
  color: var();
}

.formGroup {
  margin-bottom: 15px;
}

.formGroup label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.formGroup input[type='text'],
.formGroup input[type='file'] {
  width: 100%;
  padding: 8px;
  box-sizing: border-box;
}

.profilePreview {
  margin-top: 10px;
  width: 100px;
  height: 100px;
  object-fit: cover;
  border-radius: 50%;
}

.buttonsContainer {
  display: flex;
  justify-content: space-between;
}

.saveButton,
.changePasswordButton {
  width: 45%;
  padding: 10px;
  background: #ffffff;
  border: 1px solid blue;
  color: blue;
  border-radius: 8px;
  cursor: pointer;
}

.saveButton:hover,
.changePasswordButton:hover,
.customFileInput:hover {
  color: #ffffff;
  background: blue;
}

.customFileInputLabel {
  display: flex;
  align-items: center;
}

.customFileInput {
  display: none;
}

.customFileInputButton {
  border: 1px solid blue;
  color: blue;
  padding: 8px 16px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 16px;
  display: inline-block;
}

.fileTypes,
.fileSize {
  font-size: 10px;
  font-family:
    system-ui,
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    Oxygen,
    Ubuntu,
    Cantarell,
    'Open Sans',
    'Helvetica Neue',
    sans-serif;
  font-weight: 300;
  margin-top: 5px;
  margin-left: 5px;
  color: #666;
}

.fileDetails {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-left: 15px;
}

.uploadImageContainer {
  display: flex;
  align-items: center;
}

.customFileInputButton:hover {
  background-color: blue;
  color: #ffffff;
}

.changePasswordButton:focus {
  outline: none;
  background-color: #174e86;
  color: #ffffff;
}

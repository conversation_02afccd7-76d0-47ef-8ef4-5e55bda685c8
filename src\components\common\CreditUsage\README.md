# CreditUsage Component

A React component that displays the user's credit usage information in a compact, user-friendly format.

## Features

- **Real-time Updates**: Automatically refreshes credit data every 30 seconds
- **Theme Support**: Fully supports both dark and light themes
- **Loading States**: Shows loading indicator while fetching data
- **Error Handling**: Gracefully handles API errors with retry functionality
- **Responsive Design**: Adapts to different screen sizes
- **Detailed Tooltips**: Shows comprehensive credit information on hover
- **Accessibility**: Includes proper ARIA labels and keyboard navigation

## Usage

### Basic Usage

```tsx
import CreditUsage from '../common/CreditUsage';

// Simple usage - gets user ID from localStorage
<CreditUsage />

// With details tooltip
<CreditUsage showDetails={true} />

// With custom user ID
<CreditUsage userId="custom-user-id" showDetails={true} />
```

### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `userId` | `string` | `undefined` | User ID to fetch credits for. If not provided, gets from localStorage |
| `showDetails` | `boolean` | `false` | Whether to show detailed credit information in tooltip |
| `className` | `string` | `undefined` | Additional CSS class name |

### Integration in Chat Header

The component is integrated into the ChatHeader component and positioned between the FreePlanNotice and ActionButtons:

```tsx
<FreePlanNotice storageKey="chatPageFreePlanNotice" variant="button" />
<CreditUsage showDetails={true} />
<ActionButtons>
```

## API Integration

The component uses the `useGetCreditUsageByUserQuery` hook from the credit service to fetch data from:
- Endpoint: `/credits/{userId}`
- Method: GET
- Polling: Every 30 seconds

## Data Structure

Expected API response structure:

```typescript
interface CreditUsage {
  userId: string;
  totalCredits: number;
  usedCredits: number;
  remainingCredits: number;
  lastUpdated?: string;
}
```

## Styling

The component uses Material-UI's styled components with theme-aware styling:
- Supports dark/light theme switching
- Responsive design for mobile devices
- Hover effects and transitions
- Color-coded credit status (success/warning/error)

## Error Handling

- **Loading State**: Shows spinner and "Loading..." text
- **Error State**: Shows "Credits: --" with retry functionality on click
- **No Data**: Shows "Credits: --" with appropriate tooltip
- **Network Issues**: Automatically retries with polling

## Accessibility

- Proper ARIA labels for screen readers
- Keyboard navigation support
- High contrast colors for better visibility
- Meaningful tooltips for additional context

// Main application tour definition
export const mainTour = {
  id: 'main-tour',
  name: 'Main Application Tour',
  routes: ['/'], // Only available on home page
  requiresNavigation: true, // Should navigate to home if not already there
  steps: [
    {
      element: '.headerContainer',
      popover: {
        title: 'Welcome to NEUQUIP',
        description: `
        This is where your journey starts. We’ll go through everything together — how to use the home screen and what you can do here.
        I’ll guide you step by step through the main features of the app
    `,
        position: 'bottom',
      },
    },
    {
      element: '#sidebar',
      popover: {
        title: 'Navigation Sidebar',
        description:
          'Use this sidebar to navigate between different sections of the application.',
        position: 'right',
      },
    },
    {
      // Home menu item
      element: 'a[href="/"]',
      popover: {
        title: 'Home',
        description:
          'This is where you can start a new chat or upload a file for analysis and access your recent projects.',
        position: 'right',
      },
    },
    {
      // My Projects menu item
      element: 'a[href="/my-projects"]',
      popover: {
        title: 'My Projects',
        description: 'View and manage all your projects.',
        position: 'right',
      },
    },
    {
      // Awaiting Actions menu item
      element: 'a[href="/awaiting-actions"]',
      popover: {
        title: 'Awaiting Actions',
        description:
          'Track tasks and approvals that require your attention. Stay on top of pending workflows and deadlines.',
        position: 'right',
      },
    },
    {
      // Sketchbooks menu item
      element: 'a[href="/sketchbooklists"]',
      popover: {
        title: 'Sketchbooks',
        description:
          'Create and manage visual presentations, charts, and diagrams. Use templates or start from scratch to visualize your data.',
        position: 'right',
      },
    },
    {
      element: '#childrenContainer',
      popover: {
        title: 'Main Content Area',
        description: `
        here you can:
        Start a direct text chat – Ask questions or talk to the AI instantly.
        Upload a file – Just drag and drop to get started.
        Upload a file with a prompt – Use a ready-made prompt or write your own.
        Access your most recent projects – Quickly open and continue where you left off.
        Everything you need is just one click away!
          `,
        position: 'left',
      },
    },
    {
      element: '#catagoryTabs',
      popover: {
        title: 'Category Tabs',
        description:
          'Explore different categories of predefined prompts to get started with your analysis.',
        position: 'left',
      },
    },
    {
      element: '#academic_research',
      popover: {
        title: 'Academic Research',
        description:
          'Select this category to access prompts related to academic research and analysis.',
        // position: 'left',
        onNextClick: (element: any, step: any, { driver }: any) => {
          // Custom logic before moving to the next step
          const academicOption = document.querySelector('#academic_research');
          if (academicOption instanceof HTMLElement) {
            academicOption.click();
          }

          // Proceed to the next step
          driver.moveNext();
        },
      },
    },
    {
      element: '#predefinedPrompts',
      popover: {
        title: 'Predefined Prompts',
        description:
          'Choose from a variety of predefined prompts to get started with your analysis.',
        position: 'left',
      },
    },
    {
      element: '#data-extraction',
      popover: {
        title: 'Data Extraction & Statistical Analysis',
        description:
          'Select this prompt to extract research data points and perform statistical analysis.',
        position: 'left',
        onNextClick: (element: any, step: any, { driver }: any) => {
          // Custom logic before moving to the next step
          const extractKyePoints = document.querySelector('#data-extraction');
          if (extractKyePoints instanceof HTMLElement) {
            extractKyePoints.click();
          }

          // Proceed to the next step
          driver.moveNext();
        },
      },
    },
    {
      element: '#dropzone-container',
      popover: {
        title: 'File Upload',
        description: 'Upload your file to get started with your analysis.',
        position: 'left',
      },
    },
    {
      element: '#availablePrompts',
      popover: {
        title: 'Available Prompts',
        description: 'Select the prompt you want to use for your analysis.',
        position: 'left',
      },
    },
    {
      element: '#customPromptInput',
      popover: {
        title: 'Custom Prompt',
        description: 'Or write your own prompt if you prefer.',
        position: 'left',
      },
    },
    {
      element: '#createProjectButton',
      popover: {
        title: 'Create Project',
        description:
          'Click this button to create a new project with your prompt.',
        position: 'left',
      },
    },
  ],
};

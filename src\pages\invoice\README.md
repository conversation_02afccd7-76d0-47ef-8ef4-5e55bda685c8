# Invoice Page Implementation

## Overview
The Invoice page provides users with a comprehensive view of their payment history and invoices. It integrates with the existing payment API to display paginated payment records with filtering, sorting, and export capabilities.

## Features

### 🔍 **Payment History Display**
- Paginated table showing all user payments
- Real-time data fetching from the backend API
- Responsive design for desktop and mobile

### 🎛️ **Advanced Filtering**
- Filter by payment status (succeeded, failed, pending, etc.)
- Filter by plan ID
- Date range filtering (from/to dates)
- Quick filter buttons for common scenarios
- Filter reset functionality

### 📊 **Status Indicators**
- Color-coded status badges for easy identification
- Icons for different payment states
- Hover tooltips with detailed information

### 📤 **Export Functionality**
- Export payment data as CSV
- Individual receipt download for successful payments
- Bulk export with proper CSV formatting

### 🎨 **Theme Support**
- Full dark/light theme compatibility
- Consistent styling with the rest of the application
- Accessible color schemes

## File Structure

```
src/pages/invoice/
├── InvoicePage.tsx              # Main invoice page component
├── InvoicePage.module.css       # Page-specific styles
└── README.md                    # This documentation

src/components/invoice/
├── InvoiceTable.tsx             # Payment table component
├── InvoiceTable.module.css      # Table styles
├── InvoiceFilters.tsx           # Filter controls component
├── InvoiceFilters.module.css    # Filter styles
├── InvoiceStatusBadge.tsx       # Status badge component
└── InvoiceStatusBadge.module.css # Badge styles
```

## API Integration

### Endpoint Used
- `GET /api/v1/payments/get-all` - Fetches paginated payment history

### Query Parameters
- `page` - Page number (default: 1)
- `size` - Items per page (default: 10)
- `userId` - Filter by user ID (automatically set)
- `planId` - Filter by plan ID (optional)
- `status` - Filter by payment status (optional)

### Response Format
```typescript
{
  timestamp: string;
  path: string;
  status: number;
  success: boolean;
  message: string;
  error: string;
  data: {
    content: PaymentRecord[];
    pageSize: number;
    currentPage: number;
    totalItems: number;
    totalPages: number;
  };
}
```

## Components

### InvoicePage
Main container component that:
- Manages state for pagination and filtering
- Handles API calls using RTK Query
- Provides export functionality
- Renders child components

### InvoiceTable
Table component that:
- Displays payment records in a responsive table
- Handles pagination controls
- Provides action buttons for each payment
- Shows loading and empty states

### InvoiceFilters
Filter component that:
- Provides expandable filter controls
- Manages filter state
- Offers quick filter buttons
- Shows active filter count

### InvoiceStatusBadge
Status indicator component that:
- Displays color-coded status badges
- Shows appropriate icons for each status
- Supports all payment statuses

## Usage

### Navigation
Users can access the invoice page through:
1. Sidebar navigation: Account & Billing → Invoices
2. Direct URL: `/invoices`

### Filtering Payments
1. Click the "Filters" button to expand filter options
2. Select desired filters (plan, status, date range)
3. Use quick filter buttons for common scenarios
4. Click "Clear All" to reset filters

### Exporting Data
1. **CSV Export**: Click "Export CSV" button in the header
2. **Individual Receipts**: Click download icon for successful payments

## Error Handling

- **Authentication**: Redirects to login if user is not authenticated
- **API Errors**: Shows user-friendly error messages
- **Empty States**: Displays helpful messages when no data is found
- **Loading States**: Shows loading indicators during API calls

## Responsive Design

The invoice page is fully responsive and works on:
- Desktop computers (1200px+)
- Tablets (768px - 1199px)
- Mobile phones (320px - 767px)

## Accessibility

- Semantic HTML structure
- ARIA labels and roles
- Keyboard navigation support
- High contrast mode support
- Screen reader compatibility

## Future Enhancements

Potential improvements that could be added:
- PDF invoice generation
- Email invoice functionality
- Advanced search capabilities
- Payment retry functionality
- Subscription management integration
- Invoice templates customization

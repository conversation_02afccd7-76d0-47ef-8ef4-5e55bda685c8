/* Main Container - Stripe-like clean design */
.container {
  max-width: 500px;
  margin: 0 auto;
  padding: 0;
  background: transparent;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* Plan Summary Card */
.planCard {
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.planHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.planName {
  color: var(--color-text-primary);
  font-weight: 600;
  margin: 0;
  font-size: 1.125rem;
}

.planPrice {
  color: var(--color-text-primary);
  font-weight: 700;
  margin: 0;
  font-size: 1.75rem;
  display: flex;
  align-items: baseline;
  gap: 0.25rem;
}

.planInterval {
  font-size: 1rem;
  font-weight: 400;
  color: var(--color-text-secondary);
}

.planDetails {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.planDescription {
  color: var(--color-text-secondary);
  margin: 0;
  line-height: 1.5;
}

.planFeatures {
  color: var(--color-text-secondary);
  margin: 0;
  font-size: 0.875rem;
  opacity: 0.8;
}

/* Payment Form */
.paymentForm {
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* Form Sections */
.formSection {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.inputField {
  margin: 0;
}

/* Card Information Styling */
.cardLabel {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--color-text-primary);
  margin-bottom: 0.5rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.required {
  color: var(--color-error);
  margin-left: 0.25rem;
}

.cardContainer {
  position: relative;
  padding: 0.875rem 1rem;
  border: 1px solid var(--color-border);
  border-radius: 6px;
  background: var(--color-background);
  transition: all 0.2s ease;
  min-height: 44px;
  display: flex;
  align-items: center;
}

/* Card Container States */
.cardContainer:focus-within {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 1px var(--color-primary);
}

.cardContainer.cardError {
  border-color: var(--color-error);
}

.cardContainer.cardComplete {
  border-color: var(--color-success, #22c55e);
}

.cardElement {
  width: 100%;
  height: 20px;
}

/* Field Error */
.fieldError {
  color: var(--color-error);
  font-size: 0.875rem;
  margin-top: 0.5rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Error Alert */
.errorContainer {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin: 0 0 1rem 0;
}

.errorAlert {
  margin: 0;
  border-radius: 6px;
  font-size: 0.875rem;
}

.retryButton {
  align-self: flex-start;
  font-size: 0.75rem;
  padding: 0.375rem 0.75rem;
  border-color: var(--color-primary);
  color: var(--color-primary);
  text-transform: none;
  font-weight: 500;
}

.retryButton:hover {
  background: rgba(var(--color-primary-rgb, 99, 91, 255), 0.08);
  border-color: var(--color-primary);
}

.retrySpinner {
  margin-right: 0.375rem;
  color: inherit;
}

.errorActions {
  display: flex;
  gap: 0.75rem;
  align-items: center;
  flex-wrap: wrap;
}

.diagnosticsButton {
  font-size: 0.75rem;
  padding: 0.375rem 0.75rem;
  color: var(--color-text-secondary);
  text-transform: none;
  font-weight: 500;
}

.diagnosticsButton:hover {
  background: rgba(var(--color-text-secondary-rgb, 107, 114, 128), 0.08);
  color: var(--color-text-primary);
}

/* Pay Button - Stripe-like styling */
.payButton {
  background: var(--color-primary, #635bff);
  color: white;
  font-weight: 600;
  font-size: 1rem;
  padding: 0.875rem 1rem;
  border-radius: 6px;
  text-transform: none;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  min-height: 48px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.payButton:hover:not(:disabled) {
  background: var(--color-primary-dark, #5a52e8);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.payButton:disabled {
  background: var(--color-disabled, #e5e7eb);
  color: var(--color-text-disabled, #9ca3af);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.lockIcon {
  font-size: 0.875rem;
  opacity: 0.8;
}

.buttonSpinner {
  margin-right: 0.5rem;
  color: inherit;
}

/* Security Footer */
.securityFooter {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  margin-top: 0.5rem;
  padding-top: 1rem;
  border-top: 1px solid var(--color-border);
  font-size: 0.75rem;
  color: var(--color-text-secondary);
}

.securityItem {
  display: flex;
  align-items: center;
  gap: 0.375rem;
}

.securityIcon {
  font-size: 0.75rem;
  opacity: 0.7;
}

.securityDivider {
  opacity: 0.5;
}

/* Dark Theme Support */
[data-theme='dark'] .planCard,
[data-theme='dark'] .paymentForm {
  background: var(--color-background-secondary, #1f2937);
  border-color: var(--color-border, #374151);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

[data-theme='dark'] .planName,
[data-theme='dark'] .planPrice {
  color: var(--color-text-primary, #f9fafb);
}

[data-theme='dark'] .planDescription,
[data-theme='dark'] .planFeatures {
  color: var(--color-text-secondary, #d1d5db);
}

[data-theme='dark'] .cardLabel {
  color: var(--color-text-primary, #f9fafb);
}

[data-theme='dark'] .cardContainer {
  background: var(--color-background-secondary, #1f2937);
  border-color: var(--color-border, #374151);
}

[data-theme='dark'] .cardContainer:focus-within {
  border-color: var(--color-primary, #635bff);
  box-shadow: 0 0 0 1px var(--color-primary, #635bff);
}

[data-theme='dark'] .payButton {
  background: var(--color-primary, #635bff);
}

[data-theme='dark'] .payButton:hover:not(:disabled) {
  background: var(--color-primary-dark, #5a52e8);
}

[data-theme='dark'] .securityFooter {
  border-color: var(--color-border, #374151);
  color: var(--color-text-secondary, #d1d5db);
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    max-width: 100%;
    margin: 0 1rem;
    gap: 1rem;
  }

  .planCard,
  .paymentForm {
    padding: 1rem;
  }

  .planHeader {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .planPrice {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .container {
    margin: 0 0.5rem;
  }

  .planCard,
  .paymentForm {
    padding: 0.875rem;
    border-radius: 8px;
  }

  .planPrice {
    font-size: 1.375rem;
  }

  .payButton {
    padding: 1rem;
    font-size: 0.9375rem;
  }

  .securityFooter {
    font-size: 0.6875rem;
    gap: 0.5rem;
  }
}

/* Loading Animation */
.buttonSpinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Focus States for Accessibility */
.cardContainer:focus-within {
  outline: none;
}

.inputField:focus-within {
  outline: none;
}

/* Form Animation */
.container {
  animation: slideUp 0.4s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(16px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Hover Effects */
.planCard:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);
}

[data-theme='dark'] .planCard:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
}

/* Loading State for Pay Button */
.payButton:disabled .buttonSpinner {
  animation: spin 1s linear infinite;
}

/* Improved Focus Indicators */
.cardContainer:focus-within,
.payButton:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

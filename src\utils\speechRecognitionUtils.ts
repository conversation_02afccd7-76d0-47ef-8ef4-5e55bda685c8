/**
 * Utility functions for speech recognition diagnostics and support detection
 */

export interface SpeechRecognitionDiagnostics {
  isSupported: boolean;
  isSecureContext: boolean;
  hasPermission: boolean | null;
  browserName: string;
  issues: string[];
  recommendations: string[];
}

/**
 * Detects browser name for better error messages
 */
export const getBrowserName = (): string => {
  const userAgent = navigator.userAgent;
  
  if (userAgent.includes('Chrome')) return 'Chrome';
  if (userAgent.includes('Firefox')) return 'Firefox';
  if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) return 'Safari';
  if (userAgent.includes('Edge')) return 'Edge';
  if (userAgent.includes('Opera')) return 'Opera';
  
  return 'Unknown';
};

/**
 * Checks if the current context supports speech recognition
 */
export const checkSpeechRecognitionSupport = (): boolean => {
  return 'webkitSpeechRecognition' in window || 'SpeechRecognition' in window;
};

/**
 * Checks if the current context is secure (HTTPS or localhost)
 */
export const checkSecureContext = (): boolean => {
  return (
    window.location.protocol === 'https:' ||
    window.location.hostname === 'localhost' ||
    window.location.hostname === '127.0.0.1' ||
    window.location.hostname.endsWith('.local')
  );
};

/**
 * Checks microphone permission status
 */
export const checkMicrophonePermission = async (): Promise<boolean | null> => {
  try {
    if (!navigator.permissions) {
      return null; // Permission API not supported
    }
    
    const permission = await navigator.permissions.query({ 
      name: 'microphone' as PermissionName 
    });
    
    return permission.state === 'granted';
  } catch (error) {
    console.warn('Could not check microphone permission:', error);
    return null;
  }
};

/**
 * Comprehensive diagnostics for speech recognition
 */
export const diagnoseSpeechRecognition = async (): Promise<SpeechRecognitionDiagnostics> => {
  const isSupported = checkSpeechRecognitionSupport();
  const isSecureContext = checkSecureContext();
  const hasPermission = await checkMicrophonePermission();
  const browserName = getBrowserName();
  
  const issues: string[] = [];
  const recommendations: string[] = [];

  // Check for issues and provide recommendations
  if (!isSupported) {
    issues.push('Speech recognition API not supported');
    recommendations.push('Try using Chrome, Edge, or Safari browser');
  }

  if (!isSecureContext) {
    issues.push('Insecure context (HTTP instead of HTTPS)');
    recommendations.push('Use HTTPS or access via localhost for speech recognition to work');
  }

  if (hasPermission === false) {
    issues.push('Microphone permission denied');
    recommendations.push('Allow microphone access in browser settings');
  }

  if (browserName === 'Firefox') {
    issues.push('Firefox has limited speech recognition support');
    recommendations.push('For best experience, use Chrome or Edge');
  }

  // Success case
  if (isSupported && isSecureContext && hasPermission !== false) {
    recommendations.push('Speech recognition should work properly');
  }

  return {
    isSupported,
    isSecureContext,
    hasPermission,
    browserName,
    issues,
    recommendations
  };
};

/**
 * Logs speech recognition diagnostics to console
 */
export const logSpeechRecognitionDiagnostics = async (): Promise<void> => {
  const diagnostics = await diagnoseSpeechRecognition();
  
  console.group('🎤 Speech Recognition Diagnostics');
  console.log('Browser:', diagnostics.browserName);
  console.log('API Supported:', diagnostics.isSupported);
  console.log('Secure Context:', diagnostics.isSecureContext);
  console.log('Microphone Permission:', diagnostics.hasPermission);
  
  if (diagnostics.issues.length > 0) {
    console.warn('Issues found:', diagnostics.issues);
  }
  
  if (diagnostics.recommendations.length > 0) {
    console.info('Recommendations:', diagnostics.recommendations);
  }
  
  console.groupEnd();
};

/**
 * Gets user-friendly error message for speech recognition issues
 */
export const getSpeechRecognitionErrorMessage = (error: any): string => {
  const errorMessage = error instanceof Error ? error.message : String(error);
  
  if (errorMessage.includes('not-allowed') || errorMessage.includes('permission')) {
    return 'Microphone permission denied. Please allow microphone access and try again.';
  }
  
  if (errorMessage.includes('network')) {
    return 'Network error. Please check your internet connection.';
  }
  
  if (errorMessage.includes('service-not-allowed')) {
    return 'Speech recognition service is not available. Please try again later.';
  }
  
  if (errorMessage.includes('no-speech')) {
    return 'No speech detected. Please try speaking again.';
  }
  
  if (errorMessage.includes('audio-capture')) {
    return 'Could not access microphone. Please check your microphone settings.';
  }
  
  if (errorMessage.includes('not-supported')) {
    return 'Speech recognition is not supported in this browser. Try Chrome, Edge, or Safari.';
  }
  
  return 'Could not start speech recognition. Please try again.';
};

/**
 * Creates a toast message with speech recognition setup instructions
 */
export const createSpeechRecognitionSetupToast = (diagnostics: SpeechRecognitionDiagnostics): string => {
  if (!diagnostics.isSecureContext) {
    return 'Speech recognition requires HTTPS. Please use a secure connection.';
  }
  
  if (!diagnostics.isSupported) {
    return `Speech recognition is not supported in ${diagnostics.browserName}. Try Chrome, Edge, or Safari.`;
  }
  
  if (diagnostics.hasPermission === false) {
    return 'Please allow microphone access in your browser settings to use voice input.';
  }
  
  return 'Speech recognition is ready to use!';
};

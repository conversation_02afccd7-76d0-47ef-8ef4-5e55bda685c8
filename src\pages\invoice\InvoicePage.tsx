import React, { useState, useEffect } from 'react';
import { useGetPaymentHistoryQuery } from '../../services/paymentService';
import { getCurrentUserId } from '../../utils/auth/userHelpers';
import LoadingSpinner from '../../components/common/loading/LoadingSpinner';
import { useTheme } from '../../contexts/ThemeContext';
import styles from './InvoicePage.module.css';
import InvoiceTable from '../../components/invoice/InvoiceTable';
import InvoiceFilters from '../../components/invoice/InvoiceFilters';
import { PaymentRecord } from '../../types/payment';

interface InvoiceFilters {
  planId?: string;
  status?: string;
  dateFrom?: string;
  dateTo?: string;
}

const InvoicePage: React.FC = () => {
  const { isDarkMode } = useTheme();
  const currentUserId = getCurrentUserId();

  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [filters, setFilters] = useState<InvoiceFilters>({});

  // Build query parameters
  const queryParams = {
    page: currentPage,
    size: pageSize,
    userId: currentUserId || undefined,
    ...filters,
  };

  const {
    data: paymentData,
    isLoading,
    error,
    refetch,
  } = useGetPaymentHistoryQuery(queryParams, {
    skip: !currentUserId,
  });

  // Handle filter changes
  const handleFilterChange = (newFilters: InvoiceFilters) => {
    setFilters(newFilters);
    setCurrentPage(1); // Reset to first page when filters change
  };

  // Handle page changes
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Handle page size changes
  const handlePageSizeChange = (size: number) => {
    setPageSize(size);
    setCurrentPage(1); // Reset to first page when page size changes
  };

  // Refresh data
  const handleRefresh = () => {
    refetch();
  };

  // Export data as CSV
  const handleExportCSV = () => {
    if (!paymentData?.content || paymentData.content.length === 0) {
      alert('No data to export');
      return;
    }

    try {
      const headers = [
        'Payment ID',
        'Date',
        'Amount',
        'Currency',
        'Status',
        'Plan ID',
      ];
      const csvContent = [
        headers.join(','),
        ...paymentData.content.map((payment) =>
          [
            `"${payment.paymentIntentId || payment.id}"`,
            `"${payment.dated || payment.createdOn || ''}"`,
            payment.amount,
            payment.currency.toUpperCase(),
            payment.status,
            `"${payment.planId}"`,
          ].join(',')
        ),
      ].join('\n');

      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `invoices-${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error exporting CSV:', error);
      alert('Failed to export data. Please try again.');
    }
  };

  if (!currentUserId) {
    return (
      <div className={styles.container}>
        <div className={styles.errorMessage}>
          <h2>Authentication Required</h2>
          <p>Please log in to view your invoices.</p>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className={styles.container}>
        <LoadingSpinner fullScreen size="large" message="Loading invoices..." />
      </div>
    );
  }

  if (error) {
    return (
      <div className={styles.container}>
        <div className={styles.errorMessage}>
          <h2>Error Loading Invoices</h2>
          <p>Unable to load your payment history. Please try again later.</p>
          <button onClick={handleRefresh} className={styles.retryButton}>
            Retry
          </button>
        </div>
      </div>
    );
  }

  const payments = paymentData?.content || [];
  const totalItems = paymentData?.totalItems || 0;
  const totalPages = paymentData?.totalPages || 1;

  return (
    <div className={`${styles.container} ${isDarkMode ? styles.dark : ''}`}>
      <div className={styles.header}>
        <div className={styles.titleSection}>
          <h1 className={styles.title}>Invoices & Payment History</h1>
          <p className={styles.subtitle}>
            View and manage your payment history and invoices
          </p>
        </div>
        <div className={styles.headerActions}>
          <button onClick={handleExportCSV} className={styles.exportButton}>
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
            </svg>
            Export CSV
          </button>
          <button onClick={handleRefresh} className={styles.refreshButton}>
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z" />
            </svg>
            Refresh
          </button>
        </div>
      </div>

      <div className={styles.content}>
        <InvoiceFilters
          filters={filters}
          onFilterChange={handleFilterChange}
          onReset={() => handleFilterChange({})}
        />

        <div className={styles.tableSection}>
          <div className={styles.tableHeader}>
            <div className={styles.tableInfo}>
              <span className={styles.totalCount}>
                {totalItems} {totalItems === 1 ? 'payment' : 'payments'} found
              </span>
            </div>
            <div className={styles.tableControls}>
              <select
                value={pageSize}
                onChange={(e) => handlePageSizeChange(Number(e.target.value))}
                className={styles.pageSizeSelect}
              >
                <option value={5}>5 per page</option>
                <option value={10}>10 per page</option>
                <option value={25}>25 per page</option>
                <option value={50}>50 per page</option>
              </select>
            </div>
          </div>

          <InvoiceTable
            payments={payments}
            isLoading={isLoading}
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={handlePageChange}
          />
        </div>
      </div>
    </div>
  );
};

export default InvoicePage;

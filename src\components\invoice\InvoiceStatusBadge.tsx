import React from 'react';
import { useTheme } from '../../contexts/ThemeContext';
import styles from './InvoiceStatusBadge.module.css';

interface InvoiceStatusBadgeProps {
  status: string;
}

const InvoiceStatusBadge: React.FC<InvoiceStatusBadgeProps> = ({ status }) => {
  const { isDarkMode } = useTheme();

  const getStatusConfig = (status: string) => {
    switch (status.toLowerCase()) {
      case 'succeeded':
        return {
          label: 'Succeeded',
          className: 'success',
          icon: '✓',
        };
      case 'failed':
        return {
          label: 'Failed',
          className: 'error',
          icon: '✗',
        };
      case 'pending':
        return {
          label: 'Pending',
          className: 'warning',
          icon: '⏳',
        };
      case 'created':
        return {
          label: 'Created',
          className: 'info',
          icon: '○',
        };
      case 'canceled':
      case 'cancelled':
        return {
          label: 'Canceled',
          className: 'neutral',
          icon: '⊘',
        };
      case 'processing':
        return {
          label: 'Processing',
          className: 'warning',
          icon: '⟳',
        };
      case 'requires_action':
        return {
          label: 'Action Required',
          className: 'warning',
          icon: '!',
        };
      case 'requires_payment_method':
        return {
          label: 'Payment Method Required',
          className: 'warning',
          icon: '💳',
        };
      default:
        return {
          label: status.charAt(0).toUpperCase() + status.slice(1),
          className: 'neutral',
          icon: '?',
        };
    }
  };

  const config = getStatusConfig(status);

  return (
    <span
      className={`${styles.badge} ${styles[config.className]} ${
        isDarkMode ? styles.dark : ''
      }`}
      title={`Payment status: ${config.label}`}
    >
      <span className={styles.icon}>{config.icon}</span>
      <span className={styles.label}>{config.label}</span>
    </span>
  );
};

export default InvoiceStatusBadge;

import React from 'react';
import {
  Box,
  Container,
  Typography,
  Button,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  RocketLaunch as RocketIcon,
  ArrowBack as ArrowBackIcon,
  Schedule as ScheduleIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useTheme } from '../contexts/ThemeContext';

const TempScreen: React.FC = () => {
  const navigate = useNavigate();
  const { isDarkMode } = useTheme();

  const handleGoBack = () => {
    navigate(-1);
  };

  const handleGoHome = () => {
    navigate('/');
  };

  return (
    <div
      style={{
        minHeight: 'calc(100vh - 60px)',
        background: isDarkMode
          ? 'var(--color-background-primary)'
          : 'var(--color-background-primary)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: 'var(--spacing-4)',
        transition: 'background-color 0.3s ease',
      }}
    >
      <Container maxWidth="md">
        <Box
          sx={{
            width: '100%',
            textAlign: 'center',
            position: 'relative',
            overflow: 'hidden',
          }}
        >
          {/* Background Animation Elements */}
          {[...Array(3)].map((_, index) => (
            <Box
              key={index}
              sx={{
                position: 'absolute',
                width: { xs: '80px', md: '120px' },
                height: { xs: '80px', md: '120px' },
                borderRadius: '50%',
                background: isDarkMode
                  ? 'linear-gradient(45deg, var(--color-primary), var(--color-secondary))'
                  : 'linear-gradient(45deg, var(--color-primary), var(--color-secondary))',
                filter: 'blur(40px)',
                opacity: 0.08,
                animation: `float ${3 + index}s infinite ease-in-out`,
                top: `${Math.random() * 100}%`,
                left: `${Math.random() * 100}%`,
                zIndex: -1,
              }}
            />
          ))}

          {/* Main Content */}
          <Box
            sx={{
              padding: { xs: 4, md: 6 },
              borderRadius: 'var(--radius-2xl)',
              background: isDarkMode
                ? 'var(--color-background-card)'
                : 'var(--color-background-card)',
              boxShadow: isDarkMode
                ? '0 8px 32px rgba(0, 0, 0, 0.3)'
                : '0 8px 32px rgba(0, 0, 0, 0.08)',
              border: isDarkMode
                ? '1px solid var(--color-border)'
                : '1px solid var(--color-border)',
              position: 'relative',
              zIndex: 1,
              transition: 'all 0.3s ease',
            }}
          >
            {/* Rocket Icon with Animation */}
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'center',
                marginBottom: 3,
                animation: 'bounce 2s infinite',
              }}
            >
              <RocketIcon
                sx={{
                  fontSize: { xs: 60, md: 80 },
                  color: 'var(--color-primary)',
                  filter: 'drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1))',
                }}
              />
            </Box>

            {/* Main Heading */}
            <Typography
              variant="h2"
              sx={{
                fontSize: { xs: '2.5rem', md: '3.5rem' },
                fontWeight: 800,
                background: isDarkMode
                  ? 'linear-gradient(45deg, var(--color-primary), var(--color-secondary))'
                  : 'linear-gradient(45deg, var(--color-primary), var(--color-secondary))',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                marginBottom: 2,
                fontFamily: 'var(--font-family-primary)',
              }}
            >
              Coming Soon
            </Typography>

            {/* Subtitle */}
            <Typography
              variant="h5"
              sx={{
                fontWeight: 600,
                color: 'var(--color-text-primary)',
                marginBottom: 3,
                fontFamily: 'var(--font-family-primary)',
              }}
            >
              Something Amazing is on the Way!
            </Typography>

            {/* Description */}
            <Typography
              variant="body1"
              sx={{
                color: 'var(--color-text-secondary)',
                marginBottom: 4,
                maxWidth: '500px',
                margin: '0 auto 2rem',
                lineHeight: 1.6,
                fontSize: { xs: '1rem', md: '1.125rem' },
                fontFamily: 'var(--font-family-primary)',
              }}
            >
              We're working hard to bring you an incredible new feature that
              will enhance your experience. Stay tuned for updates and get ready
              to be amazed!
            </Typography>

            {/* Feature Highlight */}
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'center',
                marginBottom: 4,
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <ScheduleIcon
                  sx={{
                    color: 'var(--color-primary)',
                    fontSize: '1.25rem',
                  }}
                />
                <Typography
                  variant="body2"
                  sx={{
                    color: 'var(--color-text-secondary)',
                    fontFamily: 'var(--font-family-primary)',
                    fontSize: 'var(--font-size-sm)',
                  }}
                >
                  Coming Soon
                </Typography>
              </Box>
            </Box>

            {/* Action Buttons */}
            <Box
              sx={{
                display: 'flex',
                gap: 2,
                justifyContent: 'center',
                flexWrap: 'wrap',
              }}
            >
              <Button
                variant="contained"
                onClick={handleGoHome}
                sx={{
                  backgroundColor: 'var(--color-primary)',
                  color: 'var(--color-primary-contrast)',
                  padding: 'var(--spacing-3) var(--spacing-6)',
                  borderRadius: 'var(--radius-lg)',
                  fontWeight: 600,
                  textTransform: 'none',
                  fontSize: 'var(--font-size-base)',
                  fontFamily: 'var(--font-family-primary)',
                  boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
                  '&:hover': {
                    backgroundColor: 'var(--color-primary-dark)',
                    transform: 'translateY(-1px)',
                    boxShadow: '0 2px 6px rgba(0, 0, 0, 0.15)',
                  },
                  transition: 'all 0.2s ease',
                }}
              >
                Go to Home
              </Button>

              <Tooltip title="Go back to previous page">
                <IconButton
                  onClick={handleGoBack}
                  sx={{
                    backgroundColor: 'var(--color-background-tertiary)',
                    color: 'var(--color-text-primary)',
                    border: '1px solid var(--color-border)',
                    padding: 'var(--spacing-3)',
                    '&:hover': {
                      backgroundColor: 'var(--color-primary-light)',
                      color: 'var(--color-primary-contrast)',
                      transform: 'translateY(-1px)',
                    },
                    transition: 'all 0.2s ease',
                  }}
                >
                  <ArrowBackIcon />
                </IconButton>
              </Tooltip>
            </Box>
          </Box>

          {/* CSS Animations */}
          <style>
            {`
              @keyframes float {
                0%, 100% {
                  transform: translateY(0) rotate(0deg);
                  opacity: 0.08;
                }
                50% {
                  transform: translateY(-20px) rotate(10deg);
                  opacity: 0.12;
                }
              }
              @keyframes bounce {
                0%, 20%, 50%, 80%, 100% {
                  transform: translateY(0);
                }
                40% {
                  transform: translateY(-10px);
                }
                60% {
                  transform: translateY(-5px);
                }
              }
            `}
          </style>
        </Box>
      </Container>
    </div>
  );
};

export default TempScreen;

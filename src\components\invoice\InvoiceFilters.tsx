import React, { useState } from 'react';
import { useTheme } from '../../contexts/ThemeContext';
import { useGetPaymentPlansQuery } from '../../services/paymentService';
import styles from './InvoiceFilters.module.css';

interface InvoiceFiltersProps {
  filters: {
    planId?: string;
    status?: string;
    dateFrom?: string;
    dateTo?: string;
  };
  onFilterChange: (filters: any) => void;
  onReset: () => void;
}

const InvoiceFilters: React.FC<InvoiceFiltersProps> = ({
  filters,
  onFilterChange,
  onReset,
}) => {
  const { isDarkMode } = useTheme();
  const [isExpanded, setIsExpanded] = useState(false);

  // Get available plans for filter dropdown
  const { data: plans = [] } = useGetPaymentPlansQuery();

  const statusOptions = [
    { value: '', label: 'All Statuses' },
    { value: 'succeeded', label: 'Succeeded' },
    { value: 'failed', label: 'Failed' },
    { value: 'pending', label: 'Pending' },
    { value: 'created', label: 'Created' },
    { value: 'canceled', label: 'Canceled' },
    { value: 'processing', label: 'Processing' },
  ];

  const handleFilterChange = (key: string, value: string) => {
    const newFilters = {
      ...filters,
      [key]: value || undefined,
    };
    onFilterChange(newFilters);
  };

  const hasActiveFilters = Object.values(filters).some((value) => value);

  const getFilterCount = () => {
    return Object.values(filters).filter((value) => value).length;
  };

  return (
    <div className={`${styles.container} ${isDarkMode ? styles.dark : ''}`}>
      <div className={styles.header}>
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className={styles.toggleButton}
        >
          <svg
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="currentColor"
            className={`${styles.toggleIcon} ${isExpanded ? styles.expanded : ''}`}
          >
            <path d="M7 10l5 5 5-5z" />
          </svg>
          <span>Filters</span>
          {hasActiveFilters && (
            <span className={styles.filterCount}>{getFilterCount()}</span>
          )}
        </button>

        {hasActiveFilters && (
          <button onClick={onReset} className={styles.resetButton}>
            <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
              <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z" />
            </svg>
            Clear All
          </button>
        )}
      </div>

      {isExpanded && (
        <div className={styles.filtersContent}>
          <div className={styles.filtersGrid}>
            {/* Plan Filter */}
            <div className={styles.filterGroup}>
              <label className={styles.filterLabel}>Plan</label>
              <select
                value={filters.planId || ''}
                onChange={(e) => handleFilterChange('planId', e.target.value)}
                className={styles.filterSelect}
              >
                <option value="">All Plans</option>
                {plans.map((plan: any) => (
                  <option key={plan.id} value={plan.id}>
                    {plan.name} - ${plan.price}
                  </option>
                ))}
              </select>
            </div>

            {/* Status Filter */}
            <div className={styles.filterGroup}>
              <label className={styles.filterLabel}>Status</label>
              <select
                value={filters.status || ''}
                onChange={(e) => handleFilterChange('status', e.target.value)}
                className={styles.filterSelect}
              >
                {statusOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Date From Filter */}
            <div className={styles.filterGroup}>
              <label className={styles.filterLabel}>From Date</label>
              <input
                type="date"
                value={filters.dateFrom || ''}
                onChange={(e) => handleFilterChange('dateFrom', e.target.value)}
                className={styles.filterInput}
                max={filters.dateTo || new Date().toISOString().split('T')[0]}
              />
            </div>

            {/* Date To Filter */}
            <div className={styles.filterGroup}>
              <label className={styles.filterLabel}>To Date</label>
              <input
                type="date"
                value={filters.dateTo || ''}
                onChange={(e) => handleFilterChange('dateTo', e.target.value)}
                className={styles.filterInput}
                min={filters.dateFrom}
                max={new Date().toISOString().split('T')[0]}
              />
            </div>
          </div>

          {/* Quick Filter Buttons */}
          <div className={styles.quickFilters}>
            <span className={styles.quickFiltersLabel}>Quick filters:</span>
            <div className={styles.quickFilterButtons}>
              <button
                onClick={() => handleFilterChange('status', 'succeeded')}
                className={`${styles.quickFilterButton} ${
                  filters.status === 'succeeded' ? styles.active : ''
                }`}
              >
                Successful
              </button>
              <button
                onClick={() => handleFilterChange('status', 'failed')}
                className={`${styles.quickFilterButton} ${
                  filters.status === 'failed' ? styles.active : ''
                }`}
              >
                Failed
              </button>
              <button
                onClick={() => handleFilterChange('status', 'pending')}
                className={`${styles.quickFilterButton} ${
                  filters.status === 'pending' ? styles.active : ''
                }`}
              >
                Pending
              </button>
              <button
                onClick={() => {
                  const lastMonth = new Date();
                  lastMonth.setMonth(lastMonth.getMonth() - 1);
                  handleFilterChange(
                    'dateFrom',
                    lastMonth.toISOString().split('T')[0]
                  );
                }}
                className={styles.quickFilterButton}
              >
                Last 30 Days
              </button>
              <button
                onClick={() => {
                  const lastYear = new Date();
                  lastYear.setFullYear(lastYear.getFullYear() - 1);
                  handleFilterChange(
                    'dateFrom',
                    lastYear.toISOString().split('T')[0]
                  );
                }}
                className={styles.quickFilterButton}
              >
                Last Year
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default InvoiceFilters;

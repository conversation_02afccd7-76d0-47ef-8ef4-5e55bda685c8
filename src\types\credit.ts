/**
 * Credit usage and balance types
 */

export interface CreditUsage {
  id?: string;
  userId: string;
  totalCredits: number;
  usedCredits: number;
  remainingCredits: number;
  lastUpdated?: string;
  createdOn?: string;
  updatedOn?: string;
  credits?: string;
}

export interface CreditUsageByResponse {
  id?: string;
  responseId: string;
  creditsUsed: number;
  timestamp?: string;
  createdOn?: string;
}

export interface CreditUsageByProject {
  id?: string;
  projectId: string;
  totalCreditsUsed: number;
  lastActivity?: string;
  createdOn?: string;
  updatedOn?: string;
}

export interface CreditDisplayProps {
  userId: string;
  showDetails?: boolean;
  className?: string;
}

export interface CreditState {
  isLoading: boolean;
  error: string | null;
  lastFetched: string | null;
}

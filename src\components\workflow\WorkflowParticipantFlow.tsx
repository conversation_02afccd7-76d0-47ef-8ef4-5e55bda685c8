import { BASE_URL } from '../../services/config';
import { Participant } from '../../types/workflow/index';
import styles from './WorkflowParticipantFlow.module.css';
import fallbackImage from '../../assets/images/profile.png';
import { Tooltip } from '@mui/material';

interface WorkflowParticipantFlowProps {
  participants: Participant[];
  currentUserId: string;
  creatorId: string;
}

const WorkflowParticipantFlow: React.FC<WorkflowParticipantFlowProps> = ({
  participants,
  currentUserId,
  creatorId,
}) => {
  const getParticipantStatusColor = (status: string | undefined) => {
    if (!status) return styles.default;

    switch (status.toLowerCase()) {
      case 'approved':
        return styles.approved;
      case 'rejected':
        return styles.rejected;
      case 'conditional_approved':
        return styles.conditionalApproved;
      default:
        return styles.pending;
    }
  };

  return (
    <div className={styles.approvalFlow}>
      {participants.map((participant, index) => (
        <div key={participant.userId} className={styles.flowItem}>
          <div className={styles.participantNode}>
            <div className={styles.nodeHeader}>
              {participant.userId === currentUserId && !participant.status && (
                <span className={styles.currentBadge}>Current</span>
              )}
              {participant.status && (
                <span
                  className={`${styles.statusBadge} ${styles[participant.status]}`}
                >
                  {participant.status === 'approved'
                    ? '✓'
                    : participant.status === 'rejected'
                      ? '✗'
                      : participant.status === 'conditional'
                        ? '⚠'
                        : ''}
                </span>
              )}
            </div>
            <img
              onError={(e) => {
                e.currentTarget.src = fallbackImage;
              }}
              src={
                participant.profileImg
                  ? `${BASE_URL}/api/v1/user/profile/download/${participant.profileImg}`
                  : fallbackImage
              }
              alt={participant.name}
              className={styles.avatar}
            />
            <div className={styles.participantInfo}>
              <div className={styles.participantName}>{participant.name}</div>
              <div className={styles.badgeContainer}>
                {participant.userId === creatorId && (
                  <span className={styles.creatorBadge}>Creator</span>
                )}
                <span
                  className={`${styles.participantStatus} ${getParticipantStatusColor(participant.status)}`}
                >
                  {participant.status || 'Pending'}
                </span>
                <Tooltip title={participant.note}>
                  <span className={styles.participantNote}>
                    {participant.note}
                  </span>
                </Tooltip>
              </div>
            </div>
          </div>
          {index < participants.length - 1 && (
            <div className={styles.connector} />
          )}
        </div>
      ))}
    </div>
  );
};

export default WorkflowParticipantFlow;

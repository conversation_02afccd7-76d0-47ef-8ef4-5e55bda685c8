import React from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import Logo from '../../assets/images/nuequiplogo.png';
import styles from './LoginPage.module.css';
import ResetPasswordForm from './components/ResetPasswordForm';

const ResetPassword: React.FC = () => {
  const [searchParams] = useSearchParams();
  const token = searchParams.get('token');
  const navigate = useNavigate();
  React.useEffect(() => {
    if (!token) {
      navigate('/login');
    }
  }, [token, navigate]);

  console.log(token);

  return (
    <div className={styles.loginContainer}>
      <img
        src={Logo}
        alt="Logo"
        style={{
          width: '200px',
          marginTop: '64px',
          marginLeft: '64px',
        }}
      />
      <div className={styles.headingContainer}>
        <h2 className={styles.heading}>Reset Password</h2>
        <p className={styles.subHeading}>Enter your new password</p>
      </div>
      <ResetPasswordForm token={token || ''} />
    </div>
  );
};

export default ResetPassword;

import React from 'react';
import { Controller } from 'react-hook-form';
import { useNavigate, Link } from 'react-router-dom';
import toast from 'react-hot-toast';
import CustomButton from '../../../components/common/button/CustomButton';
import CustomInput from '../../../components/common/input/CustomInput';
import { useForgotPasswordForm } from '../../../hooks/useAuthForm';
import styles from '../LoginPage.module.css';
import { useForgotPasswordMutation } from '../../../services';

const ForgotPasswordForm: React.FC = () => {
  const [forgotPassword] = useForgotPasswordMutation();

  const {
    control,
    handleSubmit,
    formState: { errors },
    trigger,
  } = useForgotPasswordForm();

  const navigate = useNavigate();

  const handleForgotPassword = async (data: { email: string }) => {
    console.log(data);
    const isValid = await trigger();
    if (!isValid) {
      toast.error('Please enter a valid email address');
    }

    try {
      await forgotPassword({ email: data.email }).unwrap();
      toast.success('Password reset link sent to your email');
    } catch (error) {
      toast.error('Failed to send reset link');
    }
  };

  return (
    <form
      className={styles.formContainer}
      onSubmit={handleSubmit(handleForgotPassword)}
    >
      <Controller
        name="email"
        control={control}
        rules={{
          required: 'Email is required',
          pattern: {
            value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
            message: 'Invalid email address',
          },
        }}
        render={({ field }) => (
          <CustomInput
            {...field}
            label="Email"
            type="email"
            placeholder="Enter your email"
            disableDarkTheme={true}
            error={!errors.email?.message}
          />
        )}
      />

      <CustomButton label="Send Reset Link" type="primary" variant="submit" />

      <div className={styles.linkContainer}>
        Remember your password?
        <Link to="/login" className={styles.link}>
          Back to Login
        </Link>
      </div>
    </form>
  );
};

export default ForgotPasswordForm;

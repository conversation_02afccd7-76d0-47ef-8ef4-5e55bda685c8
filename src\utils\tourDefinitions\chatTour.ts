import chartselectionimage from '../../assets/demo/chat_guide_for _selection.png';
import messageschatsexport from '../../assets/demo/messages_charts_export.png';
import exportselection from '../../assets/demo/export_selection.png';
// Chat page tour definition
export const chatTour = {
  id: 'chat-tour',
  name: 'Chat Feature Tour',
  routes: ['/chat'], // Available on the chat page
  requiresNavigation: true, // Should navigate to chat page if not already there
  steps: [
    {
      element: '#mainChatContainer',
      popover: {
        title: 'Chat Interface',
        description: 'This is where you can interact with the AI assistant.',
        // position: 'bottom',
      },
    },
    {
      element: '#chatInput',
      popover: {
        title: 'Chat Input',
        description:
          'Type your messages or drop a file here to communicate with the AI.',
        position: 'top',
      },
    },
    {
      element: '#chatHeader',
      popover: {
        title: 'Chat Header',
        description: 'here you can access the chat header.',
        position: 'top',
      },
    },
    {
      element: '#chatHeaderTitleContainer',
      popover: {
        title: 'Project Title',
        description: 'Double click to change the title of your project.',
        position: 'bottom',
      },
    },
    {
      element: '#editTitleButton',
      popover: {
        title: 'Edit Title Button',
        description: 'or click this button to edit the title of your project.',
        position: 'left',
      },
    },
    {
      element: '#complianceButton',
      popover: {
        title: 'Check Compliance',
        description: `
      <p>Click the Compliance button to open the sidebar. Select a framework and files, then click Generate to check compliance. Make sure your current project has uploaded files.</p>
    `,
        position: 'left',
        onNextClick: (element: any, step: any, { driver }: any) => {
          // Custom logic before moving to the next step
          const complianceButton = document.querySelector('#complianceButton');
          if (complianceButton instanceof HTMLElement) {
            complianceButton.click();
          }
          //wait 1 second beform moving to next
          setTimeout(() => {
            driver.moveNext();
          }, 500);
        },
      },
    },
    {
      element: '#sidebar',
      popover: {
        title: 'Compliance Section',
        description:
          'this is where you can select the framework and files to check compliance. if there are no files uploaded in the project, you will not be able to check compliance.',
      },
    },
    {
      element: '#settingsButton',
      popover: {
        title: 'Settings',
        description: 'Click this button to access the chat settings.',
        position: 'left',
        onNextClick: (element: any, step: any, { driver }: any) => {
          // Custom logic before moving to the next step
          const settingsButton = document.querySelector('#settingsButton');
          if (settingsButton instanceof HTMLElement) {
            settingsButton.click();
          }
          //wait 1 second beform moving to next
          setTimeout(() => {
            driver.moveNext();
          }, 1000);
          // Proceed to the next step
          // driver.moveNext();
        },
      },
    },
    {
      element: '#modelSelection',
      popover: {
        title: 'Select model',
        description: 'Here you can change the model Basic to Advanced.',
        position: 'left',
      },
    },
    {
      element: '#responseTypeSelection',
      popover: {
        title: 'Response Type',
        description: 'Here you can change the response type.',
        position: 'left',
      },
    },
    {
      element: '#pageRangeSelection',
      popover: {
        title: 'Page Range',
        description: 'Here you can change the page range.',
        position: 'left',
      },
    },
    {
      element: '#closeSettingsButton',
      popover: {
        title: 'Close Settings',
        description: 'Click this button to close the settings.',
        position: 'left',
        onNextClick: (element: any, step: any, { driver }: any) => {
          // Custom logic before moving to the next step
          const closeSettingsButton = document.querySelector(
            '#closeSettingsButton'
          );
          if (closeSettingsButton instanceof HTMLElement) {
            closeSettingsButton.click();
          }
          //wait 1 second beform moving to next
          setTimeout(() => {
            driver.moveNext();
            //scroll to the messsages
            const messagesContainer = document.querySelector('#chatContent');
            if (messagesContainer instanceof HTMLElement) {
              messagesContainer.scrollIntoView({
                behavior: 'smooth',
                block: 'start',
              });
            }
          }, 500);
        },
      },
    },
    {
      element: '#mainChatContainer',
      popover: {
        title: 'Export Chat Message and Charts to Sketchbook',
        description: `
      <p>The images below show chat messages and charts available for export. Use the highlighted checkboxes to select the items you want to export to the sketchbook.</p>
      <img src="${chartselectionimage}" alt="Chat Selection" style="margin-top: 10px; max-width: 100%; border-radius: 8px;" />
    `,
        position: 'left',
      },
    },
    {
      element: '#mainChatContainer',
      popover: {
        title: 'Export Chat Message and Charts to Sketchbook',
        description: `
      <p>here are the messages and charts that i have selected for your better understanding of the export feature.</p>
      <img src="${messageschatsexport}" alt="Chat Selection" style="margin-top: 10px; max-width: 100%; border-radius: 8px;" />
    `,
        position: 'left',
      },
    },

    {
      element: '#exportButton',
      popover: {
        title: 'Export',
        description: `After selecting messages or charts, click the Export button to send them to the sketchbook. The button stays disabled until something is selected.`,
        position: 'left',
      },
    },
    {
      element: '#exportButton',
      popover: {
        title: 'Export Chat Message and Charts to Sketchbook',
        description: `
      <p>Clicking the Export button shows two options: New Sketchbook and Existing Sketchbook. Choose New to create a sketchbook or Existing to add to one of your saved sketchbooks.</p>
      <img src="${exportselection}" alt="Chat Selection" style="margin-top: 10px; max-width: 100%; border-radius: 8px;" />
    `,
        position: 'left',
      },
    },
  ],
};

/**
 * Payment API Diagnostics Utility
 * Helps troubleshoot payment service connectivity issues
 */

import { BASE_URL } from '../../services/config';

interface DiagnosticResult {
  test: string;
  status: 'success' | 'error' | 'warning';
  message: string;
  details?: any;
  timestamp: string;
}

interface DiagnosticReport {
  overall: 'healthy' | 'degraded' | 'unhealthy';
  results: DiagnosticResult[];
  recommendations: string[];
}

/**
 * Get authentication token
 */
const getToken = (): string | null => {
  const token = localStorage.getItem('token');
  return token ? token.replace(/"/g, '') : null;
};

/**
 * Test basic connectivity to the payment API
 */
const testConnectivity = async (): Promise<DiagnosticResult> => {
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

    const response = await fetch(`${BASE_URL}/api/v1/plans/get-all`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${getToken()}`,
        'Content-Type': 'application/json',
      },
      signal: controller.signal,
    });

    clearTimeout(timeoutId);

    if (response.ok) {
      return {
        test: 'API Connectivity',
        status: 'success',
        message: 'Successfully connected to payment API',
        details: { status: response.status, url: response.url },
        timestamp: new Date().toISOString(),
      };
    } else {
      return {
        test: 'API Connectivity',
        status: 'error',
        message: `API returned ${response.status}: ${response.statusText}`,
        details: { status: response.status, url: response.url },
        timestamp: new Date().toISOString(),
      };
    }
  } catch (error: any) {
    if (error.name === 'AbortError') {
      return {
        test: 'API Connectivity',
        status: 'error',
        message: 'Connection timeout - API is not responding',
        details: { error: 'Timeout after 10 seconds' },
        timestamp: new Date().toISOString(),
      };
    }

    return {
      test: 'API Connectivity',
      status: 'error',
      message: `Connection failed: ${error.message}`,
      details: { error: error.message, type: error.name },
      timestamp: new Date().toISOString(),
    };
  }
};

/**
 * Test authentication status
 */
const testAuthentication = async (): Promise<DiagnosticResult> => {
  const token = getToken();
  
  if (!token) {
    return {
      test: 'Authentication',
      status: 'error',
      message: 'No authentication token found',
      details: { hasToken: false },
      timestamp: new Date().toISOString(),
    };
  }

  try {
    // Test with a simple authenticated endpoint
    const response = await fetch(`${BASE_URL}/api/v1/plans/get-all`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (response.status === 401) {
      return {
        test: 'Authentication',
        status: 'error',
        message: 'Authentication token is invalid or expired',
        details: { status: 401, hasToken: true },
        timestamp: new Date().toISOString(),
      };
    }

    if (response.ok) {
      return {
        test: 'Authentication',
        status: 'success',
        message: 'Authentication token is valid',
        details: { status: response.status, hasToken: true },
        timestamp: new Date().toISOString(),
      };
    }

    return {
      test: 'Authentication',
      status: 'warning',
      message: `Unexpected response: ${response.status}`,
      details: { status: response.status, hasToken: true },
      timestamp: new Date().toISOString(),
    };
  } catch (error: any) {
    return {
      test: 'Authentication',
      status: 'error',
      message: `Authentication test failed: ${error.message}`,
      details: { error: error.message, hasToken: true },
      timestamp: new Date().toISOString(),
    };
  }
};

/**
 * Test payment initiation endpoint specifically
 */
const testPaymentEndpoint = async (planId: string, userId: string): Promise<DiagnosticResult> => {
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

    const response = await fetch(`${BASE_URL}/api/v1/payments/initiate`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${getToken()}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ planId, userId }),
      signal: controller.signal,
    });

    clearTimeout(timeoutId);

    if (response.ok) {
      const data = await response.json();
      return {
        test: 'Payment Endpoint',
        status: 'success',
        message: 'Payment initiation endpoint is working',
        details: { status: response.status, hasClientSecret: !!data.clientSecret },
        timestamp: new Date().toISOString(),
      };
    } else if (response.status === 504) {
      return {
        test: 'Payment Endpoint',
        status: 'error',
        message: 'Payment service timeout (504) - Backend is overloaded or Stripe is slow',
        details: { status: 504, recommendation: 'Check backend logs and Stripe status' },
        timestamp: new Date().toISOString(),
      };
    } else {
      const errorData = await response.text();
      return {
        test: 'Payment Endpoint',
        status: 'error',
        message: `Payment endpoint error: ${response.status}`,
        details: { status: response.status, error: errorData },
        timestamp: new Date().toISOString(),
      };
    }
  } catch (error: any) {
    if (error.name === 'AbortError') {
      return {
        test: 'Payment Endpoint',
        status: 'error',
        message: 'Payment endpoint timeout - Service is not responding',
        details: { error: 'Timeout after 30 seconds' },
        timestamp: new Date().toISOString(),
      };
    }

    return {
      test: 'Payment Endpoint',
      status: 'error',
      message: `Payment endpoint failed: ${error.message}`,
      details: { error: error.message, type: error.name },
      timestamp: new Date().toISOString(),
    };
  }
};

/**
 * Run comprehensive payment diagnostics
 */
export const runPaymentDiagnostics = async (
  planId?: string,
  userId?: string
): Promise<DiagnosticReport> => {
  console.log('🔍 Running payment diagnostics...');
  
  const results: DiagnosticResult[] = [];
  
  // Test 1: Basic connectivity
  results.push(await testConnectivity());
  
  // Test 2: Authentication
  results.push(await testAuthentication());
  
  // Test 3: Payment endpoint (if IDs provided)
  if (planId && userId) {
    results.push(await testPaymentEndpoint(planId, userId));
  }
  
  // Determine overall health
  const errorCount = results.filter(r => r.status === 'error').length;
  const warningCount = results.filter(r => r.status === 'warning').length;
  
  let overall: 'healthy' | 'degraded' | 'unhealthy';
  if (errorCount === 0 && warningCount === 0) {
    overall = 'healthy';
  } else if (errorCount === 0) {
    overall = 'degraded';
  } else {
    overall = 'unhealthy';
  }
  
  // Generate recommendations
  const recommendations: string[] = [];
  
  if (results.some(r => r.message.includes('timeout'))) {
    recommendations.push('Check your internet connection and try again');
    recommendations.push('Contact your backend team - the payment service may be down');
  }
  
  if (results.some(r => r.status === 'error' && r.test === 'Authentication')) {
    recommendations.push('Please log out and log back in to refresh your session');
  }
  
  if (results.some(r => r.details?.status === 504)) {
    recommendations.push('The payment service is experiencing high load - try again in a few minutes');
    recommendations.push('Check Stripe status at https://status.stripe.com');
  }
  
  if (overall === 'healthy') {
    recommendations.push('All systems are working normally');
  }
  
  const report: DiagnosticReport = {
    overall,
    results,
    recommendations,
  };
  
  console.log('📊 Diagnostic Report:', report);
  return report;
};

/**
 * Quick connectivity test
 */
export const quickConnectivityTest = async (): Promise<boolean> => {
  try {
    const result = await testConnectivity();
    return result.status === 'success';
  } catch {
    return false;
  }
};

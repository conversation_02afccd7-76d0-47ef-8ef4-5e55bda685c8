import { BaseQueryFn, createApi } from '@reduxjs/toolkit/query/react';
import { AI_BASE_URL } from './config';
import { fileUploadBaseQuery } from './baseQuery';
import {
  CREDIT_USAGE_BY_USER,
  CREDIT_USAGE_BY_RESPONSE,
  CREDIT_USAGE_BY_PROJECT,
} from './constants/creditServiceConstants';
import {
  CreditUsage,
  CreditUsageByResponse,
  CreditUsageByProject,
} from '../types/credit';

export const creditApi = createApi({
  reducerPath: 'creditApi',
  baseQuery: fileUploadBaseQuery({
    baseUrl: AI_BASE_URL,
  }) as BaseQueryFn<any, unknown, unknown, {}, {}>,
  endpoints: (builder) => ({
    getCreditUsageByUser: builder.query<CreditUsage, string>({
      query: (userId) => ({
        url: CREDIT_USAGE_BY_USER + userId,
        method: 'GET',
      }),
      keepUnusedDataFor: 0,
    }),
    getCreditUsageByResponse: builder.query<CreditUsageByResponse, string>({
      query: (responseId) => ({
        url: CREDIT_USAGE_BY_RESPONSE + responseId,
        method: 'GET',
      }),
      keepUnusedDataFor: 0,
    }),
    getCreditUsageByProject: builder.query<CreditUsageByProject, string>({
      query: (projectId) => ({
        url: CREDIT_USAGE_BY_PROJECT + projectId,
        method: 'GET',
      }),
      keepUnusedDataFor: 0,
    }),
  }),
});

export const {
  useGetCreditUsageByUserQuery,
  useGetCreditUsageByResponseQuery,
  useGetCreditUsageByProjectQuery,
} = creditApi;

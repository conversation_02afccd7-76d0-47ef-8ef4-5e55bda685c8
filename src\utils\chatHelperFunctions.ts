import { transformChartData } from './chartMapper';
import { getFileIcon, getSimplifiedFileExtension } from './getFileExtention';
import { countPagesInFile } from './pageCounter';

const handleOnQuestionClick = (item: any) => {
  handleSendMessage(item);
};

export const renderRecentProjects = ({
  recentProjects,
  newProfileImage,
  senderIcon,
  botIcon,
  setMessages,
}: any) => {
  if (recentProjects && recentProjects.file_name) {
    const combinedArray =
      recentProjects &&
      recentProjects.prompt.map((p: any, index: number) => ({
        prompt: p,
        response: recentProjects.response[index],
        totalPages:
          recentProjects?.pages == null ? 0 : recentProjects?.pages[index],
        pages: recentProjects?.pages,
      }));

    const removeSpecificString = (message: string) => {
      return message
        .replace(
          '||| Provide a text-only response without any charts or visualizations.',
          ''
        )
        .trim();
    };

    const newMessages = combinedArray.reduce((acc: any, item: any) => {
      if (
        item.prompt ==
        "generate an array of 4 questions and don't send me charts"
      ) {
        let getIndexOfFile: any = localStorage.getItem('findIndexOfRecentFile');
        getIndexOfFile =
          getIndexOfFile === null || getIndexOfFile === undefined
            ? 0
            : parseInt(getIndexOfFile, 10);

        if (getIndexOfFile >= recentProjects.file_name.length) {
          getIndexOfFile = 0;
        }

        acc.push(
          {
            sender: 'user',
            content: recentProjects.file_name[getIndexOfFile],
            type: 'light',
            icon: getFileIcon(recentProjects.file_name[getIndexOfFile]),
            image: newProfileImage || senderIcon,
            timestamp: new Date().toLocaleTimeString(),
            noOfPages:
              recentProjects?.pages == null
                ? 0
                : recentProjects.pages[recentProjects.pages.length - 1],
          },
          {
            type: 'light',
            sender: 'bot',
            content:
              'Thank you for uploading your document. What would you like to do next ?',
            image: botIcon,
            timestamp: new Date().toLocaleTimeString(),
          }
        );
        if (
          item.response.suggested_questions &&
          item.response.suggested_questions.length > 0
        ) {
          acc.push({
            type: 'hint',
            sender: 'bot',
            content: 'Suggested follow up questions:',
            timestamp: new Date().toLocaleTimeString(),
          });

          const questionMessages = item.response.suggested_questions.map(
            (question: any) => ({
              handleOnClick: () => handleOnQuestionClick(question),
              type: 'dark',
              sender: 'bot',
              content: question,
              timestamp: new Date().toLocaleTimeString(),
            })
          );
          acc.push(...questionMessages);
        }
        getIndexOfFile += 1;
        localStorage.setItem(
          'findIndexOfRecentFile',
          getIndexOfFile.toString()
        );
      } else {
        if (item.prompt) {
          acc.push({
            sender: 'user',
            content: removeSpecificString(item.prompt),
            type: 'light',
            image: newProfileImage || senderIcon,
            timestamp: new Date().toLocaleTimeString(),
          });
        }

        if (item.response.tables && item.response.tables.length > 0) {
          const chartMessages = transformChartData(item.response.tables);
          acc.push(...chartMessages);
        }

        if (item.response.images && item.response.images.length > 0) {
          const chartImages = item.response.images.map((image: string) => {
            return {
              sender: 'bot',
              content: image,
              type: 'chartImage',
              timestamp: new Date().toLocaleTimeString(),
            };
          });
          acc.push(...chartImages);
        }

        if (item.response.summary) {
          acc.push({
            sender: 'bot',
            content: item.response.summary,
            type: 'light',
            image: botIcon,
            timestamp: new Date().toLocaleTimeString(),
          });
        }
        if (item.response.page_no) {
          acc.push({
            type: 'hint',
            sender: 'bot',
            content: `Referenced Pages ${item.response.page_no}`,
            timestamp: new Date().toLocaleTimeString(),
          });
        }

        if (
          item.response.suggested_questions &&
          item.response.suggested_questions.length > 0
        ) {
          acc.push({
            type: 'hint',
            sender: 'bot',
            content: 'Suggested follow up questions:',
            timestamp: new Date().toLocaleTimeString(),
          });

          const questionMessages = item.response.suggested_questions.map(
            (question: any) => ({
              handleOnClick: () => handleOnQuestionClick(question),
              type: 'dark',
              sender: 'bot',
              content: question,
              timestamp: new Date().toLocaleTimeString(),
            })
          );
          acc.push(...questionMessages);
        }
      }

      return acc;
    }, []);

    setMessages((prevMessages: any) => [...prevMessages, ...newMessages]);
  }
};

export const renderRecentProjectsTextOnly = ({
  recentProjects,
  newProfileImage,
  senderIcon,
  botIcon,
  setMessages,
}: any) => {
  if (recentProjects && !recentProjects.file_name) {
    const combinedArray =
      recentProjects &&
      recentProjects.prompt.map((p: any, index: number) => ({
        prompt: p,
        response: recentProjects.response[index],
        pages: recentProjects?.pages,
      }));

    const removeSpecificString = (message: string) => {
      return message
        .replace(
          '||| Provide a text-only response without any charts or visualizations.',
          ''
        )
        .trim();
    };

    const newMessages = combinedArray.reduce((acc: any, item: any) => {
      if (item.prompt) {
        acc.push({
          sender: 'user',
          content: removeSpecificString(item.prompt),
          type: 'light',
          image: newProfileImage || senderIcon,
          timestamp: new Date().toLocaleTimeString(),
        });
      }

      if (item.response.tables && item.response.tables.length > 0) {
        const chartMessages = transformChartData(item.response.tables);
        acc.push(...chartMessages);
      }

      if (item.response.images && item.response.images.length > 0) {
        const chartImages = item.response.images.map((image: string) => {
          return {
            sender: 'bot',
            content: image,
            type: 'chartImage',
            timestamp: new Date().toLocaleTimeString(),
          };
        });
        acc.push(...chartImages);
      }

      if (item.response.summary) {
        acc.push({
          sender: 'bot',
          content: item.response.summary,
          type: 'light',
          image: botIcon,
          timestamp: new Date().toLocaleTimeString(),
        });
      }

      if (item.response.page_no) {
        acc.push({
          type: 'hint',
          sender: 'bot',
          content: `Referenced Pages ${item.response.page_no}`,
          timestamp: new Date().toLocaleTimeString(),
        });
      }

      if (
        item.response.suggested_questions &&
        item.response.suggested_questions.length > 0
      ) {
        acc.push({
          type: 'hint',
          sender: 'bot',
          content: 'Suggested follow up questions:',
          timestamp: new Date().toLocaleTimeString(),
        });

        const questionMessages = item.response.suggested_questions.map(
          (question: any) => ({
            handleOnClick: () => handleOnQuestionClick(question),
            type: 'dark',
            sender: 'bot',
            content: question,
            timestamp: new Date().toLocaleTimeString(),
          })
        );
        acc.push(...questionMessages);
      }
      return acc;
    }, []);

    setMessages((prevMessages: any) => [...prevMessages, ...newMessages]);
  }
};

export const handleSendMessage = async ({
  content,
  uploadFile,
  addMessage,
  setMessages,
  newProfileImage,
  senderIcon,
  botIcon,
  setPageRangeChanges,
  userInLocalStorage,
  projectID,
  getProjectTitle,
  title,
  getFileName,
  getFileType,
  pageRangeChanges,
  setInputValue,
  setLoading,
}: any) => {
  // Early exit if content is empty
  if (!content) return;

  const storedPageRange = localStorage.getItem('pageRangeChanges');
  const currentPageRange =
    storedPageRange !== null && storedPageRange !== 'undefined'
      ? storedPageRange
      : pageRangeChanges || '';

  const dontGenerateChart = localStorage.getItem('dontGenerateChart') == 'true';

  // Add the user's message
  addMessage({
    sender: 'user',
    content,
    type: 'light',
    image: newProfileImage || senderIcon,
    timestamp: new Date().toLocaleTimeString(),
  });

  // Add a loading message
  addMessage({
    sender: 'bot',
    content: 'Processing your request...',
    type: 'light',
    image: botIcon,
    timestamp: new Date().toLocaleTimeString(),
  });

  const fileExtension = getSimplifiedFileExtension(getFileType);
  const formData = new FormData();
  formData.append('user_id', userInLocalStorage[0]?.id);
  formData.append('project_id', projectID);
  formData.append('title', getProjectTitle || title);
  formData.append('page_number', currentPageRange);
  formData.append('file_type', fileExtension || '');
  formData.append('file_name', getFileName || '');
  formData.append(
    'prompt',
    `${content}${!dontGenerateChart ? ' ||| Provide a text-only response without any charts or visualizations.' : ' '}`
  );

  setInputValue('');

  try {
    const response: any = await uploadFile({ formData }).unwrap();

    // Create an array for new messages
    const newMessages: any = [];

    // Handle tables if they exist
    if (response.tables && response.tables.length > 0) {
      const chartMessages = transformChartData(response.tables);
      newMessages.push(...chartMessages);
    }

    // Handle Chart Images if they exist
    if (response.images && response.images.length > 0) {
      const chartImages = response.images.map((image: string) => ({
        sender: 'bot',
        content: image,
        type: 'chartImage',
        timestamp: new Date().toLocaleTimeString(),
      }));
      newMessages.push(...chartImages);
    }

    // Handle summary response
    newMessages.push({
      sender: 'bot',
      content:
        response.summary || 'Something went wrong, the response might be empty',
      type: 'light',
      image: botIcon,
      timestamp: new Date().toLocaleTimeString(),
    });

    // Handle referenced Page
    if (response.page_no) {
      setPageRangeChanges(response.page_no);
      newMessages.push({
        sender: 'bot',
        content: `Referenced Pages ${response.page_no}`,
        type: 'hint',
        timestamp: new Date().toLocaleTimeString(),
      });
    }

    // Handle suggested questions if they exist
    if (
      response.suggested_questions &&
      response.suggested_questions.length > 0
    ) {
      newMessages.push({
        type: 'hint',
        sender: 'bot',
        content: 'Suggested follow-up questions:',
        timestamp: new Date().toLocaleTimeString(),
      });
      const questionMessages = response.suggested_questions.map(
        (question: any) => ({
          handleOnClick: () => handleOnQuestionClick(question),
          sender: 'bot',
          isSuggestedQuestions: true,
          content: question,
          type: 'dark',
          timestamp: new Date().toLocaleTimeString(),
        })
      );
      newMessages.push(...questionMessages);
    }

    // Set all new messages at once after processing
    // setMessages((prevMessages) => [...prevMessages, ...newMessages]);
    // Set all new messages at once after processing

    setMessages((prevMessages: any) => {
      // Remove the processing message
      const updatedMessages = prevMessages.filter(
        (message: any) => message.content !== 'Processing your request...'
      );
      return [...updatedMessages, ...newMessages];
    });
  } catch (error) {
    // Handle error message
    setMessages((prevMessages: any) => {
      const updatedMessages = prevMessages.map((message: any) => {
        if (message.content === 'Processing your request...') {
          return {
            ...message,
            content:
              'An error occurred while processing your request. Please try again.',
          };
        }
        return message;
      });
      return updatedMessages;
    });
  } finally {
    setLoading(false);
  }
};

export const handleFileUpload = async ({
  file,
  addMessage,
  setMessages,
  uploadFile,
  newProfileImage,
  senderIcon,
  botIcon,
  setPageRangeChanges,
  userInLocalStorage,
  projectID,
  pageRangeChanges,

  setIsPageRangeDialogOpen,
}: any) => {
  setIsPageRangeDialogOpen(false);
  const numberOfPages = await countPagesInFile(file);

  const storedPageRange = localStorage.getItem('pageRangeChanges');
  const currentPageRange =
    storedPageRange !== null && storedPageRange !== 'undefined'
      ? storedPageRange
      : pageRangeChanges || '';

  // Display the file upload message
  addMessage({
    sender: 'user',
    content: file.name,
    type: 'light',
    icon: getFileIcon(file.name),
    image: newProfileImage || senderIcon,
    timestamp: new Date().toLocaleTimeString(),
    noOfPages: numberOfPages,
  });

  // Display the loading message
  addMessage({
    sender: 'bot',
    content: 'Processing your file...',
    type: 'light',
    image: botIcon,
    timestamp: new Date().toLocaleTimeString(),
  });
  const fileExtention = getSimplifiedFileExtension(file);

  const formData = new FormData();
  formData.append('user_id', userInLocalStorage[0]?.id);
  formData.append('file', file);
  formData.append('project_id', projectID);
  formData.append('page_number', currentPageRange);
  formData.append('file_name', file.name);
  formData.append('file_type', fileExtention);
  formData.append(
    'prompt',
    "generate an array of 4 questions and don't send me charts"
  );

  try {
    const response: any = await uploadFile({ formData }).unwrap();
    // const response: any = await mockApiCall();

    // Update the loading message with the actual response
    setMessages((prevMessages: any) => {
      const updatedMessages = prevMessages.map((message: any) => {
        if (message.content === 'Processing your file...') {
          return {
            ...message,
            content:
              'Thank you for uploading your document. What would you like to do next ?',
          };
        }
        return message;
      });
      return updatedMessages;
    });
    // Handle refrenced Page
    if (response.page_no) {
      setPageRangeChanges(response.page_no);
      localStorage.setItem('pageRangeChanges', response.page_no);
    }

    // Suggested questions
    if (
      response.suggested_questions &&
      response.suggested_questions.length > 0
    ) {
      const questionMessages = response.suggested_questions.map(
        (question: any) => ({
          handleOnClick: () => handleSendMessage(question),
          sender: 'bot',
          isSuggestedQuestions: true,
          content: `${question}`,
          type: 'dark',
          timestamp: new Date().toLocaleTimeString(),
        })
      );

      setMessages((prevMessages: any) => [
        ...prevMessages,
        ...questionMessages,
      ]);
    }
  } catch (error) {
    // Update the loading message with an error message
    setMessages((prevMessages: any) => {
      const updatedMessages = prevMessages.map((message: any) => {
        if (message.content === 'Processing your file...') {
          return {
            ...message,
            content:
              'An error occurred while processing your file. Please try again.',
          };
        }
        return message;
      });
      return updatedMessages;
    });
  }
};

import React, { useRef, useState, useEffect } from 'react';
import style from '../../assets/styles/ChatPage.module.css';
import toast from 'react-hot-toast';
import { AiFillEdit, AiOutlineClose } from 'react-icons/ai';
import CustomButton from '../common/button/CustomButton';
import {
  useGetSketchbookByUserPaginatedQuery,
  useSaveToSketchbookMutation,
  useUpdateSketchbookAiDataMutation,
} from '../../services/sketchbookServices';
import {
  useChoseModalMutation,
  useUpdateTitleMutation,
  useGetCurrentModelQuery,
} from '../../services/chatServices';
import useLocalStorage from '../../hooks/useLocalStorage';
import {
  Drawer,
  ListItem,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Typography,
  Box,
  Grid,
  Tooltip,
  IconButton,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import { useDispatch, useSelector } from 'react-redux';
import { setResponseType } from '../../store/settingsSlice';
import { RootState } from '../../store/store';
// import {
//   ChartSelection,
//   ImageSelection,
//   TextSelection,
// } from '../../pages/chatpage/ChatPage';
import { IoSave, IoSettings, IoShieldCheckmarkOutline } from 'react-icons/io5';
import { RiExportLine } from 'react-icons/ri';
import { MdSaveAlt } from 'react-icons/md';
import Select from 'react-select';
import { SingleValue } from 'react-select';
import { alpha } from '@mui/material/styles';
import { ActionIcon } from '../common/ActionIcon';
import { ArrowBack } from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import {
  ChartSelection,
  ImageSelection,
  TextSelection,
} from '../../types/chat';
import { height } from '@mui/system';
import { backgroundClip } from 'html2canvas/dist/types/css/property-descriptors/background-clip';
import { formatTimestamp } from '../../utils/timesAgo';
import FreePlanNotice from '../common/FreePlanNotice';
import CreditUsage from '../common/CreditUsage';

interface ChatHeaderProps {
  sketchbookID?: string;
  projectID: string;
  uploadFileTitle: string;
  handleOpenDialog: () => void;
  recentProjects: any;
  recentProjectTitle: string;
  pageRangeChanges: string;
  handlePageRangeChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  handleSavePageRange: () => void;
  selectedCharts: ChartSelection[];
  selectedImages: ImageSelection[];
  selectedTexts: TextSelection[];
  onComplianceClick?: () => void;
}

interface ResponseOption {
  value: string;
  label: string;
}

interface ModelOption {
  value: string;
  label: string;
}

const StyledDrawer = styled(Drawer)(({ theme }) => ({
  '& .MuiDrawer-paper': {
    width: '300px',
    // height: 'calc(100% - 77px)',
    height: '100%',
    backgroundColor: theme.palette.background.paper,
  },
}));

const DrawerHeader = styled('div')(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  padding: theme.spacing(2),
  borderBottom: `1px solid ${theme.palette.divider}`,
}));

const DrawerSection = styled('div')(({ theme }) => ({
  padding: theme.spacing(2),
  '& + &': {
    borderTop: `1px solid ${theme.palette.divider}`,
  },
}));

const SectionTitle = styled(Typography)(({ theme }) => ({
  fontSize: '0.875rem',
  fontWeight: 500,
  color: theme.palette.text.secondary,
  marginBottom: theme.spacing(2),
}));

const StyledTextField = styled(TextField)(({ theme }) => ({
  marginTop: theme.spacing(1),
  width: '100%',
  '& .MuiOutlinedInput-root': {
    background:
      theme.palette.mode === 'dark'
        ? alpha(theme.palette.background.paper, 0.6)
        : '#f8f9fa',
    borderRadius: '8px',
    transition: theme.transitions.create(['background-color', 'border-color']),
    '& fieldset': {
      border: `1px solid ${theme.palette.primary.main}`,
    },
    '&:hover fieldset': {
      border: `1px solid ${theme.palette.primary.main}`,
    },
    '&.Mui-focused fieldset': {
      border: `1px solid ${theme.palette.primary.main}`,
    },
  },
  '& .MuiInputBase-input': {
    padding: '8px 14px', // Adjust padding to match Select component
    color: theme.palette.text.primary,
  },
}));

// const VisualizationToggle = styled('div')(({ theme }) => ({
//   display: 'flex',
//   flexDirection: 'column',
//   alignItems: 'center',
//   padding: theme.spacing(2),
//   backgroundColor: theme.palette.background.paper,
//   borderRadius: theme.shape.borderRadius,
//   boxShadow: theme.shadows[1],
//   marginBottom: theme.spacing(2),
// }));

// const ToggleIcon = styled(BsBarChartFill)(({ theme }) => ({
//   fontSize: '2rem',
//   color: theme.palette.primary.main,
//   marginBottom: theme.spacing(1),
// }));

// const ToggleLabel = styled('span')(({ theme }) => ({
//   fontWeight: 'bold',
//   color: theme.palette.text.primary,
//   marginBottom: theme.spacing(1),
// }));

const customSelectStyles = {
  control: (base: any, { theme }: any) => ({
    ...base,
    background:
      theme?.palette?.mode === 'dark'
        ? alpha(theme.palette.background.paper, 0.6)
        : '#f8f9fa',
    borderRadius: '8px',
    border: `1px solid ${theme?.palette?.primary?.main || '#4a90e2'}`,
    boxShadow: 'none',
    width: '100%',
    color: theme?.palette?.text?.primary,
    transition:
      'background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease',
    '&:hover': {
      border: `1px solid ${theme?.palette?.primary?.main || '#4a90e2'}`,
    },
  }),
  option: (base: any, { isSelected, theme }: any) => ({
    ...base,
    background: isSelected
      ? theme?.palette?.primary?.main || '#4a90e2'
      : base.background,
    color: isSelected
      ? theme?.palette?.primary?.contrastText || '#ffffff'
      : theme?.palette?.text?.primary,
    '&:hover': {
      background: isSelected
        ? theme?.palette?.primary?.main || '#4a90e2'
        : theme?.palette?.mode === 'dark'
          ? alpha(theme.palette.primary.main, 0.2)
          : '#f1f3f5',
    },
  }),
  valueContainer: (base: any) => ({
    ...base,
    maxHeight: '80px',
    overflow: 'auto',
  }),
  multiValue: (base: any, { theme }: any) => ({
    ...base,
    backgroundColor:
      theme?.palette?.mode === 'dark'
        ? alpha(theme.palette.primary.main, 0.2)
        : '#e2e8f0',
    borderRadius: '4px',
  }),
  menu: (base: any, { theme }: any) => ({
    ...base,
    width: '100%',
    backgroundColor: 'white',
    borderColor: theme?.palette?.divider,
  }),
  container: (base: any) => ({
    ...base,
    width: '100%',
  }),
};

const ExportDialog = styled(Dialog)(({ theme }) => ({
  '& .MuiDialog-paper': {
    borderRadius: '12px',
    maxWidth: '600px',
    width: '100%',
    boxShadow:
      theme.palette.mode === 'dark'
        ? '0 8px 32px rgba(0, 0, 0, 0.3)'
        : '0 8px 32px rgba(0, 0, 0, 0.08)',
    backgroundColor: theme.palette.background.paper,
    transition: theme.transitions.create(['background-color', 'box-shadow']),
  },
}));

const ExportOption = styled(Box)(({ theme }) => ({
  padding: theme.spacing(3.5),
  border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
  borderRadius: '12px',
  cursor: 'pointer',
  transition: 'all 0.2s ease',
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  gap: theme.spacing(2),
  backgroundColor: theme.palette.background.paper,
  '&:hover': {
    backgroundColor: alpha(theme.palette.primary.main, 0.02),
    borderColor: theme.palette.primary.main,
    transform: 'translateY(-2px)',
    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.05)',
  },
}));

const IconWrapper = styled('div')(({ theme }) => ({
  width: '48px',
  height: '48px',
  borderRadius: '12px',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  backgroundColor: alpha(theme.palette.primary.main, 0.08),
  color: theme.palette.primary.main,
  marginBottom: theme.spacing(1),
}));

const HeaderContainer = styled('div')(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  padding: theme.spacing(1, 2),
  // backgroundColor: theme.palette.background.paper,
  borderBottom: `1px solid ${theme.palette.divider}`,
  height: '48px',
}));

const TitleContainer = styled('div')(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(1),
  flex: 1,
}));

const Title = styled('h1')(({ theme }) => ({
  margin: 0,
  fontSize: theme.typography.subtitle1.fontSize,
  fontWeight: 500,
  color: theme.palette.primary.main,
  cursor: 'pointer',
  padding: theme.spacing(0.5, 1),
  borderRadius: theme.shape.borderRadius,
  '&:hover': {
    backgroundColor: theme.palette.action.hover,
  },
}));

const ActionButtons = styled('div')(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(1),
}));

const StyledIconButton = styled(ActionIcon)(({ theme }) => ({
  padding: theme.spacing(0.5),
  color: theme.palette.primary.dark,
  backgroundColor: 'transparent',
  border: '1px solid transparent',
  transition: theme.transitions.create(
    ['background-color', 'border-color', 'box-shadow'],
    {
      duration: theme.transitions.duration.short,
    }
  ),
  '&:hover': {
    backgroundColor: alpha(theme.palette.primary.main, 0.08),
    border: `1px solid ${theme.palette.primary.main}`,
  },
  '&:active': {
    backgroundColor: alpha(theme.palette.primary.main, 0.12),
  },
  '&.Mui-disabled': {
    color: theme.palette.action.disabled,
    border: `1px solid ${theme.palette.action.disabledBackground}`,
    backgroundColor: theme.palette.action.disabledBackground,
  },
  '& svg': {
    fontSize: '16px',
  },
}));

const AnimatedExportButton = styled('div')<{ isEnabled: boolean }>(
  ({ theme, isEnabled }) => ({
    display: 'flex',
    alignItems: 'center',
    gap: theme.spacing(0.5),
    padding: isEnabled ? theme.spacing(0.5, 1) : theme.spacing(0.5),
    borderRadius: theme.shape.borderRadius,
    backgroundColor: isEnabled
      ? alpha(theme.palette.success.main, 0.1)
      : 'transparent',
    border: isEnabled
      ? `1px solid ${theme.palette.success.main}`
      : '1px solid transparent',
    cursor: isEnabled ? 'pointer' : 'not-allowed',
    transition: theme.transitions.create(['all'], {
      duration: theme.transitions.duration.standard,
      easing: theme.transitions.easing.easeInOut,
    }),
    transform: isEnabled ? 'scale(1.1)' : 'scale(1)',
    animation: isEnabled ? 'pulse 2s infinite' : 'none',

    '@keyframes pulse': {
      '0%': {
        boxShadow: `0 0 0 0 ${alpha(theme.palette.success.main, 0.4)}`,
      },
      '70%': {
        boxShadow: `0 0 0 8px ${alpha(theme.palette.success.main, 0)}`,
      },
      '100%': {
        boxShadow: `0 0 0 0 ${alpha(theme.palette.success.main, 0)}`,
      },
    },

    '&:hover': {
      backgroundColor: isEnabled
        ? alpha(theme.palette.success.main, 0.15)
        : 'transparent',
      transform: isEnabled ? 'scale(1.15)' : 'scale(1)',
    },

    '& .export-icon': {
      color: isEnabled
        ? theme.palette.success.main
        : theme.palette.action.disabled,
      fontSize: isEnabled ? '18px' : '16px',
      transition: theme.transitions.create(['color', 'font-size'], {
        duration: theme.transitions.duration.standard,
      }),
    },

    '& .export-label': {
      color: isEnabled ? theme.palette.success.main : 'transparent',
      fontSize: '12px',
      fontWeight: 600,
      opacity: isEnabled ? 1 : 0,
      maxWidth: isEnabled ? '60px' : '0px',
      overflow: 'hidden',
      whiteSpace: 'nowrap',
      transition: theme.transitions.create(['opacity', 'max-width', 'color'], {
        duration: theme.transitions.duration.standard,
      }),
    },
  })
);

const StyledCustomButton = styled(CustomButton)(
  ({ theme }) => `
  width: 100%;
  justify-content: flex-start;
  padding: 16px;
  border-radius: 8px;
  background-color: ${theme.palette.mode === 'dark' ? alpha(theme.palette.background.paper, 0.6) : '#f5f5f5'};
  border: 1px solid ${theme.palette.divider};
  margin-bottom: 8px;
  color: ${theme.palette.text.primary};
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;

  &:hover {
    background-color: ${alpha(theme.palette.primary.main, theme.palette.mode === 'dark' ? 0.2 : 0.1)};
    border-color: ${theme.palette.primary.main};
  }
`
);

const StyledListItem = styled(ListItem)(
  ({ theme }) => `
  padding: 16px;
  border-radius: 8px;
  background-color: ${theme.palette.mode === 'dark' ? alpha(theme.palette.background.paper, 0.6) : '#f5f5f5'};
  border: 1px solid ${theme.palette.divider};
  margin-bottom: 8px;
  color: ${theme.palette.text.primary};
  cursor: pointer;
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;

  &:hover {
    background-color: ${alpha(theme.palette.primary.main, theme.palette.mode === 'dark' ? 0.2 : 0.1)};
    border-color: ${theme.palette.primary.main};
  }
`
);

const ChatHeader: React.FC<ChatHeaderProps> = ({
  projectID,
  sketchbookID,
  uploadFileTitle,
  recentProjectTitle,
  pageRangeChanges,
  handlePageRangeChange,
  handleSavePageRange,
  selectedCharts,
  selectedImages,
  selectedTexts,
  onComplianceClick,
}) => {
  const [isSketchbookSaved, setIsSketchbookSaved] = useState(false);
  const [sketchbookIdFromResponse, setSketchbookIdFromResponse] = useState('');
  const [dropdownVisible, setDropdownVisible] = useState(false);
  const [showExisting, setShowExisting] = useState(false);
  const [dontGenerateChart, setDontGenerateChart] = useState<boolean>(() => {
    const storedValue = localStorage.getItem('dontGenerateChart');
    return storedValue === 'true';
  });

  const navigate = useNavigate();

  const titleRef = useRef<HTMLHeadingElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const [updateTitle] = useUpdateTitleMutation();
  const [choseModel] = useChoseModalMutation();
  const [saveToSketchbook] = useSaveToSketchbookMutation();
  const [updateSketchbookAiData] = useUpdateSketchbookAiDataMutation();

  const userInLocalStorage = useLocalStorage('user', null);
  const userId = userInLocalStorage[0].id;

  const { data: modelData, refetch: refetchModel } =
    useGetCurrentModelQuery(projectID);
  const { data: existingSketchbooks } = useGetSketchbookByUserPaginatedQuery({
    userId,
    page: 1,
    size: 200,
  });

  // const { data: existingSketchbooks } = useGetSketchbookByIdQuery({
  //   sketchbookID,
  // });

  const [currentModel, setCurrentModel] = useState('');
  const [availableModels] = useState<ModelOption[]>([
    { label: 'Basic Model', value: 'gpt-4o-mini' },
    { label: 'Advanced Model', value: 'gpt-4o-2024-08-06' },
  ]);

  const [drawerOpen, setDrawerOpen] = useState(false);

  const dispatch = useDispatch();
  const responseType = useSelector(
    (state: RootState) => state.settings.responseType
  );

  const [openExportDialog, setOpenExportDialog] = useState(false);

  useEffect(() => {
    if (modelData) {
      setCurrentModel(modelData);
    }
  }, [modelData]);

  // const handleChangeTitle = async (e: React.FocusEvent<HTMLHeadingElement>) => {
  //   const target = e.currentTarget;
  //   target.contentEditable = 'false';
  //   const newValue = target.innerText.trim();

  //   if (newValue.length === 0) {
  //     toast.error('Title cannot be empty!');
  //     target.innerText = uploadFileTitle || 'Untitled';
  //     return;
  //   }
  //   if (newValue === recentProjectTitle) {
  //     return;
  //   }

  //   if (newValue.length > 20) {
  //     toast.error('Title cannot exceed 20 characters!');
  //     target.innerText = newValue.substring(0, 20);
  //     return;
  //   }

  //   try {
  //     if (e.key === 'Enter') {
  //       await updateTitle({
  //         project_id: projectID,
  //         new_title: newValue,
  //       });
  //       toast.success('Title updated successfully');
  //     } else {
  //       await updateTitle({
  //         project_id: projectID,
  //         new_title: newValue,
  //       });
  //       toast.success('Title updated successfully');
  //     }
  //   } catch (error) {
  //     console.error(error);
  //     toast.error('Failed to update title');
  //   }
  // };

  // Change Model

  const handleChangeTitle = async (e: React.FocusEvent<HTMLHeadingElement>) => {
    const target = e.currentTarget;
    target.contentEditable = 'false';
    const newValue = target.innerText.trim();

    if (newValue.length === 0) {
      toast.error('Title cannot be empty!');
      target.innerText = uploadFileTitle || 'Untitled';
      return;
    }

    if (newValue.length > 50) {
      const truncatedValue = newValue.substring(0, 50);
      target.innerText = truncatedValue;
      await updateTitle({
        project_id: projectID,
        new_title: truncatedValue,
      });
      toast('Title cannot exceed 20 characters! Truncated automatically', {
        icon: <span style={{ fontSize: '30px' }}>🛈</span>,
      });
      return;
    }

    if (newValue === recentProjectTitle) return;

    try {
      await updateTitle({
        project_id: projectID,
        new_title: newValue,
      });
      toast.success('Title updated successfully');
    } catch (error) {
      console.error(error);
      toast.error('Failed to update title');
    }
  };
  const handleModelChange = async (
    selectedOption: SingleValue<ModelOption>
  ) => {
    if (selectedOption) {
      let toastValue =
        selectedOption.value === 'gpt-4o-mini'
          ? 'Basic Model'
          : 'Advanced Model';
      try {
        await choseModel({
          project_id: projectID,
          model_name: selectedOption.value,
        });
        toast.success(`Model Switched to ${toastValue}`);
        setCurrentModel(selectedOption.value);
        refetchModel();
      } catch (error) {
        console.error('Error switching model:', error);
        toast.error('Failed to switch model');
      }
    }
  };

  // Enable Chat Generation
  const handleToggleDontGenerateChart = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const isChecked = event.target.checked;
    setDontGenerateChart(isChecked);
    localStorage.setItem('dontGenerateChart', isChecked.toString());
  };

  // Save Selected Charts
  const handleSaveSelectedCharts = () => {
    setDropdownVisible(!dropdownVisible);
  };

  //Save Chart as new
  const handleSaveAsNew = async () => {
    // Generate layouts for charts
    const chartLayouts = selectedCharts.map((chart, index) => ({
      i: chart.id, // Using the actual chart ID
      x: (index % 2) * 3,
      y: Math.floor(index / 2) * 2,
      w: 3,
      h: 2,
    }));

    // Generate layouts for images, continuing from where charts left off
    const imageLayouts = selectedImages.map((image, index) => ({
      i: image.id, // Using the actual image ID
      x: ((index + selectedCharts.length) % 2) * 3,
      y: Math.floor((index + selectedCharts.length) / 2) * 2,
      w: 3,
      h: 2,
    }));

    const textLayouts = selectedTexts.map((text, index) => ({
      i: text.id,
      x: ((index + selectedCharts.length) % 2) * 3,
      y: Math.floor((index + selectedCharts.length) / 2) * 2,
      w: 3,
      h: 2,
    }));

    // Combine both layouts
    const layouts = [...chartLayouts, ...imageLayouts, ...textLayouts];

    const sketchbookPayload = {
      sketchbook_name: uploadFileTitle || recentProjectTitle || 'Untitled',
      project_id: sketchbookID, // Set project_id to null for blank sketchbooks
      user_id: userId,
      flowNodes: [],
      flowEdges: [],
      pageEnabled: true,
      activePage: '01',
      pageSize: {
        value: 'a4',
        label: 'A4',
        width: 559,
        height: 793,
        orientation: 'portrait',
      },
      pages: [
        {
          id: '01',
          name: '01',
          custom_chart_ids: [],
          ai_chart_ids: selectedCharts,
          ai_image_ids: selectedImages,
          ai_summary_ids: selectedTexts,
          layouts: layouts,
        },
      ],
    };

    try {
      const response = await saveToSketchbook(sketchbookPayload).unwrap();
      if (response.status === 201) {
        toast.success('Created new sketchbook successfully');
        // setDropdownVisible(false);
        console.log(response, 'hello');
        setSketchbookIdFromResponse(response?.data?.id);
        setIsSketchbookSaved(true);
      }
    } catch (error) {
      console.error(error);
      toast.error('Failed to create sketchbook');
    }
  };

  // Save as Existing
  const handleSaveAsExisting = () => {
    setShowExisting(!showExisting);
  };

  const handleUpdateExistingSketchbook = async (item: any) => {
    try {
      // Get the first page's data (assuming it's an array)
      const existingChartIds = item.chart_ids?.[0] || {
        ai_chart_ids: [],
        ai_image_ids: [],
        ai_summary_ids: [],
        layouts: [],
      };

      // Calculate the maximum Y position from existing layouts
      const maxY =
        existingChartIds.layouts?.length > 0
          ? Math.max(
              ...existingChartIds.layouts.map(
                (layout: any) => layout.y + layout.h
              )
            )
          : 0;

      // Generate new layouts for charts, images, and texts
      const newChartLayouts = selectedCharts.map((chart, index) => ({
        i: chart.id,
        x: (index % 2) * 3,
        y: maxY + Math.floor(index / 2) * 2,
        w: 3,
        h: 2,
      }));

      const newImageLayouts = selectedImages.map((image, index) => ({
        i: image.id,
        x: ((index + selectedCharts.length) % 2) * 3,
        y: maxY + Math.floor((index + selectedCharts.length) / 2) * 2,
        w: 3,
        h: 2,
      }));

      const newTextLayouts = selectedTexts.map((text, index) => ({
        i: text.id,
        x: ((index + selectedCharts.length + selectedImages.length) % 2) * 3,
        y:
          maxY +
          Math.floor(
            (index + selectedCharts.length + selectedImages.length) / 2
          ) *
            2,
        w: 3,
        h: 2,
      }));

      // Combine existing and new layouts
      const updatedLayouts = [
        ...(existingChartIds.layouts || []),
        ...newChartLayouts,
        ...newImageLayouts,
        ...newTextLayouts,
      ];

      const updateData = {
        pageIndex: 0,
        layouts: updatedLayouts,
        ai_chart_ids: [
          ...(existingChartIds.ai_chart_ids || []),
          ...selectedCharts,
        ],
        ai_image_ids: [
          ...(existingChartIds.ai_image_ids || []),
          ...selectedImages,
        ],
        ai_summary_ids: [
          ...(existingChartIds.ai_summary_ids || []),
          ...selectedTexts,
        ],
      };

      const response = await updateSketchbookAiData({
        id: item.id,
        ...updateData,
      }).unwrap();

      if (response.success) {
        toast.success('Updated existing sketchbook');
        // setShowExisting(false);
        console.log(response?.data?.id);
        setSketchbookIdFromResponse(response?.data?.id);
        setIsSketchbookSaved(true);
      }
    } catch (error) {
      console.error('Error updating sketchbook:', error);
      toast.error('Failed to update sketchbook');
    }
  };

  const handleResponseTypeChange = (
    selectedOption: SingleValue<ResponseOption>
  ) => {
    if (selectedOption) {
      dispatch(setResponseType(selectedOption.value));
      toast.success(`Response Type Changed to ${selectedOption.value}`, {
        duration: 4000,
      });
    }
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setDropdownVisible(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Initialize responseType from localStorage
  useEffect(() => {
    const savedResponseType = localStorage.getItem('responseType');
    if (savedResponseType) {
      dispatch(setResponseType(savedResponseType));
    }
  }, [dispatch]);

  return (
    <HeaderContainer id="chatHeader">
      <TitleContainer>
        <Title
          id="chatHeaderTitleContainer"
          ref={titleRef}
          onDoubleClick={(e) => {
            const target = e.currentTarget;
            target.contentEditable = 'true';
            target.focus();
          }}
          onBlur={handleChangeTitle}
          onKeyDown={(e) => {
            if (e.key === 'Enter') {
              e.preventDefault();
              if (titleRef.current) {
                titleRef.current.blur();
              }
            }
          }}
        >
          {uploadFileTitle || recentProjectTitle || 'Untitled'}
        </Title>
        <Tooltip title="Edit Title">
          <StyledIconButton
            id="editTitleButton"
            onClick={() => {
              if (titleRef.current) {
                titleRef.current.contentEditable = 'true';
                titleRef.current.focus();
              }
            }}
          >
            <AiFillEdit size={14} />
          </StyledIconButton>
        </Tooltip>
      </TitleContainer>
      <FreePlanNotice storageKey="chatPageFreePlanNotice" variant="button" />
      <CreditUsage showDetails={true} />
      <ActionButtons>
        <Tooltip title="Compliance Check">
          <span>
            <StyledIconButton
              id="complianceButton"
              onClick={onComplianceClick}
              disabled={!projectID}
            >
              <IoShieldCheckmarkOutline size={14} />
            </StyledIconButton>
          </span>
        </Tooltip>
        <Tooltip title="Settings">
          <StyledIconButton
            id="settingsButton"
            onClick={() => setDrawerOpen(true)}
          >
            <IoSettings size={14} />
          </StyledIconButton>
        </Tooltip>

        <Tooltip
          title={
            selectedCharts.length === 0 &&
            selectedImages.length === 0 &&
            selectedTexts.length === 0
              ? 'Select items to export'
              : 'Export selected items'
          }
        >
          <span id="exportButton">
            <AnimatedExportButton
              isEnabled={
                selectedCharts.length > 0 ||
                selectedImages.length > 0 ||
                selectedTexts.length > 0
              }
              onClick={() => {
                if (
                  selectedCharts.length > 0 ||
                  selectedImages.length > 0 ||
                  selectedTexts.length > 0
                ) {
                  setOpenExportDialog(true);
                }
              }}
            >
              <RiExportLine className="export-icon" />
              <span className="export-label">Export</span>
            </AnimatedExportButton>
          </span>
        </Tooltip>
      </ActionButtons>

      <StyledDrawer
        anchor="right"
        open={drawerOpen}
        onClose={() => setDrawerOpen(false)}
      >
        <DrawerHeader>
          <Typography variant="h6">Settings</Typography>
          <ActionIcon
            id="closeSettingsButton"
            onClick={() => setDrawerOpen(false)}
          >
            <AiOutlineClose size={14} />
          </ActionIcon>
        </DrawerHeader>

        <DrawerSection id="modelSelection">
          <SectionTitle>Model Selection</SectionTitle>
          <Select<ModelOption>
            className={style.select}
            placeholder="Select Model"
            options={availableModels}
            value={availableModels.find(
              (model) => model.value === currentModel
            )}
            onChange={handleModelChange}
            styles={customSelectStyles}
          />
        </DrawerSection>

        <DrawerSection id="responseTypeSelection">
          <SectionTitle>Response Type</SectionTitle>
          <Select
            className={style.select}
            placeholder="Select Response Type"
            options={[
              { value: 'brief', label: 'Brief' },
              { value: 'elaborative', label: 'Elaborative' },
            ]}
            value={{
              value: responseType,
              label:
                responseType.charAt(0).toUpperCase() + responseType.slice(1),
            }}
            onChange={handleResponseTypeChange}
            styles={customSelectStyles}
          />
        </DrawerSection>

        <DrawerSection id="pageRangeSelection">
          <SectionTitle>Referenced Pages</SectionTitle>
          <StyledTextField
            fullWidth
            size="small"
            value={pageRangeChanges}
            onChange={handlePageRangeChange}
            placeholder="e.g., 1-5, 7, 10-12"
            sx={{ marginBottom: 2 }}
          />
          <CustomButton
            type="secondary"
            label="Save Page Range"
            onClick={handleSavePageRange}
            disabled={pageRangeChanges?.length === 0}
            leftIcon={<IoSave size={14} />}
            style={{ width: '100%' }}
          />
        </DrawerSection>
      </StyledDrawer>

      <ExportDialog
        open={openExportDialog}
        onClose={() => {
          setOpenExportDialog(false);
          setShowExisting(false);
        }}
      >
        <DialogContent sx={{ p: 4 }}>
          {!showExisting ? (
            <>
              <Typography variant="h5" sx={{ mb: 1, fontWeight: 600 }}>
                Export Selection
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 4 }}>
                {selectedCharts.length} charts, {selectedImages.length} images,{' '}
                {selectedTexts.length} texts selected
              </Typography>
              <Grid container spacing={3}>
                <Grid item xs={6}>
                  <ExportOption onClick={handleSaveAsNew}>
                    <IconWrapper>
                      <MdSaveAlt size={24} />
                    </IconWrapper>
                    <Typography variant="subtitle1" fontWeight={600}>
                      New Sketchbook
                    </Typography>
                    <Typography
                      variant="body2"
                      color="text.secondary"
                      align="center"
                    >
                      Create a new sketchbook with selected items
                    </Typography>
                  </ExportOption>
                </Grid>
                <Grid item xs={6}>
                  <ExportOption onClick={() => setShowExisting(true)}>
                    <IconWrapper>
                      <IoSave size={24} />
                    </IconWrapper>
                    <Typography variant="subtitle1" fontWeight={600}>
                      Existing Sketchbook
                    </Typography>
                    <Typography
                      variant="body2"
                      color="text.secondary"
                      align="center"
                    >
                      Add to an existing sketchbook
                    </Typography>
                  </ExportOption>
                </Grid>
              </Grid>
            </>
          ) : (
            <>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                <IconButton
                  onClick={() => setShowExisting(false)}
                  sx={{ mr: 2, color: 'text.secondary' }}
                >
                  <ArrowBack />
                </IconButton>
                <Typography variant="h5" fontWeight={600}>
                  Select Sketchbook
                </Typography>
              </Box>
              <Box
                sx={{
                  maxHeight: '350px',
                  overflowY: 'auto',
                  '& > button': { mb: 1 },
                }}
              >
                {existingSketchbooks?.data?.content?.map((item: any) => (
                  <StyledListItem
                    key={item.id}
                    onClick={() => handleUpdateExistingSketchbook(item)}
                  >
                    <div>
                      {item.sketchbook_name || 'Untitled'}
                      {item.updatedOnTimestamp ? (
                        <Typography
                          variant="body2"
                          color="text.secondary"
                          sx={{ display: 'block' }}
                        >
                          Last updated:{' '}
                          {formatTimestamp(item.updatedOnTimestamp)}
                        </Typography>
                      ) : null}
                    </div>
                  </StyledListItem>
                ))}
              </Box>
            </>
          )}
        </DialogContent>
        {!showExisting && (
          <DialogActions
            sx={{
              p: 3,
              borderTop: (theme) =>
                `1px solid ${alpha(theme.palette.divider, 0.8)}`,
              backgroundColor: (theme) => theme.palette.background.paper,
              transition: (theme) =>
                theme.transitions.create(['background-color', 'border-color']),
            }}
          >
            {isSketchbookSaved && (
              <CustomButton
                type="primary"
                label="Goto Sketchbook"
                onClick={
                  // () => console.log(sketchbookID, 'hello ')
                  () => {
                    navigate('/sketchbook', {
                      state: { sketchbookId: sketchbookIdFromResponse },
                    });
                  }
                }
              />
            )}
            <CustomButton
              type="secondary"
              label="Close"
              onClick={() => {
                setOpenExportDialog(false);
                setShowExisting(false);
              }}
            />
          </DialogActions>
        )}
      </ExportDialog>
    </HeaderContainer>
  );
};

export default ChatHeader;

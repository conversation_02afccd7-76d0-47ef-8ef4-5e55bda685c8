import { ColDef } from 'ag-grid-community';
import styles from '../ChartPropertyController.module.css';
import {
  FormControl,
  Switch,
  FormControlLabel,
  TextField,
} from '@mui/material';
import DatePicker from 'react-datepicker';
import { AgGridReact } from 'ag-grid-react';
import { AnyIfEmpty } from 'react-redux';
import { chartPayloadHandler } from '../utils/chartPayloadHandler';
import { useUpdateCustomChartsMutation } from '../../../../services/sketchbookServices';
import toast from 'react-hot-toast';
import { useEffect } from 'react';

interface BurndownControlsProps {
  sketchbookId: string;
  activePage: any;
  selectedChart: any;
  onChartUpdate: (chart: any) => void;
}

export const BurndownControls = ({
  sketchbookId,
  activePage,
  selectedChart,
  onChartUpdate,
}: BurndownControlsProps) => {
  if (!selectedChart) return null;

  const [updateCustomCharts] = useUpdateCustomChartsMutation();

  const updateBurndownData = async (
    datasetIndex: number,
    pointIndex: number,
    value: number
  ) => {
    try {
      const updatedChart = JSON.parse(JSON.stringify(selectedChart));
      updatedChart.data.datasets[datasetIndex].data[pointIndex].y = value;

      // Update the chart in the UI first for immediate feedback
      onChartUpdate(updatedChart);

      // Then save to the backend
      const updatedChartPayload = chartPayloadHandler(
        updatedChart,
        'burndown',
        sketchbookId,
        activePage
      );
      const response: any = await updateCustomCharts({
        id: selectedChart.id,
        payload: updatedChartPayload,
        chartType: 'burndown',
      });

      if (response?.data?.success) {
        toast.success('Burndown data updated successfully');
      }
    } catch (error) {
      console.error('Error updating burndown data:', error);
      toast.error('Failed to update burndown data');
    }
  };

  const updateChartOption = (path: string[], value: any) => {
    const updatedChart = JSON.parse(JSON.stringify(selectedChart));
    let current = updatedChart;
    for (let i = 0; i < path.length - 1; i++) {
      current = current[path[i]];
    }
    current[path[path.length - 1]] = value;
    onChartUpdate(updatedChart);
  };

  const adjustSprintDuration = (days: number) => {
    try {
      const updatedChart = JSON.parse(JSON.stringify(selectedChart));
      const startDate = new Date(updatedChart.data.datasets[0].data[0].x);
      const totalPoints = updatedChart.data.datasets[0].data[0].y;

      // Update ideal burndown line
      updatedChart.data.datasets[0].data = [
        { x: startDate.toISOString().split('T')[0], y: totalPoints },
      ];

      // Calculate points for each day
      for (let i = 1; i < days; i++) {
        const date = new Date(startDate);
        date.setDate(date.getDate() + i);
        const idealPoints = totalPoints - (totalPoints / (days - 1)) * i;
        updatedChart.data.datasets[0].data.push({
          x: date.toISOString().split('T')[0],
          y: idealPoints,
        });
      }

      // Adjust actual burndown data points
      const actualData = updatedChart.data.datasets[1].data;
      while (actualData.length < days) {
        const lastDate = new Date(actualData[actualData.length - 1].x);
        const nextDate = new Date(lastDate);
        nextDate.setDate(nextDate.getDate() + 1);
        actualData.push({
          x: nextDate.toISOString().split('T')[0],
          y: actualData[actualData.length - 1].y,
        });
      }
      while (actualData.length > days) {
        actualData.pop();
      }

      // Update the UI first for immediate feedback
      onChartUpdate(updatedChart);
    } catch (error) {
      console.error('Error adjusting sprint duration:', error);
      toast.error('Failed to adjust sprint duration');
    }
  };

  const columnDefs: ColDef[] = [
    {
      field: 'date',
      headerName: 'Date',
      editable: true,
      cellEditor: 'agDateCellEditor',
      cellEditorParams: {
        browserDatePicker: true,
        parseValue: (value: string) => new Date(value),
      },
      valueGetter: (params: any) => {
        return params.data.date ? new Date(params.data.date) : null;
      },
      valueFormatter: (params: any) => {
        if (!params.value) return '';
        return params.value.toLocaleDateString();
      },
      valueSetter: (params: any) => {
        try {
          // Ensure we have a valid date
          if (!params.newValue) {
            return false;
          }

          // Format as ISO string with UTC timezone
          const dateValue = params.newValue.toISOString().split('T')[0];

          // Get the row index
          const rowIndex = params.node?.rowIndex || 0;

          // Update both datasets with the new date
          const updatedChart = JSON.parse(JSON.stringify(selectedChart));
          updatedChart.data.datasets[0].data[rowIndex].x = dateValue;
          updatedChart.data.datasets[1].data[rowIndex].x = dateValue;

          // Update the chart in the UI first for immediate feedback
          onChartUpdate(updatedChart);

          // Update the data in the grid
          params.data.date = dateValue;

          // Save to the backend
          try {
            const updatedChartPayload = chartPayloadHandler(
              updatedChart,
              'burndown',
              sketchbookId,
              activePage
            );

            updateCustomCharts({
              id: selectedChart.id,
              payload: updatedChartPayload,
              chartType: 'burndown',
            }).then((response: any) => {
              if (response?.data?.success) {
                toast.success('Date updated successfully');
              }
            });
          } catch (error) {
            console.error('Error saving date change to backend:', error);
            // Don't show error toast here as the UI update still worked
          }

          return true;
        } catch (error) {
          console.error('Error setting date value:', error);
          toast.error('Invalid date format');
          return false;
        }
      },
    },
    { field: 'ideal', headerName: 'Ideal Points', editable: true },
    { field: 'actual', headerName: 'Actual Points', editable: true },
  ];

  const rowData: any[] =
    selectedChart?.data?.datasets?.[0]?.data?.map(
      (point: any, index: number) => ({
        date: point?.x || new Date().toISOString(),
        ideal: selectedChart?.data?.datasets?.[0]?.data?.[index]?.y || 0,
        actual: selectedChart?.data?.datasets?.[1]?.data?.[index]?.y || 0,
      })
    ) || [];

  const handleCalendarClose = async (chartToUpdate = selectedChart) => {
    try {
      if (!chartToUpdate?.data?.datasets?.[0]?.data?.[0]?.x) {
        return;
      }

      // Create a deep copy to avoid reference issues
      const updatedChart = JSON.parse(JSON.stringify(chartToUpdate));

      // Create the payload
      const updatedChartPayload = chartPayloadHandler(
        updatedChart,
        'burndown',
        sketchbookId,
        activePage
      );

      // Send to backend
      try {
        const response: any = await updateCustomCharts({
          id: updatedChart.id,
          payload: updatedChartPayload,
          chartType: 'burndown',
        });

        if (response?.data?.success) {
          toast.success('Sprint start date updated successfully');
        } else {
          // Try to update the UI anyway
          onChartUpdate(updatedChart);
        }
      } catch (error) {
        toast.error('Failed to update sprint start date on the server');
        // Try to update the UI anyway
        onChartUpdate(updatedChart);
      }
    } catch (error) {
      console.error('Error updating sprint start date:', error);
      toast.error('Failed to update sprint start date');
    }
  };

  // Initialize chart data if empty
  useEffect(() => {
    if (!selectedChart?.data?.datasets?.length) {
      const updatedChart = JSON.parse(JSON.stringify(selectedChart));
      const today = new Date().toISOString().split('T')[0];
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 7);
      const futureDateStr = futureDate.toISOString().split('T')[0];

      // Initialize data object if not present
      updatedChart.data = updatedChart.data || {};

      // Initialize ideal line
      updatedChart.data.datasets = [
        {
          label: 'Ideal',
          data: [
            { x: today, y: 100 },
            { x: futureDateStr, y: 0 },
          ],
          borderColor: 'rgba(197, 170, 255, 1)',
          backgroundColor: 'rgba(197, 170, 255, 0.7)',
          borderWidth: 1,
          fill: false,
          isBurndown: true,
        },
        {
          label: 'Actual',
          data: [
            { x: today, y: 100 },
            { x: futureDateStr, y: 50 },
          ],
          borderColor: 'rgba(255, 99, 132, 1)',
          backgroundColor: 'rgba(255, 99, 132, 0.7)',
          borderWidth: 1,
          fill: true,
          isBurndown: true,
        },
      ];

      // Set title if not set
      if (!updatedChart.title) {
        updatedChart.title = 'Burndown Chart';
      }

      onChartUpdate(updatedChart);
    }
  }, [selectedChart, onChartUpdate]);

  return (
    <div className={styles.section}>
      <div className={styles.heading}>Burndown Chart Settings</div>

      {/* Chart Title Controls */}
      <div className={styles.controlGroup}>
        <TextField
          fullWidth
          label="Chart Title"
          value={selectedChart?.title || ''}
          onChange={(e) => updateChartOption(['title'], e.target.value)}
          onBlur={async (e) => {
            const updatedChart = JSON.parse(JSON.stringify(selectedChart));
            updatedChart.title = e.target.value;
            const updatedChartPayload = chartPayloadHandler(
              updatedChart,
              'burndown',
              sketchbookId,
              activePage
            );
            const response: any = await updateCustomCharts({
              id: selectedChart.id,
              payload: updatedChartPayload,
              chartType: 'burndown',
            });
            if (response?.data?.success) {
              toast.success('Chart title updated successfully');
            }
          }}
          margin="normal"
        />
      </div>

      {/* Sprint Settings */}
      <div className={styles.controlGroup}>
        <div className={styles.heading}>Sprint Settings</div>
        <FormControl fullWidth margin="normal">
          <DatePicker
            selected={
              selectedChart?.data?.datasets?.[0]?.data?.[0]?.x
                ? new Date(selectedChart.data.datasets[0].data[0].x)
                : new Date()
            }
            onChange={(date) => {
              if (date && selectedChart?.data?.datasets) {
                try {
                  // Create a deep copy of the chart
                  const updatedChart = JSON.parse(
                    JSON.stringify(selectedChart)
                  );
                  const sprintDuration =
                    updatedChart.data.datasets[0].data.length;
                  const startDate = date;

                  // Update dates for both datasets
                  for (let i = 0; i < sprintDuration; i++) {
                    const currentDate = new Date(startDate);
                    currentDate.setDate(currentDate.getDate() + i);
                    const dateStr = currentDate.toISOString().split('T')[0];
                    updatedChart.data.datasets[0].data[i].x = dateStr;
                    updatedChart.data.datasets[1].data[i].x = dateStr;
                  }

                  // Log the updated chart for debugging
                  console.log(
                    'Updated chart with new dates:',
                    JSON.stringify(updatedChart)
                  );

                  // Update the chart in the UI
                  onChartUpdate(updatedChart);

                  // Save changes to the backend with the updated chart
                  handleCalendarClose(updatedChart);
                } catch (error) {
                  console.error('Error updating sprint dates:', error);
                  toast.error('Failed to update sprint dates');
                }
              }
            }}
            dateFormat="MM/dd/yyyy"
            popperClassName={styles.datePickerPopper}
            popperContainer={({ children }) => (
              <div
                className={styles.datePickerWrapper}
                style={{ zIndex: 9999 }}
              >
                {children}
              </div>
            )}
            popperPlacement="bottom-start"
            customInput={<TextField label="Sprint Start Date" />}
          />
        </FormControl>
        <TextField
          fullWidth
          type="number"
          label="Sprint Duration (days)"
          value={selectedChart?.data?.datasets?.[0]?.data?.length || 2}
          onChange={(e) => {
            const days = Math.max(2, parseInt(e.target.value) || 2);
            adjustSprintDuration(days);
          }}
          onBlur={async () => {
            try {
              const updatedChart = JSON.parse(JSON.stringify(selectedChart));
              const updatedChartPayload = chartPayloadHandler(
                updatedChart,
                'burndown',
                sketchbookId,
                activePage
              );
              const response: any = await updateCustomCharts({
                id: selectedChart.id,
                payload: updatedChartPayload,
                chartType: 'burndown',
              });
              if (response?.data?.success) {
                toast.success('Sprint duration updated successfully');
              }
            } catch (error) {
              console.error('Error updating sprint duration:', error);
              toast.error('Failed to update sprint duration');
            }
          }}
          margin="normal"
        />
        <TextField
          fullWidth
          type="number"
          label="Total Story Points"
          value={selectedChart?.data?.datasets?.[0]?.data?.[0]?.y || 0}
          onChange={(e) => {
            if (!selectedChart?.data?.datasets) return;

            const updatedChart = JSON.parse(JSON.stringify(selectedChart));
            const value = Number(e.target.value);
            const sprintDuration = updatedChart.data.datasets[0].data.length;

            // Update ideal burndown
            for (let i = 0; i < sprintDuration; i++) {
              updatedChart.data.datasets[0].data[i].y =
                value - (value / (sprintDuration - 1)) * i;
            }

            // Update first actual value
            updatedChart.data.datasets[1].data[0].y = value;
            onChartUpdate(updatedChart);
          }}
          onBlur={async () => {
            try {
              const updatedChart = JSON.parse(JSON.stringify(selectedChart));
              const updatedChartPayload = chartPayloadHandler(
                updatedChart,
                'burndown',
                sketchbookId,
                activePage
              );
              const response: any = await updateCustomCharts({
                id: selectedChart.id,
                payload: updatedChartPayload,
                chartType: 'burndown',
              });
              if (response?.data?.success) {
                toast.success('Total story points updated successfully');
              }
            } catch (error) {
              console.error('Error updating story points:', error);
              toast.error('Failed to update story points');
            }
          }}
          margin="normal"
        />
      </div>

      {/* Visual Settings */}
      <div className={styles.controlGroup}>
        <div className={styles.heading}>Visual Settings</div>
        <div className={styles.datasetSettings}>
          <div className={styles.subheading}>Ideal Line</div>
          <div className={styles.colorPicker}>
            <label>Line Color</label>
            <input
              type="color"
              value={
                selectedChart?.data?.datasets?.[0]?.borderColor || '#000000'
              }
              onChange={(e) => {
                if (!selectedChart?.data?.datasets) return;

                const updatedChart = JSON.parse(JSON.stringify(selectedChart));
                updatedChart.data.datasets[0].borderColor = e.target.value;
                updatedChart.data.datasets[0].backgroundColor = `${e.target.value}33`;
                onChartUpdate(updatedChart);
              }}
              onBlur={async () => {
                try {
                  const updatedChart = JSON.parse(
                    JSON.stringify(selectedChart)
                  );
                  const updatedChartPayload = chartPayloadHandler(
                    updatedChart,
                    'burndown',
                    sketchbookId,
                    activePage
                  );
                  const response: any = await updateCustomCharts({
                    id: selectedChart.id,
                    payload: updatedChartPayload,
                    chartType: 'burndown',
                  });
                  if (response?.data?.success) {
                    toast.success('Ideal line color updated successfully');
                  }
                } catch (error) {
                  console.error('Error updating ideal line color:', error);
                  toast.error('Failed to update ideal line color');
                }
              }}
            />
          </div>
        </div>

        <div className={styles.datasetSettings}>
          <div className={styles.subheading}>Actual Progress</div>
          <FormControlLabel
            control={
              <Switch
                checked={selectedChart?.data?.datasets?.[1]?.fill || false}
                onChange={(e: any) => {
                  if (!selectedChart?.data?.datasets) return;

                  const updatedChart = JSON.parse(
                    JSON.stringify(selectedChart)
                  );
                  updatedChart.data.datasets[1].fill = e.target.checked;
                  onChartUpdate(updatedChart);
                }}
                onBlur={async (e) => {
                  try {
                    const updatedChart = JSON.parse(
                      JSON.stringify(selectedChart)
                    );
                    const updatedChartPayload = chartPayloadHandler(
                      updatedChart,
                      'burndown',
                      sketchbookId,
                      activePage
                    );
                    const response: any = await updateCustomCharts({
                      id: selectedChart.id,
                      payload: updatedChartPayload,
                      chartType: 'burndown',
                    });
                    if (response?.data?.success) {
                      toast.success('Area fill setting updated successfully');
                    }
                  } catch (error) {
                    console.error('Error updating area fill setting:', error);
                    toast.error('Failed to update area fill setting');
                  }
                }}
              />
            }
            label="Show Area Fill"
          />
          <div className={styles.colorPicker}>
            <label>Line Color</label>
            <input
              type="color"
              value={
                selectedChart?.data?.datasets?.[1]?.borderColor || '#000000'
              }
              onChange={(e: any) => {
                if (!selectedChart?.data?.datasets) return;

                const updatedChart = JSON.parse(JSON.stringify(selectedChart));
                updatedChart.data.datasets[1].borderColor = e.target.value;
                updatedChart.data.datasets[1].backgroundColor = `${e.target.value}33`;
                onChartUpdate(updatedChart);
              }}
              onBlur={async () => {
                try {
                  const updatedChart = JSON.parse(
                    JSON.stringify(selectedChart)
                  );
                  const updatedChartPayload = chartPayloadHandler(
                    updatedChart,
                    'burndown',
                    sketchbookId,
                    activePage
                  );
                  const response: any = await updateCustomCharts({
                    id: selectedChart.id,
                    payload: updatedChartPayload,
                    chartType: 'burndown',
                  });
                  if (response?.data?.success) {
                    toast.success('Actual line color updated successfully');
                  }
                } catch (error) {
                  console.error('Error updating actual line color:', error);
                  toast.error('Failed to update actual line color');
                }
              }}
            />
          </div>
        </div>
      </div>

      {/* Data Grid */}
      <div className={styles.controlGroup}>
        <div className={styles.heading}>Sprint Data</div>
        <div className="ag-theme-alpine" style={{ height: 400, width: '100%' }}>
          <AgGridReact
            columnDefs={columnDefs}
            rowData={rowData}
            onCellValueChanged={(params: any) => {
              const { colDef, newValue } = params;
              const rowIndex = params.node?.rowIndex || 0;

              if (colDef.field === 'ideal') {
                updateBurndownData(0, rowIndex, Number(newValue));
              } else if (colDef.field === 'actual') {
                updateBurndownData(1, rowIndex, Number(newValue));
              }
              // Date changes are handled in the valueSetter
            }}
          />
        </div>
        <button
          className={styles.addButton}
          onClick={async () => {
            const updatedChart = JSON.parse(JSON.stringify(selectedChart));
            const lastDate = new Date(
              updatedChart.data.datasets[0].data[
                updatedChart.data.datasets[0].data.length - 1
              ].x
            );
            const nextDate = new Date(lastDate);
            nextDate.setDate(nextDate.getDate() + 1);

            // Add point to ideal line - always set to 0 for the last day
            const idealPoint = 0;

            updatedChart.data.datasets[0].data.push({
              x: nextDate.toISOString().split('T')[0],
              y: idealPoint,
            });

            // Add point to actual line
            updatedChart.data.datasets[1].data.push({
              x: nextDate.toISOString().split('T')[0],
              y: updatedChart.data.datasets[1].data[
                updatedChart.data.datasets[1].data.length - 1
              ].y,
            });

            const updatedChartPayload = chartPayloadHandler(
              updatedChart,
              'burndown',
              sketchbookId,
              activePage
            );
            const response: any = await updateCustomCharts({
              id: selectedChart.id,
              payload: updatedChartPayload,
              chartType: 'burndown',
            });
            if (response?.data?.success) {
              toast.success('Sprint data updated successfully');
            }
            onChartUpdate(updatedChart);
          }}
        >
          Add Day
        </button>
      </div>
    </div>
  );
};

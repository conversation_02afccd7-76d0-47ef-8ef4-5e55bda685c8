import React, { useState, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDropzone } from 'react-dropzone';
import ProjectDialog from '../../common/ProjectDialog/ProjectDialog';
import styles from './FileUpload.module.css';
import star from '../../../assets/images/star.png';
import { RiFileUploadFill } from 'react-icons/ri';
import { FaFileUpload } from 'react-icons/fa';
import upload from '../../../assets/images/UploadIcon.png';
import Loader from '../loader/Loader';
import { useUploadFileMutation } from '../../../services/chatServices';
import { countPagesInFile } from '../../../utils/pageCounter';
import useLocalStorage from '../../../hooks/useLocalStorage';
import toast from 'react-hot-toast';
import { getSimplifiedFileExtension } from '../../../utils/getFileExtention';
import { createFormData } from '../../../utils/formDataHelper';
import { useSelector } from 'react-redux';
import { RootState } from '../../../store/store';

// Add these type definitions at the top of the file
interface UploadResponse {
  summary: string;
  tables: any[]; // Update this type based on your actual table structure
  suggested_questions: string[];
  object_id: string;
}

interface UploadError {
  status?: number;
  data?: {
    message?: string;
  };
}

// Function to get engaging upload messages based on progress percentage
const getUploadMessage = (progress: number): string => {
  if (progress === 0) return 'Preparing your file for upload...';

  // Array of engaging messages for different progress stages
  const messages = [
    // 1-10%
    [
      'Starting the journey of your file to our servers...',
      'Initiating upload sequence...',
      'Your file is getting ready for its big adventure!',
      'Warming up the upload engines...',
    ],
    // 11-25%
    [
      'Your file is making progress! 🚀',
      'Transferring your valuable data...',
      'Bits and bytes are flowing smoothly!',
      'Your file is on its way to greatness!',
    ],
    // 26-50%
    [
      'Making good progress! Keep the faith!',
      'Your file is cruising along nicely!',
      "We're getting there! Your file is halfway to its destination!",
      'Halfway point reached! Your file is doing great!',
    ],
    // 51-75%
    [
      'More than halfway there! Your file is a champion!',
      'The finish line is coming into view!',
      'Your file is performing excellently!',
      'Almost there! Your file is working hard!',
    ],
    // 76-99%
    [
      'Final stretch! Your file is almost home!',
      'So close now! Just a few more moments...',
      'Success is just around the corner!',
      'The upload is nearly complete! Hang in there!',
    ],
    // 100%
    [
      'Upload complete! Now processing your file...',
      'Your file made it! Now working our magic...',
      'Upload successful! Preparing your data...',
      'Great job! Your file is now being processed...',
    ],
  ];

  // Select message category based on progress percentage
  let categoryIndex;
  if (progress >= 100) categoryIndex = 5;
  else if (progress >= 76) categoryIndex = 4;
  else if (progress >= 51) categoryIndex = 3;
  else if (progress >= 26) categoryIndex = 2;
  else if (progress >= 11) categoryIndex = 1;
  else categoryIndex = 0;

  // Select a random message from the appropriate category
  const selectedCategory = messages[categoryIndex];
  const randomIndex = Math.floor(Math.random() * selectedCategory.length);

  return `${progress}% - ${selectedCategory[randomIndex]}`;
};

const FileUpload: React.FC = () => {
  const localstorage = useLocalStorage('user', null);
  const [loading, setLoading] = useState(false);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [rangeInput, setRangeInput] = useState('');
  const [title, setTitle] = useState('');
  const responseType = useSelector(
    (state: RootState) => state.settings.responseType
  );

  const navigate = useNavigate();

  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadStatus, setUploadStatus] = useState<
    'uploading' | 'processing' | null
  >(null);
  const [uploadFile, { isLoading }] = useUploadFileMutation();

  const { getRootProps, getInputProps } = useDropzone({
    accept: {
      'application/pdf': ['.pdf'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
        ['.docx'],
      'application/msword': ['.doc'],
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': [
        '.xlsx',
      ],
      // 'application/vnd.ms-excel': ['.xls'],
      'text/csv': ['.csv'],
      'text/plain': ['.txt'],
    },
    onDrop: (acceptedFiles, fileRejections) => {
      if (fileRejections.length > 0) {
        toast.error('Only PDF, Word, Excel, CSV, and Text files are allowed.');
        return;
      }
      setSelectedFile(acceptedFiles[0]);
      setOpenDialog(true);
    },
  });

  const handleProjectSelection = async (projectId: string, pageRanges: any) => {
    try {
      if (!selectedFile) {
        throw new Error('Please select a file');
      }

      if (!projectId) {
        throw new Error('Project ID is required');
      }

      if (!localstorage?.[0]?.id) {
        throw new Error('User ID is required');
      }

      setLoading(true);
      setUploadStatus('uploading');

      const numberOfPages = await countPagesInFile(selectedFile);
      const fileExtention = getSimplifiedFileExtension(selectedFile);

      if (!fileExtention) {
        throw new Error('Invalid file type');
      }

      const formData = createFormData({
        userId: localstorage[0].id,
        projectId,
        pageNumber: rangeInput,
        fileName: selectedFile.name,
        fileType: fileExtention,
        title,
        prompt: ' ',
        file: selectedFile,
        response_type: responseType,
      });

      const result = await uploadFile({
        formData,
        onProgress: (progress: number, status: 'uploading' | 'processing') => {
          setUploadProgress(progress);
          setUploadStatus(status);
        },
      }).unwrap();

      const uploadResponse = result as any;

      toast.success('File Uploaded', { duration: 6000 });
      navigate('/chat', {
        state: {
          summary: uploadResponse.summary,
          tables: uploadResponse.tables,
          questions: uploadResponse.suggested_questions,
          file: selectedFile,
          numberOfPages,
          insightProjectId: uploadResponse.object_id,
          projectId,
          pageRange: pageRanges,
          uploadFileTitle: title,
          fileUpload: true,
        },
      });
    } catch (error) {
      console.error('File upload failed:', error);
      toast.error(
        error instanceof Error ? error.message : 'File upload failed',
        { duration: 6000 }
      );
    } finally {
      setLoading(false);
      setOpenDialog(false);
      setUploadStatus(null);
    }
  };

  return (
    <>
      <div className={styles.dropzoneContainer} {...getRootProps()}>
        <input {...getInputProps()} />
        <>
          <div className={styles.uploadContent}>
            <FaFileUpload className={styles.uploadIcon} />

            {isLoading || loading ? (
              <div className={styles.loadingContainer}>
                <Loader loading={uploadProgress} />
                <p className={styles.uploadStatus}>
                  {uploadStatus === 'processing'
                    ? 'Analyzing your file, please wait...'
                    : getUploadMessage(uploadProgress)}
                </p>
              </div>
            ) : (
              <div className={styles.uploadPrompt}>
                <p className={styles.dragDropText}>
                  Drag and drop your file here
                  <span className={styles.orText}>or</span>
                </p>

                <button className={styles.uploadButton}>
                  <FaFileUpload className={styles.buttonIcon} />
                  Choose File
                </button>
              </div>
            )}
          </div>
        </>
      </div>

      {openDialog && (
        <ProjectDialog
          onClose={() => setOpenDialog(false)}
          onSelectProject={handleProjectSelection}
          rangeInput={rangeInput}
          setRangeInput={setRangeInput}
          title={title}
          setTitle={setTitle}
          isLoading={loading}
        />
      )}
    </>
  );
};

export default FileUpload;

import React, { useState } from 'react';
import { PaymentRecord } from '../../types/payment';
import { useTheme } from '../../contexts/ThemeContext';
import InvoiceStatusBadge from './InvoiceStatusBadge';
import { downloadReceipt } from '../../utils/receiptGenerator';
import styles from './PaymentDetailsModal.module.css';

interface PaymentDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  payment: PaymentRecord | null;
}

const PaymentDetailsModal: React.FC<PaymentDetailsModalProps> = ({
  isOpen,
  onClose,
  payment,
}) => {
  const { isDarkMode } = useTheme();
  const [downloadFormat, setDownloadFormat] = useState<'txt' | 'csv' | 'html'>(
    'txt'
  );

  if (!isOpen || !payment) return null;

  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return 'N/A';
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        timeZoneName: 'short',
      });
    } catch (error) {
      return 'Invalid Date';
    }
  };

  const formatCurrency = (amount: number, currency: string = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency.toUpperCase(),
    }).format(amount);
  };

  const handleDownloadReceipt = () => {
    try {
      downloadReceipt(payment, downloadFormat, {
        companyName: 'NeuQuip',
        companyEmail: '<EMAIL>',
      });
    } catch (error) {
      console.error('Error downloading receipt:', error);
      alert('Failed to download receipt. Please try again.');
    }
  };

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div
      className={`${styles.overlay} ${isDarkMode ? styles.dark : ''}`}
      onClick={handleBackdropClick}
    >
      <div className={styles.modal}>
        <div className={styles.header}>
          <h2 className={styles.title}>Payment Details</h2>
          <button onClick={onClose} className={styles.closeButton}>
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
              <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z" />
            </svg>
          </button>
        </div>

        <div className={styles.content}>
          <div className={styles.section}>
            <h3 className={styles.sectionTitle}>Transaction Information</h3>
            <div className={styles.detailsGrid}>
              <div className={styles.detailItem}>
                <span className={styles.label}>Payment ID:</span>
                <span className={styles.value}>
                  {payment.paymentIntentId || payment.id}
                </span>
              </div>
              <div className={styles.detailItem}>
                <span className={styles.label}>Amount:</span>
                <span className={styles.value}>
                  {formatCurrency(payment.amount, payment.currency)}
                </span>
              </div>
              <div className={styles.detailItem}>
                <span className={styles.label}>Currency:</span>
                <span className={styles.value}>
                  {payment.currency.toUpperCase()}
                </span>
              </div>
              <div className={styles.detailItem}>
                <span className={styles.label}>Status:</span>
                <div className={styles.value}>
                  <InvoiceStatusBadge status={payment.status} />
                </div>
              </div>
              <div className={styles.detailItem}>
                <span className={styles.label}>Plan ID:</span>
                <span className={styles.value}>{payment.planId}</span>
              </div>
              <div className={styles.detailItem}>
                <span className={styles.label}>User ID:</span>
                <span className={styles.value}>{payment.userId}</span>
              </div>
            </div>
          </div>

          <div className={styles.section}>
            <h3 className={styles.sectionTitle}>Timeline</h3>
            <div className={styles.timeline}>
              {payment.createdOn && (
                <div className={styles.timelineItem}>
                  <div className={styles.timelineIcon}>📅</div>
                  <div className={styles.timelineContent}>
                    <div className={styles.timelineTitle}>Payment Created</div>
                    <div className={styles.timelineDate}>
                      {formatDate(payment.createdOn)}
                    </div>
                  </div>
                </div>
              )}
              {payment.dated && (
                <div className={styles.timelineItem}>
                  <div className={styles.timelineIcon}>💳</div>
                  <div className={styles.timelineContent}>
                    <div className={styles.timelineTitle}>
                      Payment Processed
                    </div>
                    <div className={styles.timelineDate}>
                      {formatDate(payment.dated)}
                    </div>
                  </div>
                </div>
              )}
              {payment.updatedOn && payment.updatedOn !== payment.createdOn && (
                <div className={styles.timelineItem}>
                  <div className={styles.timelineIcon}>🔄</div>
                  <div className={styles.timelineContent}>
                    <div className={styles.timelineTitle}>Last Updated</div>
                    <div className={styles.timelineDate}>
                      {formatDate(payment.updatedOn)}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        <div className={styles.footer}>
          {payment.status === 'succeeded' && (
            <div className={styles.downloadSection}>
              <select
                value={downloadFormat}
                onChange={(e) =>
                  setDownloadFormat(e.target.value as 'txt' | 'csv' | 'html')
                }
                className={styles.formatSelect}
              >
                <option value="txt">Text (.txt)</option>
                <option value="html">HTML (.html)</option>
                <option value="csv">CSV (.csv)</option>
              </select>
              <button
                onClick={handleDownloadReceipt}
                className={styles.downloadButton}
              >
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                >
                  <path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z" />
                </svg>
                Download Receipt
              </button>
            </div>
          )}
          <button onClick={onClose} className={styles.closeButtonSecondary}>
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default PaymentDetailsModal;

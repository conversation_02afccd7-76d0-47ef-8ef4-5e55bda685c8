import React, { useState, useEffect, useRef } from 'react';
import { <PERSON><PERSON><PERSON>rum<PERSON>, Link, Tooltip, IconButton } from '@mui/material';
import { Link as RouterLink, useNavigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { RootState } from '../../../store/store';
import { PDFDocument } from 'pdf-lib';
import { saveAs } from 'file-saver';
import html2canvas from 'html2canvas';
import toast from 'react-hot-toast';
import { SketchPicker } from 'react-color';
import {
  FaEye,
  FaHome,
  FaPalette,
  FaFileExport,
  FaEdit,
  FaSave,
} from 'react-icons/fa';
import { BsFillHddNetworkFill } from 'react-icons/bs';
import { ActionIcon } from '../ActionIcon';
// PreviewDialog is replaced by EditorPreview
import FlowPreviewDialog from './components/FlowPreviewDialog';
import { ShareDialog } from '../ShareDialog/ShareDialog';
import styles from './BreadCrumbsComponent.module.css';
import separator from '../../../assets/images/svg/separator.svg';
import useLocalStorage from '../../../hooks/useLocalStorage';
// renderChartToCanvas is no longer needed as we use EditorPreview
import {
  useSaveFileForEmailMutation,
  useSendEmailWithAttachmentMutation,
  useUpdateSketchbookMutation,
  useUpdateSketchbookNameMutation,
} from '../../../services/sketchbookServices';
import { useGetWorkflowByProjectIdQuery } from '../../../services/workflowServices';
import EditorPreview from './components/EditorPreview';
import SaveAsTemplateModal from '../../specific/sketchBookControler/SaveAsTemplateModal';

interface BreadCrumbsProps {
  projectTitle: string;
  onColorChange: (color: string) => void;
  sketchbookId: string;
  projectId: string;
  mongoIdOfInsightsForComplinces: string;
  sketchbookIdFromCurrentSketchbook: string;
  isFlowMode?: boolean;
}

// These interfaces are handled by the EditorPreview component

const BreadcrumbsComponent: React.FC<BreadCrumbsProps> = ({
  projectTitle,
  onColorChange,
  sketchbookId,
  projectId,
  sketchbookIdFromCurrentSketchbook,
  mongoIdOfInsightsForComplinces,
  isFlowMode = false,
}) => {
  const navigate = useNavigate();
  const [currentUser] = useLocalStorage('user', null);
  const titleRef = useRef<HTMLSpanElement>(null);
  const [editableTitle, setEditableTitle] = useState(
    projectTitle || 'Untitled Sketchbook'
  );

  // API hooks
  const [saveFileForEmail] = useSaveFileForEmailMutation();
  const [sendEmailWithAttachment] = useSendEmailWithAttachmentMutation();
  const [updateSketchbook] = useUpdateSketchbookMutation();
  const [updateSketchbookName] = useUpdateSketchbookNameMutation();
  const { data: workflowData } = useGetWorkflowByProjectIdQuery(
    { projectId },
    { skip: !projectId }
  );

  // Redux state
  const sketchbookState = useSelector((state: RootState) => state.sketchbook);
  const { pages, charts, layouts, pageSize, flowNodes, flowEdges } =
    sketchbookState;

  // Update editable title when projectTitle changes
  useEffect(() => {
    setEditableTitle(projectTitle || 'Untitled Sketchbook');
  }, [projectTitle]);

  // Local state
  const [color, setColor] = useState('#f5f5f5');
  const [colorPickerOpen, setColorPickerOpen] = useState(false);
  const [previewOpen, setPreviewOpen] = useState(false);
  const [flowPreviewOpen, setFlowPreviewOpen] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [pendingExport, setPendingExport] = useState<'PDF' | 'DOC' | null>(
    null
  );
  const [shareDialogOpen, setShareDialogOpen] = useState(false);
  const [uploadedFileName, setUploadedFileName] = useState('');
  const [generatedPdfBytes, setGeneratedPdfBytes] =
    useState<ArrayBuffer | null>(null);
  const [saveAsTemplateOpen, setSaveAsTemplateOpen] = useState(false);
  const [isBlankSketchbook, setIsBlankSketchbook] = useState(true);

  // Determine if this is a blank sketchbook (created from blank template)
  // A sketchbook is considered blank if it has no charts or only has empty pages
  useEffect(() => {
    const hasCharts = Object.values(charts).some(
      (pageCharts) =>
        pageCharts && Array.isArray(pageCharts) && pageCharts.length > 0
    );
    setIsBlankSketchbook(!hasCharts);
  }, [charts]);

  // Load color from localStorage on mount
  useEffect(() => {
    const savedColor = localStorage.getItem('selectedColor');
    if (savedColor) {
      setColor(savedColor);
      onColorChange(savedColor);
    } else {
      // Set default gray color if no saved color
      onColorChange('#f5f5f5');
    }
  }, [onColorChange]);

  const handleColorChange = (color: any) => {
    setColor(color.hex);
    onColorChange(color.hex);
    localStorage.setItem('selectedColor', color.hex);
  };

  const toggleColorPicker = () => {
    setColorPickerOpen((prev) => !prev);
  };

  const handlePreviewClick = () => {
    setPreviewOpen(true);
  };

  const handleSaveAsTemplateClick = () => {
    setSaveAsTemplateOpen(true);
  };

  // handleShareClick is no longer needed as we're using the ShareDialog in EditorPreview
  /*
  const handleShareClick = async () => {
    try {
      setPreviewOpen(true);
      await new Promise((resolve) => setTimeout(resolve, 1500));
      const pdfBytes = await exportAsPDF('email');
      if (!pdfBytes) {
        throw new Error('PDF generation failed');
      }

      // Store the generated PDF bytes
      setGeneratedPdfBytes(pdfBytes);

      // Create file object for sharing
      const file = new Blob([pdfBytes], { type: 'application/pdf' });
      const fileName = `${projectTitle || 'NEUQUIP-project'}.pdf`;

      // Prepare for sharing
      const formData = new FormData();
      formData.append('file', file, fileName);

      const response = await saveFileForEmail({
        payload: formData,
      }).unwrap();

      if (response.success) {
        setUploadedFileName(fileName);
        setShareDialogOpen(true);
        toast.success('File prepared for sharing');
      } else {
        toast.error('Failed to prepare file for sharing');
      }
    } catch (error: any) {
      console.error('Error in handleShareClick:', error);
      toast.error(error.data?.message || 'Failed to prepare file for sharing');
    } finally {
      setPreviewOpen(false);
    }
  };
  */

  const handleDirectDownload = async () => {
    try {
      // If we already have generated PDF bytes, use them
      if (!generatedPdfBytes) {
        setPreviewOpen(true);
        await new Promise((resolve) => setTimeout(resolve, 1500));
        const pdfBytes: any = await exportAsPDF('email');
        if (!pdfBytes) {
          throw new Error('PDF generation failed');
        }
        setGeneratedPdfBytes(pdfBytes);
        setPreviewOpen(false);
      }

      if (!generatedPdfBytes) {
        throw new Error('PDF generation failed');
      }

      // Create file and trigger download
      const file = new Blob([generatedPdfBytes], { type: 'application/pdf' });
      const fileName = `${projectTitle || 'NEUQUIP-project'}.pdf`;
      saveAs(file, fileName);
      toast.success('File downloaded successfully');
    } catch (error: any) {
      console.error('Error in handleDirectDownload:', error);
      toast.error('Failed to download file');
    } finally {
      setShareDialogOpen(false);
    }
  };

  const handleCloseShareDialog = () => {
    setShareDialogOpen(false);
    setGeneratedPdfBytes(null); // Clear stored PDF bytes
  };

  const handleShareWorkflow = () => {
    const existingWorkflows = workflowData?.data.workflows.filter(
      (workflow: any) => workflow.creatorId === currentUser.id
    );

    if (!existingWorkflows) {
      navigate('/workflow', {
        state: { projectTitle, sketchbookId, projectId },
      });
      return;
    }

    const conflictingWorkflow = existingWorkflows.find(
      (workflow: any) =>
        workflow.approvalStatus === 'in-progress' ||
        workflow.approvalStatus === 'approved'
    );

    if (conflictingWorkflow) {
      toast.error(
        conflictingWorkflow.approvalStatus === 'in-progress'
          ? 'You already have a workflow in progress for this project.'
          : 'You already have an approved workflow for this project.'
      );
      return;
    }

    const allRejected = existingWorkflows.every(
      (workflow: any) => workflow.approvalStatus === 'rejected'
    );

    if (allRejected || existingWorkflows.length === 0) {
      navigate('/workflow', {
        state: { projectTitle, sketchbookId, projectId },
      });
      return;
    }

    toast.error('An unexpected error occurred. Please try again.');
  };

  const handleSendEmail = async (emailData: {
    sendToEmails: string[];
    ccToEmails: string[];
    subject: string;
    content: string;
    hasAttachment: boolean;
    fileNames: string[];
  }) => {
    try {
      const response = await sendEmailWithAttachment({
        payload: emailData,
      }).unwrap();

      if (response.success) {
        toast.success('Email sent successfully');
        setShareDialogOpen(false);
      } else {
        toast.error('Failed to send email');
      }
    } catch (error: any) {
      console.error('Error sending email:', error);
      toast.error(error.data?.message || 'Failed to send email');
    }
  };

  const handleExport = (type: 'email' | 'saveToDisk', pdfData?: Uint8Array) => {
    // If we received PDF data from the EditorPreview component, use it directly
    if (pdfData) {
      if (type === 'email') {
        // For email, we need to save the file and open the email dialog
        handleEmailExport(pdfData);
      } else {
        // For direct download, save the PDF to disk
        handleDirectDownloadExport(pdfData);
      }
    } else {
      // If no PDF data was provided, trigger the PDF generation process
      exportAsPDF(type);
    }
  };

  const handleDirectDownloadExport = (pdfData: Uint8Array) => {
    try {
      // Create file object for download
      const file = new Blob([pdfData], { type: 'application/pdf' });
      const fileName = `${projectTitle || 'NEUQUIP-project'}.pdf`;

      // Create a download link
      const url = URL.createObjectURL(file);
      const a = document.createElement('a');
      a.href = url;
      a.download = fileName;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      // Show success message
      toast.success('PDF downloaded successfully');
    } catch (error) {
      console.error('Error in handleDirectDownloadExport:', error);
      toast.error('Failed to download PDF');
    } finally {
      setIsExporting(false);
      setPendingExport(null);
      setPreviewOpen(false);
    }
  };

  const handleEmailExport = async (pdfData: Uint8Array) => {
    try {
      // Create file object for sharing
      const file = new Blob([pdfData], { type: 'application/pdf' });
      const fileName = `${projectTitle || 'NEUQUIP-project'}.pdf`;

      // Prepare for sharing
      const formData = new FormData();
      formData.append('file', file, fileName);

      const response = await saveFileForEmail({
        payload: formData,
      }).unwrap();

      if (response.success) {
        setUploadedFileName(fileName);
        setShareDialogOpen(true); // Use ShareDialog instead of EmailDialog
        toast.success('File prepared for sharing');
      } else {
        toast.error('Failed to prepare file for sharing');
      }
    } catch (error: any) {
      console.error('Error in handleEmailExport:', error);
      toast.error(error.data?.message || 'Failed to prepare file for sharing');
    } finally {
      setIsExporting(false);
      setPendingExport(null);
      setPreviewOpen(false);
    }
  };

  /**
   * Export the current page as a PDF file.
   * This implementation uses the EditorPreview component to capture the content
   * and generate a high-quality PDF that exactly matches what the user sees.
   */
  const exportAsPDF = async (type: 'email' | 'saveToDisk') => {
    try {
      // Open the preview dialog first to ensure it's rendered
      setPreviewOpen(true);

      // Wait a moment for the preview to fully render
      await new Promise((resolve) => setTimeout(resolve, 500));

      // The actual PDF generation will be handled by the EditorPreview component
      // We just need to set up the state for it
      setIsExporting(true);
      setPendingExport(type === 'email' ? 'PDF' : null);

      // The actual PDF generation and handling happens in the EditorPreview component
      // which will call handleExport with the generated PDF data if needed
    } catch (error) {
      console.error('PDF export error:', error);
      toast.error('Failed to prepare for PDF generation. Please try again.');
      setIsExporting(false);
      setPendingExport(null);
      return null;
    }
  };

  /**
   * Print the current page.
   * This implementation uses the EditorPreview component to capture the content
   * and generate a high-quality printable document that exactly matches what the user sees.
   */
  const handlePrint = async () => {
    try {
      // Open the preview dialog first to ensure it's rendered
      setPreviewOpen(true);

      // Wait a moment for the preview to fully render
      await new Promise((resolve) => setTimeout(resolve, 500));

      // The actual printing will be handled by the EditorPreview component
      // We just need to set up the state for it
      setIsExporting(true);

      // The actual printing happens in the EditorPreview component
    } catch (error) {
      console.error('Print error:', error);
      toast.error('Failed to prepare document for printing');
      setIsExporting(false);
    }
  };

  const handleExportFlowChart = async () => {
    try {
      setIsExporting(true);

      const flowElement = document.querySelector('.react-flow__viewport');
      if (!flowElement) throw new Error('Flow chart container not found');

      // Get the actual bounds of all nodes and edges to ensure we capture everything
      const nodeElements = document.querySelectorAll('.react-flow__node');
      const edgeElements = document.querySelectorAll('.react-flow__edge-path');

      if (nodeElements.length === 0)
        throw new Error('No flow chart nodes found');

      // Calculate the bounding box that contains all nodes and edges
      let minX = Infinity,
        minY = Infinity,
        maxX = -Infinity,
        maxY = -Infinity;

      // Include nodes in bounding box
      nodeElements.forEach((node) => {
        const rect = node.getBoundingClientRect();
        minX = Math.min(minX, rect.left);
        minY = Math.min(minY, rect.top);
        maxX = Math.max(maxX, rect.right);
        maxY = Math.max(maxY, rect.bottom);
      });

      // Include edges in bounding box
      edgeElements.forEach((edge) => {
        const rect = edge.getBoundingClientRect();
        minX = Math.min(minX, rect.left);
        minY = Math.min(minY, rect.top);
        maxX = Math.max(maxX, rect.right);
        maxY = Math.max(maxY, rect.bottom);
      });

      // Add extra padding around the content to ensure edges are visible
      const padding = 150;
      const width = maxX - minX + padding * 2;
      const height = maxY - minY + padding * 2;

      // Ensure we're capturing the entire flow
      const canvas = await html2canvas(flowElement as HTMLElement, {
        backgroundColor: '#ffffff',
        scale: 3, // Increase scale for better quality
        useCORS: true,
        allowTaint: true,
        width: width,
        height: height,
        x: minX - padding - flowElement.getBoundingClientRect().left,
        y: minY - padding - flowElement.getBoundingClientRect().top,
        onclone: (clonedDoc, clonedElement) => {
          // Make sure all edges are visible in the cloned document
          const clonedEdges = clonedDoc.querySelectorAll(
            '.react-flow__edge-path'
          );
          clonedEdges.forEach((edge) => {
            (edge as SVGPathElement).style.strokeWidth = '2px';
            (edge as SVGPathElement).style.stroke = '#000';
          });
          return clonedElement;
        },
      });

      const pdfDoc = await PDFDocument.create();

      // Use A4 as base size
      const baseWidth = 595.276;
      const baseHeight = 841.89;

      // Calculate aspect ratio
      const aspectRatio = canvas.width / canvas.height;

      // Determine PDF dimensions to fit content properly
      let pdfWidth, pdfHeight;

      if (aspectRatio > 1) {
        // Landscape orientation
        pdfWidth = baseWidth;
        pdfHeight = pdfWidth / aspectRatio;

        // If height is too small, adjust
        if (pdfHeight < baseHeight / 2) {
          pdfHeight = baseHeight / 2;
          pdfWidth = pdfHeight * aspectRatio;
        }
      } else {
        // Portrait orientation
        pdfHeight = baseHeight;
        pdfWidth = pdfHeight * aspectRatio;

        // If width is too small, adjust
        if (pdfWidth < baseWidth / 2) {
          pdfWidth = baseWidth / 2;
          pdfHeight = pdfWidth / aspectRatio;
        }
      }

      const page = pdfDoc.addPage([pdfWidth, pdfHeight]);
      const pngImage = await pdfDoc.embedPng(canvas.toDataURL('image/png'));

      // Center the image on the page
      page.drawImage(pngImage, {
        x: 0,
        y: 0,
        width: pdfWidth,
        height: pdfHeight,
      });

      const pdfBytes = await pdfDoc.save();
      saveAs(
        new Blob([pdfBytes], { type: 'application/pdf' }),
        `${projectTitle || 'flow-chart'}_${new Date().toISOString().split('T')[0]}.pdf`
      );

      toast.success('Flow chart exported successfully');
      setFlowPreviewOpen(false);
    } catch (error) {
      console.error('Error exporting flow chart:', error);
      toast.error('Failed to export flow chart');
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <>
      <div className={styles.breadcrumbsContainer}>
        <Breadcrumbs
          separator={
            <img src={separator} alt="separator" className={styles.separator} />
          }
          className={styles.breadcrumbs}
          maxItems={3}
          itemsBeforeCollapse={0}
          itemsAfterCollapse={2}
        >
          <Link
            color="#677480"
            className={styles.link}
            component={RouterLink}
            to="/"
            title="Home"
          >
            <FaHome size={14} />
          </Link>
          <Link
            color="#677480"
            className={styles.link}
            component={RouterLink}
            sx={{ textDecoration: 'none' }}
            to="/sketchbooklists"
            title="Sketchbooks"
          >
            Sketchbooks
          </Link>
          <div className={styles.titleContainer}>
            <Tooltip title="Double-click to edit title">
              <span
                ref={titleRef}
                className={styles.editableTitle}
                onDoubleClick={(e) => {
                  const target = e.currentTarget;
                  target.contentEditable = 'true';
                  target.focus();
                }}
                onBlur={async (e) => {
                  const target = e.currentTarget;
                  target.contentEditable = 'false';
                  const newTitle = target.innerText.trim();

                  if (newTitle.length === 0) {
                    toast.error('Title cannot be empty');
                    target.innerText = editableTitle;
                    return;
                  }

                  if (newTitle !== editableTitle) {
                    try {
                      const toastId = toast.loading('Updating title...');
                      // Create a safe title with only alphanumeric characters, spaces, and common punctuation
                      const safeTitle = newTitle.replace(
                        /[^a-zA-Z0-9\s\-_.,!?()]/g,
                        ''
                      );

                      // Use the new updateSketchbookName endpoint
                      const response = await updateSketchbookName({
                        sketchbookId: sketchbookId,
                        sketchbook_name: safeTitle,
                      }).unwrap();

                      toast.dismiss(toastId);

                      if (response.success) {
                        setEditableTitle(safeTitle);
                        toast.success('Title updated successfully');
                      } else {
                        toast.error(
                          response.message || 'Failed to update title'
                        );
                        target.innerText = editableTitle;
                      }
                    } catch (error) {
                      console.error('Error updating title:', error);
                      toast.dismiss();
                      toast.error('Failed to update title');
                      target.innerText = editableTitle;
                    }
                  }
                }}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    if (titleRef.current) {
                      titleRef.current.blur();
                    }
                  }
                }}
              >
                {editableTitle}
              </span>
            </Tooltip>
            <Tooltip title="Edit title">
              <IconButton
                size="small"
                className={styles.editButton}
                onClick={() => {
                  if (titleRef.current) {
                    titleRef.current.contentEditable = 'true';
                    titleRef.current.focus();
                  }
                }}
              >
                <FaEdit size={14} />
              </IconButton>
            </Tooltip>
          </div>
        </Breadcrumbs>

        <div className={styles.buttonsContainer}>
          <div>
            <Tooltip title="Pick Color">
              <ActionIcon onClick={toggleColorPicker}>
                <FaPalette size={14} />
              </ActionIcon>
            </Tooltip>
            {colorPickerOpen && (
              <div className={styles.colorPickerContainer}>
                <SketchPicker
                  color={color}
                  onChangeComplete={handleColorChange}
                />
              </div>
            )}
          </div>
          {isFlowMode ? (
            <Tooltip title="Export Flow Chart">
              <ActionIcon onClick={() => setFlowPreviewOpen(true)}>
                <FaFileExport size={14} />
              </ActionIcon>
            </Tooltip>
          ) : (
            <>
              <Tooltip title="Preview">
                <ActionIcon onClick={handlePreviewClick}>
                  <FaEye size={14} />
                </ActionIcon>
              </Tooltip>
              {/* Only show Save as Template button for sketchbooks without a project ID */}
              {!sketchbookIdFromCurrentSketchbook && (
                <Tooltip title="Save as Template">
                  <ActionIcon onClick={handleSaveAsTemplateClick}>
                    <FaSave size={14} />
                  </ActionIcon>
                </Tooltip>
              )}
              <Tooltip title="Create Workflow">
                <ActionIcon onClick={handleShareWorkflow}>
                  <BsFillHddNetworkFill size={14} />
                </ActionIcon>
              </Tooltip>
            </>
          )}
        </div>
      </div>

      <EditorPreview
        projectId={mongoIdOfInsightsForComplinces}
        currentUser={currentUser}
        open={previewOpen}
        onClose={() => setPreviewOpen(false)}
        pages={pages}
        charts={charts}
        layouts={layouts}
        pageSize={pageSize}
        isExporting={isExporting}
        pendingExport={pendingExport}
        onPrint={handlePrint}
        onExport={handleExport}
        projectTitle={projectTitle}
      />

      <FlowPreviewDialog
        open={flowPreviewOpen}
        onClose={() => setFlowPreviewOpen(false)}
        nodes={flowNodes}
        edges={flowEdges}
        isExporting={isExporting}
        onExport={handleExportFlowChart}
      />

      <ShareDialog
        open={shareDialogOpen}
        onClose={handleCloseShareDialog}
        onSendEmail={handleSendEmail}
        onExport={handleDirectDownload}
        fileName={uploadedFileName}
        projectId={mongoIdOfInsightsForComplinces}
      />

      <SaveAsTemplateModal
        open={saveAsTemplateOpen}
        onClose={() => setSaveAsTemplateOpen(false)}
        sketchbookId={sketchbookId}
        currentTitle={editableTitle}
      />
    </>
  );
};

export default BreadcrumbsComponent;

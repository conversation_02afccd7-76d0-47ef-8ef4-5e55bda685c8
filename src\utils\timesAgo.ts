export function parseToDate(timestamp?: string | number): Date {
  if (!timestamp) return new Date();

  if (typeof timestamp === 'number') {
    return timestamp < 1e12 ? new Date(timestamp * 1000) : new Date(timestamp);
  }

  if (typeof timestamp === 'string') {
    // Patch: if there's no Z or timezone, assume it's UTC
    const isoFixed = timestamp.match(/Z|[\+\-]\d{2}:\d{2}/)
      ? timestamp
      : timestamp + 'Z';

    const parsed = new Date(isoFixed);
    if (isNaN(parsed.getTime())) throw new Error(`Invalid date: ${timestamp}`);
    return parsed;
  }

  throw new Error('Unsupported timestamp format');
}

// 2. Shows "x minutes ago", "x days ago", etc.
// export function getTimeAgo(timestamp: string | number): string {
//   try {
//     const date = parseToDate(timestamp);
//     const diff = Math.floor((Date.now() - date.getTime()) / 1000); // in seconds

//     if (diff < 60) return `${diff} seconds ago`;
//     if (diff < 3600) return `${Math.floor(diff / 60)} minutes ago`;
//     if (diff < 86400) return `${Math.floor(diff / 3600)} hours ago`;
//     if (diff < 2592000) return `${Math.floor(diff / 86400)} days ago`;
//     if (diff < 31536000) return `${Math.floor(diff / 2592000)} months ago`;
//     return `${Math.floor(diff / 31536000)} years ago`;
//   } catch (err) {
//     console.error('Error parsing time ago:', err);
//     return 'some time ago';
//   }
// }

export function getTimeAgo(timestamp: string | number): string {
  let date: number | string;

  // Handle ISO date string
  if (typeof timestamp === 'string' && timestamp.includes('T')) {
    date = Math.floor(new Date(timestamp).getTime() / 1000);
  } else {
    date = timestamp;
  }

  const now = Math.floor(Date.now() / 1000); // Current time in seconds
  const diff = now - date;

  if (diff < 60) return `${diff} seconds ago`;
  if (diff < 3600) return `${Math.floor(diff / 60)} minutes ago`;
  if (diff < 86400) return `${Math.floor(diff / 3600)} hours ago`;
  if (diff < 2592000) return `${Math.floor(diff / 86400)} days ago`;
  if (diff < 31536000) return `${Math.floor(diff / 2592000)} months ago`;
  return `${Math.floor(diff / 31536000)} years ago`;
}
// 1. Formats to 'MMM DD, hh:mm AM/PM' in user's local time
export function formatTimestamp(timestamp?: string | number): string {
  try {
    const date = parseToDate(timestamp);

    return new Intl.DateTimeFormat(undefined, {
      month: 'short',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      hour12: true,
    }).format(date);
  } catch (error) {
    console.error('Error formatting timestamp:', error);
    return new Date().toLocaleString();
  }
}

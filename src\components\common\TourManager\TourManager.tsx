import React, { useState } from 'react';
import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  IconButton,
  Tooltip,
  Typography,
  Divider,
  Badge,
} from '@mui/material';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import RestartAltIcon from '@mui/icons-material/RestartAlt';
import CloseIcon from '@mui/icons-material/Close';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import SchoolIcon from '@mui/icons-material/School';
import LiveHelpIcon from '@mui/icons-material/LiveHelp';
import YouTubeIcon from '@mui/icons-material/YouTube';
import { useTour } from '../../../contexts/TourContext';
import VideoTourDialog from '../VideoTourDialog/VideoTourDialog';
import styles from './TourManager.module.css';
import toast from 'react-hot-toast';
import { useLocation, useNavigate } from 'react-router-dom';

const TourManager: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const [open, setOpen] = useState(false);
  const [videoTourOpen, setVideoTourOpen] = useState(false);
  const [selectedVideoTour, setSelectedVideoTour] = useState<{
    videoId: string;
    title: string;
    description?: string;
  } | null>(null);

  const {
    availableTours,
    startTour,
    resetTours,
    hasSeenTour,
    isVideoTour,
    getVideoTourData,
    getAvailableToursForRoute,
    isTourValidForRoute,
  } = useTour();

  // Get current route
  const currentRoute = location.pathname;

  // Get tours available for current route
  const availableToursForRoute = getAvailableToursForRoute(currentRoute);

  const handleOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  const handleStartTour = (tourId: string) => {
    // Check if tour is valid for current route
    if (!isTourValidForRoute(tourId, currentRoute)) {
      const tour = availableTours.find((t) => t.id === tourId);
      if (tour?.requiresNavigation && tour.routes && tour.routes.length > 0) {
        // Show navigation option for tours that require specific routes
        toast(
          (t) => (
            <div className={styles.customtoast}>
              <div className={styles.customtoastmessage}>
                🧭 <strong>Navigate to start tour?</strong>
                <div className={styles.customtoastsubtext}>
                  This tour is designed for the {tour.routes?.[0]} page. Would
                  you like to navigate there?
                </div>
              </div>
              <div style={{ display: 'flex', gap: '8px', marginTop: '8px' }}>
                <button
                  onClick={() => {
                    navigate(tour.routes![0]);
                    toast.dismiss(t.id);
                    // Start tour after navigation with a delay
                    setTimeout(() => {
                      if (isVideoTour(tourId)) {
                        const videoData = getVideoTourData(tourId);
                        if (videoData) {
                          setSelectedVideoTour(videoData);
                          setVideoTourOpen(true);
                          startTour(tourId, tour.routes![0]);
                        }
                      } else {
                        startTour(tourId, tour.routes![0]);
                      }
                    }, 1000);
                  }}
                  className={styles.customtoastbutton}
                >
                  Navigate & Start
                </button>
                <button
                  onClick={() => toast.dismiss(t.id)}
                  className={styles.customtoastbutton}
                  style={{ backgroundColor: '#6b7280' }}
                >
                  Cancel
                </button>
              </div>
            </div>
          ),
          { duration: 8000 }
        );
        return;
      } else {
        toast.error(`This tour is not available on the current page.`);
        return;
      }
    }

    // Check if this is a video tour
    if (isVideoTour(tourId)) {
      const videoData = getVideoTourData(tourId);
      if (videoData) {
        setSelectedVideoTour(videoData);
        setVideoTourOpen(true);
        startTour(tourId, currentRoute); // Pass current route for validation
      }
    } else {
      // Regular tour
      startTour(tourId, currentRoute); // Pass current route for validation
    }
    handleClose();
  };

  const handleCloseVideoTour = () => {
    setVideoTourOpen(false);
    setSelectedVideoTour(null);
  };

  const handleResetTours = () => {
    toast((t) => (
      <div className={styles.customtoast}>
        <div className={styles.customtoastmessage}>
          ⚠️ <strong>Reset tour history?</strong>
          <div className={styles.customtoastsubtext}>
            This will let you see all the tours again.
          </div>
        </div>
        <button
          onClick={() => {
            resetTours();
            toast.dismiss(t.id);
          }}
          className={styles.customtoastbutton}
        >
          Reset
        </button>
      </div>
    ));
  };

  // Count unseen tours for current route
  const unseenTourCount = availableToursForRoute.filter(
    (tour) => !hasSeenTour(tour.id)
  ).length;

  return (
    <>
      <Tooltip title="Help & Interactive Tours" arrow placement="bottom">
        <IconButton
          color="primary"
          onClick={handleOpen}
          className={styles.helpButton}
          size="small"
        >
          <Badge
            badgeContent={unseenTourCount}
            color="error"
            overlap="circular"
          >
            <LiveHelpIcon />
          </Badge>
        </IconButton>
      </Tooltip>

      {/* Video Tour Dialog */}
      {selectedVideoTour && (
        <VideoTourDialog
          open={videoTourOpen}
          onClose={handleCloseVideoTour}
          videoId={selectedVideoTour.videoId}
          title={selectedVideoTour.title}
          description={selectedVideoTour.description}
        />
      )}

      <Dialog
        open={open}
        onClose={handleClose}
        maxWidth="sm"
        fullWidth
        className={styles.dialog}
        // PaperProps={{
        //   className: styles.dialogPaper,
        // }}
      >
        <DialogTitle className={styles.dialogTitle}>
          <div className={styles.titleContent}>
            <SchoolIcon className={styles.titleIcon} />
            <Typography variant="h6">Interactive Tours</Typography>
          </div>
          <IconButton
            aria-label="close"
            onClick={handleClose}
            className={styles.closeButton}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent dividers className={styles.dialogContent}>
          <Typography variant="body1" className={styles.dialogDescription}>
            Take a guided tour to learn how to use different features of the
            application. Each tour will highlight key elements and provide
            helpful instructions.
          </Typography>
          {availableToursForRoute.length === 0 && (
            <Typography
              variant="body2"
              color="text.secondary"
              style={{ textAlign: 'center', padding: '20px' }}
            >
              No tours are available for the current page. Navigate to different
              sections to see relevant tours.
            </Typography>
          )}
          <List className={styles.tourList}>
            {availableToursForRoute.map((tour) => (
              <ListItem
                key={tour.id}
                disablePadding
                className={styles.tourListItem}
              >
                <ListItemButton
                  onClick={() => handleStartTour(tour.id)}
                  className={`${styles.tourItem} ${hasSeenTour(tour.id) ? styles.seenTour : styles.unseenTour}`}
                >
                  <ListItemIcon className={styles.tourIcon}>
                    {isVideoTour(tour.id) ? (
                      <YouTubeIcon color="error" />
                    ) : (
                      <PlayArrowIcon color="primary" />
                    )}
                  </ListItemIcon>
                  <ListItemText
                    primary={tour.name}
                    secondary={
                      isVideoTour(tour.id)
                        ? 'Video tutorial'
                        : `${tour.steps.length} steps • Interactive guide`
                    }
                    primaryTypographyProps={{ className: styles.tourTitle }}
                    secondaryTypographyProps={{
                      className: styles.tourSubtitle,
                    }}
                  />
                  {hasSeenTour(tour.id) ? (
                    <Tooltip title="You've completed this tour" arrow>
                      <CheckCircleIcon
                        color="success"
                        className={styles.completedIcon}
                      />
                    </Tooltip>
                  ) : (
                    <span className={styles.newBadge}>NEW</span>
                  )}
                </ListItemButton>
              </ListItem>
            ))}
          </List>
        </DialogContent>
        <Divider />
        <DialogActions className={styles.dialogActions}>
          <Button
            startIcon={<RestartAltIcon />}
            onClick={handleResetTours}
            color="secondary"
            variant="outlined"
            className={styles.resetButton}
          >
            Reset Tour History
          </Button>
          <Button
            onClick={handleClose}
            color="primary"
            variant="contained"
            className={styles.closeDialogButton}
          >
            Close
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default TourManager;

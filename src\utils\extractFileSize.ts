import { PDFDocument } from 'pdf-lib';

export async function checkFileInBrowser(file: File) {
  const user = JSON.parse(localStorage.getItem('user') || '{}');
  const tier = user?.tier || 'free';

  const fileSizeMB = file.size / (1024 * 1024);
  const ext = file.name.split('.').pop()?.toLowerCase();
  let pages: number | undefined;

  try {
    if (ext === 'pdf') {
      const buffer = await file.arrayBuffer();
      const pdfDoc = await PDFDocument.load(buffer);
      pages = pdfDoc.getPageCount();
    }

    const maxSizeMB = 0.048828125;
    const isAllowed = tier !== 'free' || fileSizeMB <= maxSizeMB;

    return {
      fileSizeMB: parseFloat(fileSizeMB.toFixed(3)),
      pages,
      isAllowed,
      message: isAllowed
        ? 'File is allowed.'
        : `Free tier users can only upload files under ${maxSizeMB * 1024} KB.`,
    };
  } catch (err: any) {
    return {
      fileSizeMB,
      isAllowed: false,
      message: `Error reading file: ${err.message}`,
    };
  }
}

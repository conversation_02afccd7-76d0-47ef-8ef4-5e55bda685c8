import React, { useEffect, useState } from 'react';
import { 
  diagnoseSpeechRecognition, 
  SpeechRecognitionDiagnostics as DiagnosticsType 
} from '../../utils/speechRecognitionUtils';

interface SpeechRecognitionDiagnosticsProps {
  onClose?: () => void;
}

const SpeechRecognitionDiagnostics: React.FC<SpeechRecognitionDiagnosticsProps> = ({ 
  onClose 
}) => {
  const [diagnostics, setDiagnostics] = useState<DiagnosticsType | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const runDiagnostics = async () => {
      try {
        const result = await diagnoseSpeechRecognition();
        setDiagnostics(result);
      } catch (error) {
        console.error('Error running speech recognition diagnostics:', error);
      } finally {
        setLoading(false);
      }
    };

    runDiagnostics();
  }, []);

  if (loading) {
    return (
      <div style={{ padding: '20px', textAlign: 'center' }}>
        <div>Running diagnostics...</div>
      </div>
    );
  }

  if (!diagnostics) {
    return (
      <div style={{ padding: '20px', textAlign: 'center', color: '#d32f2f' }}>
        <div>Failed to run diagnostics</div>
        {onClose && (
          <button onClick={onClose} style={{ marginTop: '10px' }}>
            Close
          </button>
        )}
      </div>
    );
  }

  const getStatusColor = (status: boolean | null) => {
    if (status === true) return '#4caf50';
    if (status === false) return '#f44336';
    return '#ff9800';
  };

  const getStatusIcon = (status: boolean | null) => {
    if (status === true) return '✅';
    if (status === false) return '❌';
    return '⚠️';
  };

  return (
    <div style={{ 
      padding: '20px', 
      backgroundColor: '#f5f5f5', 
      borderRadius: '8px',
      maxWidth: '500px',
      margin: '0 auto'
    }}>
      <h3 style={{ marginTop: 0, marginBottom: '20px' }}>
        🎤 Speech Recognition Diagnostics
      </h3>

      <div style={{ marginBottom: '20px' }}>
        <div style={{ marginBottom: '10px' }}>
          <strong>Browser:</strong> {diagnostics.browserName}
        </div>
        
        <div style={{ marginBottom: '10px' }}>
          <span style={{ color: getStatusColor(diagnostics.isSupported) }}>
            {getStatusIcon(diagnostics.isSupported)}
          </span>
          <strong> API Support:</strong> {diagnostics.isSupported ? 'Yes' : 'No'}
        </div>
        
        <div style={{ marginBottom: '10px' }}>
          <span style={{ color: getStatusColor(diagnostics.isSecureContext) }}>
            {getStatusIcon(diagnostics.isSecureContext)}
          </span>
          <strong> Secure Context:</strong> {diagnostics.isSecureContext ? 'Yes (HTTPS)' : 'No (HTTP)'}
        </div>
        
        <div style={{ marginBottom: '10px' }}>
          <span style={{ color: getStatusColor(diagnostics.hasPermission) }}>
            {getStatusIcon(diagnostics.hasPermission)}
          </span>
          <strong> Microphone Permission:</strong> {
            diagnostics.hasPermission === true ? 'Granted' :
            diagnostics.hasPermission === false ? 'Denied' : 'Unknown'
          }
        </div>
      </div>

      {diagnostics.issues.length > 0 && (
        <div style={{ marginBottom: '20px' }}>
          <h4 style={{ color: '#f44336', marginBottom: '10px' }}>Issues Found:</h4>
          <ul style={{ margin: 0, paddingLeft: '20px' }}>
            {diagnostics.issues.map((issue, index) => (
              <li key={index} style={{ marginBottom: '5px', color: '#f44336' }}>
                {issue}
              </li>
            ))}
          </ul>
        </div>
      )}

      {diagnostics.recommendations.length > 0 && (
        <div style={{ marginBottom: '20px' }}>
          <h4 style={{ color: '#2196f3', marginBottom: '10px' }}>Recommendations:</h4>
          <ul style={{ margin: 0, paddingLeft: '20px' }}>
            {diagnostics.recommendations.map((recommendation, index) => (
              <li key={index} style={{ marginBottom: '5px', color: '#2196f3' }}>
                {recommendation}
              </li>
            ))}
          </ul>
        </div>
      )}

      <div style={{ 
        padding: '15px', 
        backgroundColor: '#e3f2fd', 
        borderRadius: '4px',
        marginBottom: '20px'
      }}>
        <h4 style={{ margin: '0 0 10px 0', color: '#1976d2' }}>
          Quick Fixes:
        </h4>
        <ul style={{ margin: 0, paddingLeft: '20px', color: '#1976d2' }}>
          <li>Make sure you're using HTTPS (not HTTP)</li>
          <li>Use Chrome, Edge, or Safari browser</li>
          <li>Allow microphone access when prompted</li>
          <li>Check that your microphone is working</li>
          <li>Try refreshing the page</li>
        </ul>
      </div>

      {onClose && (
        <div style={{ textAlign: 'center' }}>
          <button 
            onClick={onClose}
            style={{
              padding: '10px 20px',
              backgroundColor: '#2196f3',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            Close
          </button>
        </div>
      )}
    </div>
  );
};

export default SpeechRecognitionDiagnostics;

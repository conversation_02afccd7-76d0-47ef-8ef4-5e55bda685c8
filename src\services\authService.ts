import { createApi } from '@reduxjs/toolkit/query/react';
import { baseQuery } from './baseQuery';
import {
  SIGNUP,
  SIGNIN,
  FORGOT_PASSWORD,
  RESET_PASSWORD,
} from './constants/authServiceConstants';

export const authApi = createApi({
  reducerPath: 'authApi',
  baseQuery: baseQuery,
  tagTypes: ['Auth'],
  endpoints: (builder) => ({
    signup: builder.mutation({
      query: (credentials) => ({
        url: SIGNUP,
        method: 'POST',
        body: credentials,
      }),
      invalidatesTags: ['Auth'],
    }),
    signin: builder.mutation({
      query: (credentials) => ({
        url: SIGNIN,
        method: 'POST',
        body: credentials,
      }),
      invalidatesTags: ['Auth'],
    }),
    forgotPassword: builder.mutation({
      query: (data) => ({
        url: `${FORGOT_PASSWORD}?frontendUrl=${encodeURIComponent('https://neuquipfrontend.cogweel.com')}`,
        method: 'POST',
        body: { email: data.email },
      }),
    }),

    resetPassword: builder.mutation({
      query: (data) => ({
        url: `${RESET_PASSWORD}?token=${data.token}`,
        method: 'POST',
        body: {
          token: data.token,
          newPassword: data.newPassword,
        },
      }),
    }),
  }),
});

export const {
  useSignupMutation,
  useSigninMutation,
  useForgotPasswordMutation,
  useResetPasswordMutation,
} = authApi;

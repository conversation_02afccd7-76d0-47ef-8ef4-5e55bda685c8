import style from './ChatInput.module.css';
import { MdAttachFile } from 'react-icons/md';
import { useEffect, useRef, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { IoSend } from 'react-icons/io5';
import { useTheme } from '../../../hooks/useTheme';
import IconButton from '../../common/button/IconButton';
import VoiceInput from '../../chat/VoiceInput';
import { useSpeechRecognition } from 'react-speech-recognition';
import toast from 'react-hot-toast';

interface ChatInputProps {
  type: 'inputWithFileUpload' | 'inputWithOutFileUpload';
  placeholder?: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onSend: () => void;
  onFileUpload: (file: File) => void;
  autofocus?: boolean;
  disabled?: boolean;
}

const acceptedFileTypes = {
  pdf: {
    mimeTypes: ['application/pdf'],
    extensions: ['.pdf'],
  },
  word: {
    mimeTypes: [
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    ],
    extensions: ['.doc', '.docx'],
  },
  excel: {
    mimeTypes: [
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    ],
    extensions: ['.xls', '.xlsx'],
  },
  csv: {
    mimeTypes: ['text/csv'],
    extensions: ['.csv'],
  },
  text: {
    mimeTypes: ['text/plain'],
    extensions: ['.txt'],
  },
};

const getAcceptFileTypes = () =>
  Object.values(acceptedFileTypes)
    .flatMap((type) => type.mimeTypes)
    .join(',');

const ChatInput: React.FC<ChatInputProps> = ({
  type = 'inputWithFileUpload',
  placeholder,
  value,
  onChange,
  onSend,
  onFileUpload,
  autofocus = false,
  disabled = false,
}) => {
  const theme = useTheme();
  const fileInputRef = useRef<HTMLInputElement>(null);

  const {
    transcript,
    listening,
    resetTranscript,
    browserSupportsSpeechRecognition,
  } = useSpeechRecognition();
  const [isListening, setIsListening] = useState(false);
  const [typedValue, setTypedValue] = useState('');

  // Sync external value prop with internal typedValue
  useEffect(() => {
    setTypedValue(value);
  }, [value]);

  // When mic stops, merge voice text
  useEffect(() => {
    if (!isListening && transcript) {
      const newValue = typedValue + transcript;
      setTypedValue(newValue);
      resetTranscript();
      // Propagate merged value to parent
      const fakeEvent = {
        target: { value: newValue },
      } as unknown as React.ChangeEvent<HTMLInputElement>;
      onChange(fakeEvent);
    }
  }, [isListening, transcript]);

  // Handle speech recognition errors
  useEffect(() => {
    if (!browserSupportsSpeechRecognition) {
      console.warn('Browser does not support speech recognition');
    }
  }, [browserSupportsSpeechRecognition]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    accept: {
      'application/pdf': ['.pdf'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
        ['.docx'],
      'application/msword': ['.doc'],
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': [
        '.xlsx',
      ],
      'text/csv': ['.csv'],
      'text/plain': ['.txt'],
    },
    noClick: true,
    onDrop: (acceptedFiles) => {
      if (acceptedFiles?.[0]) {
        onFileUpload(acceptedFiles[0]);
      }
    },
  });

  const handleUploadClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      onFileUpload(file);
      // Reset input value to allow selecting the same file again
      event.target.value = '';
    }
  };

  const handleSendClick = () => {
    if (value.trim() !== '') {
      onSend();
    }
  };

  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      if (value.trim() !== '') {
        onSend();
      }
    }
  };

  const MAX_LENGTH = 3000;

  return (
    <div
      id="chatInput"
      className={`${style.chatInputContainer} ${disabled ? style.disabled : ''} ${isDragActive ? style.dragActive : ''}`}
      {...getRootProps()}
      style={
        {
          '--color-primary': 'var(--color-primary)',
          '--color-primary-light': 'var(--color-primary-light)',
          '--color-primary-rgb': 'var(--color-primary-rgb)',
          '--color-text-primary': 'var(--color-text-primary)',
          '--color-text-secondary': 'var(--color-text-secondary)',
          '--color-error-main': 'var(--color-error-main)',
        } as React.CSSProperties
      }
    >
      {type === 'inputWithFileUpload' && (
        <div onClick={handleUploadClick} className={style.attachment}>
          <MdAttachFile size={22} />
        </div>
      )}
      <input
        {...getInputProps({
          ref: fileInputRef,
          accept: getAcceptFileTypes(),
          style: { display: 'none' },
          disabled: disabled,
          onChange: handleFileChange,
        })}
      />
      {/* <input
        className={style.inputStyles}
        autoFocus={autofocus}
        autoCorrect="on"
        autoComplete="on"
        type="text"
        placeholder={
          isDragActive
            ? 'Drop your file here...'
            : isListening
            ? 'Listening...'
            : placeholder
        }
        value={isListening ? transcript : value}
        onChange={onChange}
        onKeyDown={handleKeyDown}
        disabled={disabled}
        style={{
          fontFamily: theme.typography.fontFamily.primary,
        }}
      /> */}
      <textarea
        maxLength={MAX_LENGTH}
        className={style.inputStyles}
        autoFocus={autofocus}
        autoCorrect="on"
        autoComplete="on"
        placeholder={
          isDragActive
            ? 'Drop your file here...'
            : isListening
              ? 'Listening...'
              : placeholder
        }
        // value={isListening ? transcript : value}
        value={typedValue + (isListening ? transcript : '')}
        onChange={(e) => {
          const value = e.target.value;

          if (value.length === MAX_LENGTH) {
            toast.error(
              `Your input is too long. Max allowed is ${MAX_LENGTH} characters.`
            );
          }

          setTypedValue(value);
          onChange(e as unknown as React.ChangeEvent<HTMLInputElement>);
        }}
        onKeyDown={(e) =>
          handleKeyDown(e as unknown as React.KeyboardEvent<HTMLInputElement>)
        }
        disabled={disabled}
        style={{
          fontFamily: theme.typography.fontFamily.primary,
        }}
      />

      {browserSupportsSpeechRecognition && (
        <VoiceInput isListening={isListening} setListening={setIsListening} />
      )}
      {/* <div className={style.sendIconContainer} onClick={handleSendClick}> */}
      <IconButton
        // size="small"
        icon={<IoSend size={18} />}
        onClick={handleSendClick}
      />
      {/* </div> */}
    </div>
  );
};

export default ChatInput;

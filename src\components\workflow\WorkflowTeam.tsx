import { useState } from 'react';
import styles from './WorkflowTeam.module.css';
import CustomButton from '../common/button/CustomButton';
import IconButton from '../common/button/IconButton';
import { IoClose, IoTrash } from 'react-icons/io5';
import { FaTrash } from 'react-icons/fa';
import userimage from '../../assets/images/profile.png';
import { WorkflowPerson, WorkflowUser } from '../../types/workflow/index';

interface WorkflowTeamProps {
  people: WorkflowPerson[];
  onAddPerson: (person: WorkflowPerson) => void;
  onUpdatePerson: (id: string | number, note: string) => void;
  onDeletePerson: (id: string | number) => void;
  users: WorkflowUser[];
  validationError?: boolean;
}

const WorkflowTeam: React.FC<WorkflowTeamProps> = ({
  people,
  onAddPerson,
  onUpdatePerson,
  onDeletePerson,
  users,
  validationError,
}) => {
  const [isSearching, setIsSearching] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  const filteredUsers = users.filter((user) =>
    user.name.toLowerCase().includes((searchQuery || '').toLowerCase())
  );

  const handleSelectUser = (user: WorkflowUser) => {
    onAddPerson({
      id: user.id,
      name: user.name,
      note: '',
    });
    setIsSearching(false);
    setSearchQuery('');
  };

  return (
    <div className={styles.peopleSection}>
      <div className={styles.sectionHeader}>
        <div className={styles.participantsHeader}>
          <h3>
            Team Members <span className={styles.required}>*</span>
          </h3>
          {validationError && (
            <span className={styles.errorText}>
              At least one participant is required
            </span>
          )}
        </div>
        <CustomButton
          type="secondary"
          label="Add Person"
          onClick={() => setIsSearching(true)}
          style={{ width: '15%' }}
        />
      </div>

      {isSearching && (
        <div className={styles.searchContainer}>
          <div className={styles.searchInput}>
            <input
              type="text"
              placeholder="Search users..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className={styles.standardInput}
            />
            <IconButton
              type="secondary"
              icon={<IoClose size={18} />}
              onClick={() => setIsSearching(false)}
              size="medium"
              title="Close search"
            />
          </div>
          {searchQuery && (
            <div className={styles.searchResults}>
              {filteredUsers.map((user) => (
                <div
                  key={user.id}
                  className={styles.searchResult}
                  onClick={() => handleSelectUser(user)}
                >
                  <img src={userimage} alt="" className={styles.userImg} />
                  <span>{user.name}</span>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      <div className={styles.teamTable}>
        <table>
          <thead>
            <tr>
              <th>People</th>
              <th>Note</th>
              <th>Action</th>
            </tr>
          </thead>
          <tbody>
            {people.map((person) => (
              <tr key={person.id}>
                <td className={styles.userCell}>
                  <img src={userimage} alt="" className={styles.userImg} />
                  <span>{person.name}</span>
                </td>
                <td>
                  <input
                    type="text"
                    placeholder="Add note..."
                    value={person.note}
                    onChange={(e) => onUpdatePerson(person.id, e.target.value)}
                    className={styles.standardInput}
                  />
                </td>
                <td>
                  <IconButton
                    type="danger"
                    icon={<IoTrash size={16} />}
                    onClick={() => onDeletePerson(person.id)}
                    size="small"
                    title="Delete team member"
                  />
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      {people.length === 0 && (
        <p className={styles.helperText}>
          Please add at least one team member to create a workflow
        </p>
      )}
    </div>
  );
};

export default WorkflowTeam;

import React, { useEffect, useRef, useState } from 'react';
import {
  Dialog,
  DialogContent,
  Typo<PERSON>,
  IconButton,
  CircularProgress,
  Box,
  Menu,
  MenuItem,
  Paper,
  DialogActions,
  Button,
  Tooltip,
  Switch,
  FormControlLabel,
} from '@mui/material';
import {
  Close as CloseIcon,
  Print as PrintIcon,
  FileDownload as DownloadIcon,
  Share as ShareIcon,
  MoreVert as MoreVertIcon,
  ZoomIn as ZoomInIcon,
  ZoomOut as ZoomOutIcon,
  ZoomOutMap as ZoomResetIcon,
} from '@mui/icons-material';
import html2canvas from 'html2canvas';
// jspdf is imported dynamically when needed
import PreviewChart from './PreviewChart';
import styles from '../EditorPreview.module.css';
import MarkdownRenderer from '../../../common/chips/Markdown';
import '../../../specific/sketchBookControler/DropableEditor.css';
import { toast } from 'react-hot-toast';
import { useGetComplincesAsMakedownQuery } from '../../../../services';
import { ShareDialog } from '../../../common/ShareDialog/ShareDialog';
import {
  useSendEmailWithAttachmentMutation,
  useSaveFileForEmailMutation,
} from '../../../../services/sketchbookServices';

interface EditorPreviewProps {
  open: boolean;
  onClose: () => void;
  pages: any[];
  charts: Record<string, any[]>;
  layouts: Record<string, any[]>;
  pageSize: {
    value: string;
    label: string;
    width: number;
    height: number;
    orientation: 'portrait' | 'landscape';
  };
  isExporting: boolean;
  pendingExport: 'PDF' | 'DOC' | null;
  // onPrint is no longer needed as we handle printing internally
  onPrint: () => void; // kept for backward compatibility
  onExport: (type: 'email' | 'saveToDisk', pdfData?: Uint8Array) => void;
  projectTitle?: string;
  projectId: string;
  currentUser: any;
}

/**
 * EditorPreview - A new preview component that directly mirrors the editor content
 *
 * This component renders pages and charts exactly as they appear in the DropableEditor without
 * any transformations or repositioning that could cause discrepancies between the editor view
 * and the preview/print output.
 *
 * Key features:
 * - Uses CSS Grid for exact positioning matching the editor grid
 * - Maintains z-index and stacking order
 * - Preserves chart dimensions and aspect ratios
 * - Supports dynamic page heights
 * - Allows for zoom controls without affecting the print/export output
 * - Direct integration with the actual chart components used in the editor
 */
const EditorPreview: React.FC<EditorPreviewProps> = ({
  open,
  onClose,
  pages,
  charts,
  layouts,
  pageSize,
  isExporting,
  pendingExport,
  // onPrint is kept for fallback but not used directly
  onPrint: _onPrint,
  onExport,
  projectTitle,
  projectId,
  currentUser,
}) => {
  const { data: previousReportsAsMarkdown } = useGetComplincesAsMakedownQuery(
    {
      userId: currentUser?.id,
      projectId,
    },
    {
      skip: !projectId,
    }
  );

  const [zoom, setZoom] = useState(1);
  const [menuAnchorEl, setMenuAnchorEl] = useState<null | HTMLElement>(null);
  const previewContainerRef = useRef<HTMLDivElement>(null);
  const printableContentRef = useRef<HTMLDivElement>(null);
  const [isExportingState, setIsExporting] = useState(isExporting);
  const [pendingExportState, setPendingExport] = useState<null | 'PDF' | 'DOC'>(
    pendingExport
  );
  const [showComplianceReports, setShowComplianceReports] = useState(false);
  const [shareDialogOpen, setShareDialogOpen] = useState(false);
  const [uploadedFileName, setUploadedFileName] = useState('');
  const [generatedPdfBytes, setGeneratedPdfBytes] =
    useState<ArrayBuffer | null>(null);
  // This is needed for the share dialog functionality
  const [previewOpen, setPreviewOpen] = useState(open);

  const menuOpen = Boolean(menuAnchorEl);

  // Reset zoom and scroll position when dialog opens
  useEffect(() => {
    if (open) {
      setZoom(1);
      if (previewContainerRef.current) {
        previewContainerRef.current.scrollTop = 0;
      }
    }
    // Sync previewOpen with open prop
    setPreviewOpen(open);
  }, [open]);

  const handleMenuClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setMenuAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setMenuAnchorEl(null);
  };

  const handleZoomIn = () => {
    setZoom((prev) => Math.min(prev + 0.1, 2));
  };

  const handleZoomOut = () => {
    setZoom((prev) => Math.max(prev - 0.1, 0.5));
  };

  const handleResetZoom = () => {
    setZoom(1);
  };

  // Extract PDF generation logic into a reusable function
  const generatePDFForSharing = async (): Promise<ArrayBuffer> => {
    // Get the preview container that shows the actual content
    const previewContent = document.querySelector(
      `.${styles.zoomContainer}`
    ) as HTMLElement;
    if (!previewContent) {
      throw new Error('Preview content not found');
    }

    // Store original styles to restore later
    const originalTransform = previewContent.style.transform;
    const originalTransformOrigin = previewContent.style.transformOrigin;

    try {
      // Reset transform to ensure proper rendering
      previewContent.style.transform = 'none';
      previewContent.style.transformOrigin = 'top left';

      // Import jsPDF directly in our code instead of relying on the window object
      const jsPDFModule = await import('jspdf');
      // Handle both ESM and CommonJS module formats
      const jsPDF = jsPDFModule.default || jsPDFModule;

      const doc = new jsPDF({
        orientation: pageSize.orientation,
        unit: 'pt', // Use points for better precision
        format: 'a4',
        compress: true,
        precision: 16,
        hotfixes: ['px_scaling'], // Fix scaling issues
      });

      // Get all page elements
      const pageElements = document.querySelectorAll(`.${styles.pageWrapper}`);

      // Get compliance report page if toggle is enabled
      const compliancePageElement =
        showComplianceReports && previousReportsAsMarkdown?.markdown_content
          ? document.querySelector(`.${styles.compliancePage}`)
          : null;

      const totalPages = pageElements.length + (compliancePageElement ? 1 : 0);

      // Process each page
      for (let i = 0; i < pageElements.length; i++) {
        // Add a new page for each page after the first
        if (i > 0) doc.addPage();

        const pageElement = pageElements[i] as HTMLElement;
        const pageContent = pageElement.querySelector(
          `.${styles.page}`
        ) as HTMLElement;

        if (!pageContent) continue;

        // Capture the page content
        const canvas = await html2canvas(pageContent, {
          scale: 3, // Higher scale for better quality
          useCORS: true,
          allowTaint: true,
          backgroundColor: '#ffffff',
          logging: false,
          width: pageSize.width,
          height: pageContent.offsetHeight,
          windowWidth: pageSize.width,
          windowHeight: pageContent.offsetHeight,
          onclone: (_clonedDoc: Document, clonedElement: HTMLElement) => {
            // Ensure all chart canvases render properly
            const chartContainers = clonedElement.querySelectorAll(
              `.${styles.chartContainer}`
            );
            chartContainers.forEach((container: Element) => {
              const chartContent = container.querySelector<HTMLElement>(
                `.${styles.chartContent}`
              );
              if (chartContent) {
                chartContent.style.overflow = 'visible';
              }

              // Make sure chart canvases render properly
              const canvases = container.querySelectorAll('canvas');
              canvases.forEach((canvas: HTMLCanvasElement) => {
                canvas.style.width = '100%';
                canvas.style.height = '100%';
              });
            });

            return clonedElement;
          },
        });

        // Calculate dimensions to fit the page content to A4
        const imgData = canvas.toDataURL('image/png', 1.0);

        // Get the actual dimensions of the page content
        const contentWidth = pageSize.width;
        const contentHeight = pageContent.offsetHeight;

        // Calculate the aspect ratio of the content
        const contentAspect = contentWidth / contentHeight;

        // Calculate the dimensions for the PDF page (in points)
        // A4 in points: 595 x 842 (portrait) or 842 x 595 (landscape)
        const pdfWidth = pageSize.orientation === 'landscape' ? 842 : 595;
        const pdfHeight = pageSize.orientation === 'landscape' ? 595 : 842;

        // Calculate the maximum dimensions that will fit on the page with margins
        const margin = 20; // 20 point margin
        const maxWidth = pdfWidth - margin * 2;
        const maxHeight = pdfHeight - margin * 2;

        // Calculate the dimensions to fit the content proportionally
        let imgWidth, imgHeight;

        if (contentAspect > maxWidth / maxHeight) {
          // Content is wider than the page proportion
          imgWidth = maxWidth;
          imgHeight = imgWidth / contentAspect;
        } else {
          // Content is taller than the page proportion
          imgHeight = maxHeight;
          imgWidth = imgHeight * contentAspect;
        }

        // Center the image on the page
        const xOffset = margin + (maxWidth - imgWidth) / 2;
        const yOffset = margin + (maxHeight - imgHeight) / 2;

        // Add the image to the PDF with proper dimensions
        doc.addImage(
          imgData,
          'PNG',
          xOffset,
          yOffset,
          imgWidth,
          imgHeight,
          undefined,
          'FAST'
        );
      }

      // Process compliance report page if toggle is enabled
      if (compliancePageElement) {
        // Add a new page for the compliance report
        doc.addPage();

        const complianceContent = compliancePageElement.querySelector(
          `.${styles.page}`
        ) as HTMLElement;

        if (complianceContent) {
          // Capture the compliance page content
          const canvas = await html2canvas(complianceContent, {
            scale: 3, // Higher scale for better quality
            useCORS: true,
            allowTaint: true,
            backgroundColor: '#ffffff',
            logging: false,
            width: pageSize.width,
            height: complianceContent.offsetHeight,
            windowWidth: pageSize.width,
            windowHeight: complianceContent.offsetHeight,
          });

          // Calculate dimensions to fit the page content to A4
          const imgData = canvas.toDataURL('image/png', 1.0);

          // Get the actual dimensions of the page content
          const contentWidth = pageSize.width;
          const contentHeight = complianceContent.offsetHeight;

          // Calculate the aspect ratio of the content
          const contentAspect = contentWidth / contentHeight;

          // Calculate the dimensions for the PDF page (in points)
          // A4 in points: 595 x 842 (portrait) or 842 x 595 (landscape)
          const pdfWidth = pageSize.orientation === 'landscape' ? 842 : 595;
          const pdfHeight = pageSize.orientation === 'landscape' ? 595 : 842;

          // Calculate the maximum dimensions that will fit on the page with margins
          const margin = 20; // 20 point margin
          const maxWidth = pdfWidth - margin * 2;
          const maxHeight = pdfHeight - margin * 2;

          // Calculate the dimensions to fit the content proportionally
          let imgWidth, imgHeight;

          if (contentAspect > maxWidth / maxHeight) {
            // Content is wider than the page proportion
            imgWidth = maxWidth;
            imgHeight = imgWidth / contentAspect;
          } else {
            // Content is taller than the page proportion
            imgHeight = maxHeight;
            imgWidth = imgHeight * contentAspect;
          }

          // Center the image on the page
          const xOffset = margin + (maxWidth - imgWidth) / 2;
          const yOffset = margin + (maxHeight - imgHeight) / 2;

          // Add the image to the PDF with proper dimensions
          doc.addImage(
            imgData,
            'PNG',
            xOffset,
            yOffset,
            imgWidth,
            imgHeight,
            undefined,
            'FAST'
          );
        }
      }

      // Generate the PDF blob and return as ArrayBuffer
      const pdfBlob = doc.output('blob');
      return await pdfBlob.arrayBuffer();
    } finally {
      // Restore original styles
      previewContent.style.transform = originalTransform;
      previewContent.style.transformOrigin = originalTransformOrigin;
    }
  };

  const handleOpenShareDialog = async () => {
    try {
      handleMenuClose();

      // Show loading toast
      toast.loading('Generating PDF for sharing...', { id: 'share-loading' });

      // Generate the actual PDF using the same logic as download
      const pdfArrayBuffer = await generatePDFForSharing();
      setGeneratedPdfBytes(pdfArrayBuffer);

      // Set the file name
      const fileName = `${projectTitle || 'NEUQUIP-project'}.pdf`;
      setUploadedFileName(fileName);

      // Dismiss loading toast
      toast.dismiss('share-loading');
      toast.success('PDF generated successfully!');

      // Open the share dialog
      setShareDialogOpen(true);
    } catch (error) {
      // Dismiss loading toast
      toast.dismiss('share-loading');

      console.error('Error preparing file for sharing:', error);
      toast.error('Failed to generate PDF for sharing');
    }
  };

  const handleCloseShareDialog = () => {
    setShareDialogOpen(false);
    setGeneratedPdfBytes(null); // Clear stored PDF bytes
  };

  // Initialize the API mutations
  const [sendEmailWithAttachment] = useSendEmailWithAttachmentMutation();
  const [saveFileForEmail] = useSaveFileForEmailMutation();

  const handleSendEmail = async (emailData: {
    sendToEmails: string[];
    ccToEmails: string[];
    subject: string;
    content: string;
    hasAttachment: boolean;
    fileNames: string[];
  }) => {
    try {
      // Show loading toast
      toast.loading('Preparing email with attachment...', {
        id: 'email-sending',
      });

      // First, upload the PDF file to the server if we have generated PDF bytes
      if (generatedPdfBytes && emailData.hasAttachment) {
        // Create file object for upload
        const file = new Blob([generatedPdfBytes as ArrayBuffer], {
          type: 'application/pdf',
        });
        const fileName = `${projectTitle || 'NEUQUIP-project'}.pdf`;

        // Prepare FormData for file upload
        const formData = new FormData();
        formData.append('file', file, fileName);

        // Upload the file to the server
        const uploadResponse = await saveFileForEmail({
          payload: formData,
        }).unwrap();

        if (!uploadResponse.success) {
          throw new Error('Failed to upload file to server');
        }

        // Update loading message
        toast.loading('Sending email...', { id: 'email-sending' });
      }

      // Call the API to send the email
      const response = await sendEmailWithAttachment({
        payload: emailData,
      }).unwrap();

      // Dismiss loading toast
      toast.dismiss('email-sending');

      if (response.success) {
        toast.success('Email sent successfully');
        setShareDialogOpen(false);
      } else {
        toast.error('Failed to send email');
      }
    } catch (error: any) {
      // Dismiss loading toast
      toast.dismiss('email-sending');

      console.error('Error sending email:', error);
      toast.error(error.data?.message || 'Failed to send email');
    }
  };

  const handleDirectDownload = async () => {
    try {
      // Show loading toast
      toast.loading('Generating PDF for download...', {
        id: 'download-loading',
      });

      // If we don't have generated PDF bytes, generate them
      let pdfArrayBuffer = generatedPdfBytes;
      if (!pdfArrayBuffer) {
        // Generate the actual PDF using the same logic as share
        pdfArrayBuffer = await generatePDFForSharing();
        setGeneratedPdfBytes(pdfArrayBuffer);
      }

      // Create file and trigger download
      const file = new Blob([pdfArrayBuffer as ArrayBuffer], {
        type: 'application/pdf',
      });
      const fileName = `${projectTitle || 'NEUQUIP-project'}.pdf`;

      // Dismiss loading toast
      toast.dismiss('download-loading');

      // Create a download link
      const url = URL.createObjectURL(file);
      const a = document.createElement('a');
      a.href = url;
      a.download = fileName;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      toast.success('File downloaded successfully');
    } catch (error) {
      // Dismiss loading toast
      toast.dismiss('download-loading');

      console.error('Error in handleDirectDownload:', error);
      toast.error('Failed to download file');
    }
  };

  // Enhanced print function that directly captures the preview content
  const handlePrint = async () => {
    if (isExportingState) return;

    // Show loading state
    setIsExporting(true);

    try {
      // Get the preview container that shows the actual content
      const previewContent = document.querySelector(
        `.${styles.zoomContainer}`
      ) as HTMLElement;
      if (!previewContent) {
        throw new Error('Preview content not found');
      }

      // Store original styles to restore later
      const originalTransform = previewContent.style.transform;
      const originalTransformOrigin = previewContent.style.transformOrigin;

      // Reset transform to ensure proper rendering
      previewContent.style.transform = 'none';
      previewContent.style.transformOrigin = 'top left';

      // Open a new window for printing
      const printWindow = window.open('', '_blank');
      if (!printWindow) {
        toast.error('Please allow pop-ups for printing');
        setIsExporting(false);
        return;
      }
      // Create a new MarkdownRenderer instance and render the content
      // A4 dimensions in pixels at 96 DPI
      const a4Width = pageSize.orientation === 'landscape' ? 1123 : 794; // ~297mm : ~210mm
      const a4Height = pageSize.orientation === 'landscape' ? 794 : 1123; // ~210mm : ~297mm

      // Initialize print window with basic structure
      const printContent = `
        <!DOCTYPE html>
        <html>
          <head>
            <title>Print ${projectTitle || 'Sketchbook'}</title>
            <style>
              @page {
                size: ${pageSize.orientation === 'landscape' ? 'landscape' : 'portrait'};
                margin: 0mm;
              }
              body {
                margin: 0;
                padding: 0;
                background-color: white;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
                font-family: Arial, sans-serif;
              }
              .print-page {
                page-break-after: always;
                position: relative;
                background: white;
                margin: 0 auto;
                padding: 0;
                width: ${a4Width}px;
                min-height: ${a4Height}px;
                height: auto; /* Allow height to adjust to content */
                overflow: visible;
                box-sizing: border-box;
              }
              .print-page:last-child {
                page-break-after: auto;
              }
              .print-content {
                width: 100%;
                height: 100%;
                position: relative;
                overflow: visible;
              }
              img {
                display: block;
                max-width: 100%;
                object-fit: contain;
              }
              .page-number {
                position: absolute;
                bottom: 10px;
                right: 10px;
                font-size: 10px;
                color: #888;
              }
              .loading-message {
                text-align: center;
                padding: 30px;
                font-size: 18px;
                color: #666;
              }
              @media print {
                .no-print {
                  display: none;
                }
                html, body {
                  width: ${a4Width}px;
                  height: auto;
                }
                .print-page {
                  page-break-after: always;
                  page-break-inside: avoid;
                }
              }
              ${document.querySelector('style')?.innerHTML || ''}
            </style>
          </head>
          <body>
            <div class="no-print" style="text-align: center; padding: 20px; background: #f5f5f5; margin-bottom: 20px;">
              <h2>Your document is ready for printing</h2>
              <p>Click the Print button below or use Ctrl+P to print.</p>
              <button onclick="window.print(); window.opener.postMessage('printComplete', '*');" style="padding: 10px 20px; background: #174E86; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 16px;">Print Document</button>
              <p style="margin-top: 10px; font-size: 12px; color: #666;">After printing, you can close this window.</p>
            </div>
            <div id="loading-message" class="loading-message">
              <p>Preparing your document...</p>
            </div>
            <div id="pages-container" style="display: none;">
              <!-- Page content will be inserted here -->
            </div>
            <script>
              // Set up message passing to parent window
              window.addEventListener('beforeunload', function() {
                window.opener.postMessage('printWindowClosed', '*');
              });

              // Automatically reset loading state after 30 seconds as failsafe
              setTimeout(function() {
                window.opener.postMessage('printTimeout', '*');
              }, 30000);
            </script>
          </body>
        </html>
      `;

      // Write content to the document
      printWindow.document.open();
      // @ts-ignore - document.write is deprecated but works fine for this use case
      printWindow.document.write(printContent);

      // Use html2canvas to capture each page
      const pageElements = document.querySelectorAll(`.${styles.pageWrapper}`);
      const pagesContainer =
        printWindow.document.getElementById('pages-container');
      const loadingMessage =
        printWindow.document.getElementById('loading-message');

      // Get compliance report page if toggle is enabled
      const compliancePageElement =
        showComplianceReports && previousReportsAsMarkdown?.markdown_content
          ? document.querySelector(`.${styles.compliancePage}`)
          : null;

      if (!pagesContainer) {
        throw new Error('Pages container not found in print window');
      }

      // Add event listener for messages from print window
      const messageHandler = (event: MessageEvent) => {
        if (
          event.data === 'printComplete' ||
          event.data === 'printWindowClosed' ||
          event.data === 'printTimeout'
        ) {
          setIsExporting(false);
          window.removeEventListener('message', messageHandler);
        }
      };

      window.addEventListener('message', messageHandler);

      // Process each page
      for (let i = 0; i < pageElements.length; i++) {
        const pageElement = pageElements[i] as HTMLElement;
        const pageContent = pageElement.querySelector(
          `.${styles.page}`
        ) as HTMLElement;

        if (!pageContent) continue;

        // Capture the page content
        const canvas = await html2canvas(pageContent, {
          scale: 2, // Higher scale for better quality
          useCORS: true,
          allowTaint: true,
          backgroundColor: '#ffffff',
          logging: false,
          width: pageSize.width,
          height: pageContent.offsetHeight,
          onclone: (_clonedDoc: Document, clonedElement: HTMLElement) => {
            // Ensure all chart canvases render properly
            const chartContainers = clonedElement.querySelectorAll(
              `.${styles.chartContainer}`
            );
            chartContainers.forEach((container: Element) => {
              const chartContent = container.querySelector<HTMLElement>(
                `.${styles.chartContent}`
              );
              if (chartContent) {
                chartContent.style.overflow = 'visible';
              }

              // Make sure chart canvases render properly
              const canvases = container.querySelectorAll('canvas');
              canvases.forEach((canvas: HTMLCanvasElement) => {
                canvas.style.width = '100%';
                canvas.style.height = '100%';
              });
            });

            return clonedElement;
          },
        });

        // Create a page div and add the image
        const pageDiv = printWindow.document.createElement('div');
        pageDiv.className = 'print-page';

        const contentDiv = printWindow.document.createElement('div');
        contentDiv.className = 'print-content';

        const pageImg = printWindow.document.createElement('img');
        pageImg.src = canvas.toDataURL('image/png', 1.0);
        pageImg.style.width = '100%';

        const pageNumber = printWindow.document.createElement('div');
        pageNumber.className = 'page-number';
        pageNumber.textContent = `Page ${i + 1} of ${pageElements.length + (compliancePageElement ? 1 : 0)}`;

        contentDiv.appendChild(pageImg);
        pageDiv.appendChild(contentDiv);
        pageDiv.appendChild(pageNumber);
        pagesContainer.appendChild(pageDiv);
      }

      // Process compliance report page if toggle is enabled
      if (compliancePageElement && pagesContainer) {
        const complianceContent = compliancePageElement.querySelector(
          `.${styles.page}`
        ) as HTMLElement;

        if (complianceContent) {
          // Capture the compliance page content
          const canvas = await html2canvas(complianceContent, {
            scale: 2, // Higher scale for better quality
            useCORS: true,
            allowTaint: true,
            backgroundColor: '#ffffff',
            logging: false,
            width: pageSize.width,
            height: complianceContent.offsetHeight,
          });

          // Create a page div and add the image
          const pageDiv = printWindow.document.createElement('div');
          pageDiv.className = 'print-page';

          const contentDiv = printWindow.document.createElement('div');
          contentDiv.className = 'print-content';

          const pageImg = printWindow.document.createElement('img');
          pageImg.src = canvas.toDataURL('image/png', 1.0);
          pageImg.style.width = '100%';

          const pageNumber = printWindow.document.createElement('div');
          pageNumber.className = 'page-number';
          pageNumber.textContent = `Compliance Report (Page ${pageElements.length + 1} of ${pageElements.length + 1})`;

          contentDiv.appendChild(pageImg);
          pageDiv.appendChild(contentDiv);
          pageDiv.appendChild(pageNumber);
          pagesContainer.appendChild(pageDiv);
        }
      }

      // Hide loading message and show content
      if (loadingMessage && pagesContainer) {
        loadingMessage.style.display = 'none';
        pagesContainer.style.display = 'block';
      }

      // Restore original styles
      previewContent.style.transform = originalTransform;
      previewContent.style.transformOrigin = originalTransformOrigin;

      // Finalize document
      printWindow.document.close();

      // Add event listener for window load to handle printing
      printWindow.onload = () => {
        // Small delay to ensure all images are loaded
        setTimeout(() => {
          try {
            if (printWindow && !printWindow.closed) {
              printWindow.focus();
            }
          } catch (e) {
            console.error('Error focusing print window:', e);
          }
        }, 1000);
      };
    } catch (error) {
      console.error('Print error:', error);
      toast.error('Failed to prepare document for printing');
      setIsExporting(false);
    }
  };

  // Enhanced export function that directly captures the preview content
  const handleExport = async (type: 'email' | 'saveToDisk') => {
    if (isExportingState) return;
    handleMenuClose();

    // Show loading state
    setIsExporting(true);
    setPendingExport(type === 'email' ? 'PDF' : null);

    try {
      // Get the preview container that shows the actual content
      const previewContent = document.querySelector(
        `.${styles.zoomContainer}`
      ) as HTMLElement;
      if (!previewContent) {
        throw new Error('Preview content not found');
      }

      // Store original styles to restore later
      const originalTransform = previewContent.style.transform;
      const originalTransformOrigin = previewContent.style.transformOrigin;

      // Reset transform to ensure proper rendering
      previewContent.style.transform = 'none';
      previewContent.style.transformOrigin = 'top left';

      // Open a new window for PDF generation
      const printWindow = window.open('', '_blank');
      if (!printWindow) {
        toast.error('Please allow pop-ups for PDF export');
        setIsExporting(false);
        setPendingExport(null);
        return;
      }

      // Progress window doesn't need A4 dimensions, just a reference to page count
      const pageCount = pages.length;

      // Initialize PDF generation window
      const pdfWindowContent = `
        <!DOCTYPE html>
        <html>
          <head>
            <title>Generating PDF - ${projectTitle || 'Document'}</title>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1">
            <style>
              body {
                margin: 0;
                padding: 20px;
                font-family: Arial, sans-serif;
                background-color: #f5f5f5;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                min-height: 100vh;
              }
              .progress-container {
                width: 100%;
                max-width: 500px;
                text-align: center;
                padding: 30px;
                background: white;
                border-radius: 8px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
              }
              .progress-bar {
                width: 100%;
                height: 20px;
                background-color: #e0e0e0;
                border-radius: 10px;
                margin: 20px 0;
                overflow: hidden;
              }
              .progress-fill {
                height: 100%;
                background-color: #174E86;
                width: 0%;
                transition: width 0.3s ease;
                border-radius: 10px;
              }
              h2 {
                margin-top: 0;
                color: #333;
              }
              p {
                color: #666;
                margin-bottom: 30px;
              }
            </style>
          </head>
          <body>
            <div class="progress-container">
              <h2>Generating PDF</h2>
              <p>Please wait while we prepare your document...</p>
              <div class="progress-bar">
                <div id="progress-fill" class="progress-fill"></div>
              </div>
              <div id="status-text">Processing page 1 of ${pageCount}...</div>
            </div>
            <script>
              // Function to update progress bar
              function updateProgress(percent) {
                document.getElementById('progress-fill').style.width = percent + '%';
              }

              // Function to update status text
              function updateStatus(text) {
                document.getElementById('status-text').textContent = text;
              }

              // Listen for messages from parent window
              window.addEventListener('message', function(event) {
                if (event.data.type === 'progress') {
                  updateProgress(event.data.percent);
                  updateStatus(event.data.status || '');
                }
              });

              // Notify parent that we're ready
              window.opener.postMessage({ type: 'pdf-window-ready' }, '*');
            </script>
          </body>
        </html>
      `;

      // Write content to the document
      printWindow.document.open();
      // @ts-ignore - document.write is deprecated but works fine for this use case
      printWindow.document.write(pdfWindowContent);

      // Add message listener for PDF generation events
      const messageHandler = (event: MessageEvent) => {
        if (event.data.type === 'pdf-window-ready') {
          // Window is ready, start PDF generation process
          generatePDF(printWindow);
        }
      };

      window.addEventListener('message', messageHandler);

      // Function to generate PDF from preview content
      const generatePDF = async (pdfWindow: Window) => {
        try {
          // Import jsPDF directly in our code instead of relying on the window object
          // This ensures we have the library available
          const jsPDFModule = await import('jspdf');
          // Handle both ESM and CommonJS module formats
          const jsPDF = jsPDFModule.default || jsPDFModule;

          const doc = new jsPDF({
            orientation: pageSize.orientation,
            unit: 'pt', // Use points for better precision
            format: 'a4',
            compress: true,
            precision: 16,
            hotfixes: ['px_scaling'], // Fix scaling issues
          });

          // Get all page elements
          const pageElements = document.querySelectorAll(
            `.${styles.pageWrapper}`
          );

          // Get compliance report page if toggle is enabled
          const compliancePageElement =
            showComplianceReports && previousReportsAsMarkdown?.markdown_content
              ? document.querySelector(`.${styles.compliancePage}`)
              : null;

          const totalPages =
            pageElements.length + (compliancePageElement ? 1 : 0);

          // Process each page
          for (let i = 0; i < pageElements.length; i++) {
            // Add a new page for each page after the first
            if (i > 0) doc.addPage();

            // Update progress
            pdfWindow.postMessage(
              {
                type: 'progress',
                percent: Math.round((i / totalPages) * 80), // Use 80% for page processing
                status: `Processing page ${i + 1} of ${totalPages}...`,
              },
              '*'
            );

            const pageElement = pageElements[i] as HTMLElement;
            const pageContent = pageElement.querySelector(
              `.${styles.page}`
            ) as HTMLElement;

            if (!pageContent) continue;

            // Capture the page content
            const canvas = await html2canvas(pageContent, {
              scale: 3, // Higher scale for better quality
              useCORS: true,
              allowTaint: true,
              backgroundColor: '#ffffff',
              logging: false,
              width: pageSize.width,
              height: pageContent.offsetHeight,
              windowWidth: pageSize.width,
              windowHeight: pageContent.offsetHeight,
              onclone: (_clonedDoc: Document, clonedElement: HTMLElement) => {
                // Ensure all chart canvases render properly
                const chartContainers = clonedElement.querySelectorAll(
                  `.${styles.chartContainer}`
                );
                chartContainers.forEach((container: Element) => {
                  const chartContent = container.querySelector<HTMLElement>(
                    `.${styles.chartContent}`
                  );
                  if (chartContent) {
                    chartContent.style.overflow = 'visible';
                  }

                  // Make sure chart canvases render properly
                  const canvases = container.querySelectorAll('canvas');
                  canvases.forEach((canvas: HTMLCanvasElement) => {
                    canvas.style.width = '100%';
                    canvas.style.height = '100%';
                  });
                });

                return clonedElement;
              },
            });

            // Calculate dimensions to fit the page content to A4
            const imgData = canvas.toDataURL('image/png', 1.0);

            // Get the actual dimensions of the page content
            const contentWidth = pageSize.width;
            const contentHeight = pageContent.offsetHeight;

            // Calculate the aspect ratio of the content
            const contentAspect = contentWidth / contentHeight;

            // Calculate the dimensions for the PDF page (in points)
            // A4 in points: 595 x 842 (portrait) or 842 x 595 (landscape)
            const pdfWidth = pageSize.orientation === 'landscape' ? 842 : 595;
            const pdfHeight = pageSize.orientation === 'landscape' ? 595 : 842;

            // Calculate the maximum dimensions that will fit on the page with margins
            const margin = 20; // 20 point margin
            const maxWidth = pdfWidth - margin * 2;
            const maxHeight = pdfHeight - margin * 2;

            // Calculate the dimensions to fit the content proportionally
            let imgWidth, imgHeight;

            if (contentAspect > maxWidth / maxHeight) {
              // Content is wider than the page proportion
              imgWidth = maxWidth;
              imgHeight = imgWidth / contentAspect;
            } else {
              // Content is taller than the page proportion
              imgHeight = maxHeight;
              imgWidth = imgHeight * contentAspect;
            }

            // Center the image on the page
            const xOffset = margin + (maxWidth - imgWidth) / 2;
            const yOffset = margin + (maxHeight - imgHeight) / 2;

            // Add the image to the PDF with proper dimensions
            doc.addImage(
              imgData,
              'PNG',
              xOffset,
              yOffset,
              imgWidth,
              imgHeight,
              undefined,
              'FAST'
            );
          }

          // Process compliance report page if toggle is enabled
          if (compliancePageElement) {
            // Add a new page for the compliance report
            doc.addPage();

            // Update progress
            pdfWindow.postMessage(
              {
                type: 'progress',
                percent: Math.round((pageElements.length / totalPages) * 80),
                status: `Processing compliance report page...`,
              },
              '*'
            );

            const complianceContent = compliancePageElement.querySelector(
              `.${styles.page}`
            ) as HTMLElement;

            if (complianceContent) {
              // Capture the compliance page content
              const canvas = await html2canvas(complianceContent, {
                scale: 3, // Higher scale for better quality
                useCORS: true,
                allowTaint: true,
                backgroundColor: '#ffffff',
                logging: false,
                width: pageSize.width,
                height: complianceContent.offsetHeight,
                windowWidth: pageSize.width,
                windowHeight: complianceContent.offsetHeight,
              });

              // Calculate dimensions to fit the page content to A4
              const imgData = canvas.toDataURL('image/png', 1.0);

              // Get the actual dimensions of the page content
              const contentWidth = pageSize.width;
              const contentHeight = complianceContent.offsetHeight;

              // Calculate the aspect ratio of the content
              const contentAspect = contentWidth / contentHeight;

              // Calculate the dimensions for the PDF page (in points)
              // A4 in points: 595 x 842 (portrait) or 842 x 595 (landscape)
              const pdfWidth = pageSize.orientation === 'landscape' ? 842 : 595;
              const pdfHeight =
                pageSize.orientation === 'landscape' ? 595 : 842;

              // Calculate the maximum dimensions that will fit on the page with margins
              const margin = 20; // 20 point margin
              const maxWidth = pdfWidth - margin * 2;
              const maxHeight = pdfHeight - margin * 2;

              // Calculate the dimensions to fit the content proportionally
              let imgWidth, imgHeight;

              if (contentAspect > maxWidth / maxHeight) {
                // Content is wider than the page proportion
                imgWidth = maxWidth;
                imgHeight = imgWidth / contentAspect;
              } else {
                // Content is taller than the page proportion
                imgHeight = maxHeight;
                imgWidth = imgHeight * contentAspect;
              }

              // Center the image on the page
              const xOffset = margin + (maxWidth - imgWidth) / 2;
              const yOffset = margin + (maxHeight - imgHeight) / 2;

              // Add the image to the PDF with proper dimensions
              doc.addImage(
                imgData,
                'PNG',
                xOffset,
                yOffset,
                imgWidth,
                imgHeight,
                undefined,
                'FAST'
              );
            }
          }

          // Update progress for PDF generation
          pdfWindow.postMessage(
            {
              type: 'progress',
              percent: 90,
              status: 'Finalizing PDF...',
            },
            '*'
          );

          // Generate the PDF blob
          const pdfBlob = doc.output('blob');

          // Update progress for completion
          pdfWindow.postMessage(
            {
              type: 'progress',
              percent: 100,
              status: 'PDF generated successfully!',
            },
            '*'
          );

          // For both email and direct download, we'll return the PDF bytes to the parent component
          const arrayBuffer = await pdfBlob.arrayBuffer();
          const result = new Uint8Array(arrayBuffer);

          // Call the onExport function with the PDF data
          onExport(type, result);

          // Close the PDF window after a delay
          setTimeout(() => {
            if (pdfWindow && !pdfWindow.closed) {
              pdfWindow.close();
            }
          }, 1500);

          // Clean up
          setIsExporting(false);
          setPendingExport(null);
          window.removeEventListener('message', messageHandler);
        } catch (error) {
          console.error('PDF generation error:', error);
          toast.error('Failed to generate PDF. Please try again.');

          // Close the PDF window
          if (pdfWindow && !pdfWindow.closed) {
            pdfWindow.close();
          }

          // Clean up
          setIsExporting(false);
          setPendingExport(null);
          window.removeEventListener('message', messageHandler);
        } finally {
          // Restore original styles
          previewContent.style.transform = originalTransform;
          previewContent.style.transformOrigin = originalTransformOrigin;
        }
      };

      // Safety timeout to clear loading state if something goes wrong
      setTimeout(() => {
        if (isExportingState) {
          setIsExporting(false);
          setPendingExport(null);
          toast.error('PDF generation timed out. Please try again.');
          window.removeEventListener('message', messageHandler);

          // Close the PDF window if it's still open
          if (printWindow && !printWindow.closed) {
            printWindow.close();
          }
        }
      }, 60000); // 60 second timeout
    } catch (error) {
      console.error('PDF export error:', error);
      toast.error('Failed to generate PDF. Please try again.');
      setIsExporting(false);
      setPendingExport(null);
    }
  };

  // Calculate grid dimensions based on orientation and content height
  const getGridDimensions = () => {
    const gridCols = pageSize.orientation === 'portrait' ? 12 : 20;
    // Base grid rows is just for reference, we'll calculate actual rows based on content height
    const baseGridRows = pageSize.orientation === 'portrait' ? 20 : 12;

    // Row height calculation based on original page dimensions
    const rowHeight = pageSize.height / baseGridRows;
    const colWidth = pageSize.width / gridCols;

    return { gridCols, baseGridRows, rowHeight, colWidth };
  };

  return (
    <>
      <Dialog
        open={open}
        onClose={isExportingState ? undefined : onClose}
        maxWidth="xl"
        fullWidth
        PaperProps={{
          sx: {
            height: '90vh',
            maxHeight: '90vh',
            display: 'flex',
            flexDirection: 'column',
            overflow: 'hidden',
          },
        }}
      >
        <Box className={styles.previewHeader}>
          <Typography variant="h6" className={styles.previewTitle}>
            {projectTitle ? `Preview: ${projectTitle}` : 'Document Preview'}
          </Typography>
          <Box className={styles.headerControls}>
            {isExportingState && (
              <Box className={styles.exportingIndicator}>
                <CircularProgress size={24} sx={{ mr: 1 }} />
                <Typography variant="body2">
                  {pendingExportState
                    ? `Preparing ${pendingExportState}...`
                    : 'Preparing document...'}
                </Typography>
              </Box>
            )}
            <Box className={styles.zoomControlsHeader}>
              <Tooltip title="Zoom out">
                <IconButton
                  size="small"
                  onClick={handleZoomOut}
                  disabled={isExportingState}
                  className={styles.zoomButton}
                >
                  <ZoomOutIcon fontSize="small" />
                </IconButton>
              </Tooltip>
              <Typography className={styles.zoomLevel}>
                {Math.round(zoom * 100)}%
              </Typography>
              <Tooltip title="Zoom in">
                <IconButton
                  size="small"
                  onClick={handleZoomIn}
                  disabled={isExportingState}
                  className={styles.zoomButton}
                >
                  <ZoomInIcon fontSize="small" />
                </IconButton>
              </Tooltip>
              <Tooltip title="Reset zoom">
                <IconButton
                  size="small"
                  onClick={handleResetZoom}
                  disabled={isExportingState}
                  className={styles.zoomButton}
                >
                  <ZoomResetIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Box>
            <Tooltip title="Options">
              <IconButton
                size="small"
                onClick={handleMenuClick}
                disabled={isExportingState}
                className={styles.zoomButton}
              >
                <MoreVertIcon fontSize="small" />
              </IconButton>
            </Tooltip>
            <Tooltip title="Close preview">
              <IconButton
                edge="end"
                onClick={onClose}
                disabled={isExportingState}
                size="small"
              >
                <CloseIcon />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>

        <Menu
          anchorEl={menuAnchorEl}
          open={menuOpen}
          onClose={handleMenuClose}
          anchorOrigin={{
            vertical: 'bottom',
            horizontal: 'right',
          }}
          transformOrigin={{
            vertical: 'top',
            horizontal: 'right',
          }}
        >
          <MenuItem onClick={() => handleExport('saveToDisk')}>
            <DownloadIcon fontSize="small" sx={{ mr: 1 }} />
            Download as PDF
          </MenuItem>
          <MenuItem onClick={handleOpenShareDialog}>
            <ShareIcon fontSize="small" sx={{ mr: 1 }} />
            Share
          </MenuItem>
        </Menu>

        <DialogContent
          className={styles.previewContent}
          ref={previewContainerRef}
        >
          <div className={styles.previewContainer}>
            <div
              className={styles.zoomContainer}
              style={{
                transform: `scale(${zoom})`,
                transformOrigin: 'top center',
              }}
            >
              {pages && pages.length > 0 ? (
                pages.map((page, pageIndex) => {
                  const { rowHeight, colWidth } = getGridDimensions();

                  // Calculate dynamic page height based on content
                  let maxBottomPosition = pageSize.height;

                  // Add buffer to ensure heights match DropableEditor
                  const heightBuffer = 300; // Increased buffer to ensure all content is visible

                  if (layouts && layouts[page.id]?.length > 0) {
                    layouts[page.id].forEach((layout: any) => {
                      const bottom = (layout.y + layout.h) * rowHeight;
                      if (bottom > maxBottomPosition) {
                        maxBottomPosition = bottom;
                      }
                    });
                  }

                  // Calculate height that will fully contain all charts plus buffer
                  const calculatedHeight = Math.max(
                    pageSize.height,
                    maxBottomPosition + heightBuffer
                  );

                  // Calculate how many grid rows we need to fully cover the calculated height
                  // We want enough rows to cover the entire usable area
                  const usableHeight = calculatedHeight; // Account for page padding (20px top + 20px bottom)
                  const requiredGridRows = Math.ceil(usableHeight / rowHeight);

                  return (
                    <div key={page.id} className={styles.pageWrapper}>
                      <div className={styles.pageHeader}>
                        <span className={styles.pageNumber}>
                          Page {pageIndex + 1}
                        </span>
                        <h3 className={styles.pageName}>{page.name}</h3>
                      </div>

                      <div
                        className={styles.page}
                        style={{
                          width: pageSize.width,
                          // Use larger height to match DropableEditor
                          minHeight: pageSize.height,
                          height: calculatedHeight,
                        }}
                        data-original-height={pageSize.height}
                        data-max-bottom-position={maxBottomPosition}
                        data-calculated-height={calculatedHeight}
                      >
                        {/* Grid container that exactly matches the editor's grid */}
                        <div
                          className={styles.gridContainer}
                          style={{
                            display: 'grid',
                            gridTemplateColumns: `repeat(${pageSize.orientation === 'portrait' ? 12 : 20}, 1fr)`,
                            // Use dynamic number of rows based on calculated height
                            gridTemplateRows: `repeat(${requiredGridRows}, ${rowHeight}px)`,
                            // Set absolute positioning with explicit dimensions
                            position: 'absolute',
                            top: '20px',
                            left: '20px',
                            right: '20px',
                            bottom: '20px',
                            width: `calc(100% - 40px)`, // Full width minus padding
                            height: `${calculatedHeight - 40}px`, // Exact height minus padding
                            minHeight: `${pageSize.height - 40}px`, // At least original page height
                          }}
                        >
                          {/* First render charts from bottom to top to ensure proper stacking */}
                          {[...(charts[page.id] || [])]
                            // Sort charts by vertical position (y) from highest to lowest
                            .map((chart) => {
                              const layout = layouts[page.id]?.find(
                                (l: any) => l.i === chart.id
                              );
                              return { chart, layout, y: layout?.y || 0 };
                            })
                            // Sort by y position - higher y values (lower on the page) come first
                            .sort((a, b) => b.y - a.y)
                            .map(({ chart, layout }) => {
                              if (!layout) return null;

                              // Calculate exact dimensions for the chart
                              const chartWidth = layout.w * colWidth - 10;
                              const chartHeight = layout.h * rowHeight - 10;

                              return (
                                <div
                                  key={chart.id}
                                  className={styles.chartContainer}
                                  style={{
                                    gridColumn: `${layout.x + 1} / span ${layout.w}`,
                                    gridRow: `${layout.y + 1} / span ${layout.h}`,
                                    zIndex: 3000 - layout.y,
                                    position: 'relative',
                                    margin: '3px',
                                  }}
                                  data-chart-id={chart.id}
                                  data-chart-type={chart.type}
                                >
                                  <div
                                    className={styles.chartContent}
                                    style={{
                                      width: '100%',
                                      height: '100%',
                                      position: 'relative',
                                    }}
                                  >
                                    {chart.type === 'image' ? (
                                      <img
                                        src={chart.data.src}
                                        alt={chart.data.alt || 'Preview image'}
                                        style={{
                                          width: '100%',
                                          height: '100%',
                                          objectFit: 'contain',
                                        }}
                                      />
                                    ) : (
                                      <PreviewChart
                                        chart={chart}
                                        width={chartWidth}
                                        height={chartHeight}
                                        key={`${chart.id}-${chartWidth}-${chartHeight}`}
                                      />
                                    )}
                                  </div>
                                </div>
                              );
                            })}
                        </div>
                      </div>
                    </div>
                  );
                })
              ) : (
                <div className={styles.emptyPreview}>No pages to preview</div>
              )}

              {/* Render compliance reports as additional pages when toggle is enabled */}
              {showComplianceReports &&
                previousReportsAsMarkdown?.markdown_content && (
                  <div className={styles.compliancePage}>
                    <div className={styles.compliancePageHeader}>
                      <span className={styles.pageNumber}>
                        Compliance Report
                      </span>
                      <h3 className={styles.pageName}>Compliance Report</h3>
                    </div>
                    <div
                      className={styles.page}
                      style={{
                        width: pageSize.width,
                        minHeight: pageSize.height,
                        height: 'auto',
                        padding: '40px',
                      }}
                    >
                      <MarkdownRenderer
                        content={previousReportsAsMarkdown.markdown_content}
                      />
                    </div>
                  </div>
                )}
            </div>
          </div>
        </DialogContent>

        {/* Hidden element for printing, contains exact pixel-positioned elements */}
        <div ref={printableContentRef} style={{ display: 'none' }}>
          {pages && pages.length > 0
            ? pages.map((page) => {
                const { rowHeight, colWidth } = getGridDimensions();

                // Calculate page height for printing
                let maxBottomPosition = pageSize.height;
                if (layouts && layouts[page.id]?.length > 0) {
                  layouts[page.id].forEach((layout: any) => {
                    const bottom = (layout.y + layout.h) * rowHeight;
                    if (bottom > maxBottomPosition) {
                      maxBottomPosition = bottom;
                    }
                  });
                }
                const calculatedHeight = Math.max(
                  pageSize.height,
                  maxBottomPosition + 50
                );

                // A4 dimensions in pixels at 96 DPI
                const a4Width =
                  pageSize.orientation === 'landscape' ? 1123 : 794;
                const widthRatio = a4Width / pageSize.width;

                return (
                  <div key={`print-${page.id}`} className="print-container">
                    {/* <div className="page-header">
                <h3>{page.name}</h3>
                <span>Page {pageIndex + 1}</span>
              </div> */}
                    <div
                      className="page"
                      style={{
                        height: `${calculatedHeight * widthRatio}px`,
                      }}
                    >
                      {/* Render charts with exact pixel positioning */}
                      {(charts[page.id] || []).map((chart) => {
                        const layout = layouts[page.id]?.find(
                          (l: any) => l.i === chart.id
                        );
                        if (!layout) return null;

                        // Calculate exact pixel positions for print with A4 scaling
                        const left = layout.x * colWidth * widthRatio; // Add page padding & scale
                        const top = layout.y * rowHeight * widthRatio; // Add page padding & scale
                        const width = layout.w * colWidth * widthRatio; // Reduced padding
                        const height = layout.h * rowHeight * widthRatio; // Reduced padding

                        // Create chart-specific styling for textarea elements
                        const chartStyle: React.CSSProperties = {
                          position: 'absolute',
                          left: `${left}px`,
                          top: `${top}px`,
                          width: `${width}px`,
                          height: `${height}px`,
                          zIndex: 1000 - layout.y,
                        };

                        // Create content-specific styling based on chart type
                        const contentStyle =
                          chart.type === 'textarea'
                            ? { padding: '2px 10px 2px 2px' }
                            : {};

                        // Prepare chart data for non-image, non-textarea charts
                        const isChartType =
                          chart.type !== 'image' && chart.type !== 'textarea';

                        // If this is a chart, prepare the data to be serialized
                        let chartDataStr = '';
                        if (isChartType) {
                          try {
                            // Create a simplified version of the chart data for reconstruction
                            const printChartData = {
                              type: chart.type,
                              data: chart.data,
                              options: {
                                responsive: true,
                                maintainAspectRatio: false,
                                animation: false,
                                plugins: {
                                  legend: {
                                    display: true,
                                    position: 'top',
                                    labels: {
                                      boxWidth: 10,
                                      font: { size: 9 },
                                    },
                                  },
                                  tooltip: { enabled: false },
                                },
                                layout: {
                                  padding: {
                                    right: 5,
                                    left: 5,
                                    top: 5,
                                    bottom: 5,
                                  },
                                },
                              },
                            };

                            // Merge with chart's original options if available
                            if (chart.options) {
                              printChartData.options = {
                                ...printChartData.options,
                                ...chart.options,
                              };
                            }

                            chartDataStr = JSON.stringify(printChartData);
                          } catch (e) {
                            console.error(
                              'Failed to serialize chart data for printing',
                              e
                            );
                          }
                        }

                        if (isChartType) {
                          // For chart types, use canvas
                          return (
                            <div
                              key={`print-${chart.id}`}
                              className="chart-container"
                              style={chartStyle}
                              data-chart-type={chart.type}
                              data-chart-data={chartDataStr}
                            >
                              <div className="chart-content">
                                <canvas
                                  width={width}
                                  height={height}
                                  style={{ width: '100%', height: '100%' }}
                                />
                              </div>
                            </div>
                          );
                        } else if (chart.type === 'image') {
                          // For images
                          return (
                            <div
                              key={`print-${chart.id}`}
                              className="chart-container"
                              style={chartStyle}
                              data-chart-type="image"
                            >
                              <div
                                className="chart-content"
                                style={contentStyle}
                              >
                                <img
                                  src={chart.data.src}
                                  alt={chart.data.alt || 'Print image'}
                                  style={{
                                    width: '100%',
                                    height: '100%',
                                    objectFit: 'cover',
                                  }}
                                />
                              </div>
                            </div>
                          );
                        } else {
                          // For text content
                          const content =
                            chart.data.markdown ||
                            chart.data.text ||
                            chart.data.content ||
                            '';

                          return (
                            <div
                              key={`print-${chart.id}`}
                              className="chart-container"
                              style={chartStyle}
                              data-chart-type="textarea"
                            >
                              <div className="chart-wrapper">
                                <div
                                  style={{
                                    width: '100%',
                                    height: '100%',
                                    overflow: 'auto',
                                    ...(chart.data.style || {}),
                                  }}
                                >
                                  <MarkdownRenderer content={content} />
                                </div>
                              </div>
                            </div>
                          );
                        }
                      })}
                    </div>
                  </div>
                );
              })
            : null}

          {/* Add compliance report to hidden printable content */}
          {showComplianceReports &&
            previousReportsAsMarkdown?.markdown_content && (
              <div className="print-container compliance-print-container">
                <div
                  className="page compliance-page"
                  style={{
                    height: 'auto',
                    minHeight: pageSize.height,
                    padding: '40px',
                  }}
                >
                  <MarkdownRenderer
                    content={previousReportsAsMarkdown.markdown_content}
                  />
                </div>
              </div>
            )}
        </div>

        {isExportingState && (
          <Paper className={styles.exportingOverlay}>
            <CircularProgress size={24} />
            <Box>
              <Typography variant="body2" fontWeight={500}>
                {pendingExportState
                  ? `Preparing ${pendingExportState}...`
                  : 'Preparing for print...'}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                Please wait, this may take a moment
              </Typography>
            </Box>
          </Paper>
        )}

        <DialogActions className={styles.dialogActions}>
          {previousReportsAsMarkdown?.markdown_content && (
            <FormControlLabel
              control={
                <Switch
                  checked={showComplianceReports}
                  onChange={(e) => setShowComplianceReports(e.target.checked)}
                  color="primary"
                  disabled={isExportingState}
                />
              }
              label="Include Compliance Reports"
              className={styles.complianceToggle}
            />
          )}
          <div className={styles.actionButtons}>
            <Button
              onClick={onClose}
              disabled={isExportingState}
              variant="outlined"
              size="medium"
            >
              Close
            </Button>
            {/* <Button
              onClick={handlePrint}
              disabled={isExportingState}
              variant="contained"
              startIcon={<PrintIcon />}
              size="medium"
            >
              Print
            </Button> */}
            <Button
              disabled={isExportingState}
              variant="contained"
              color="primary"
              startIcon={<DownloadIcon />}
              size="medium"
              aria-label="Export PDF"
              aria-haspopup="true"
              endIcon={<MoreVertIcon />}
              onClick={handleMenuClick}
            >
              Export PDF
            </Button>
          </div>
        </DialogActions>
      </Dialog>

      <ShareDialog
        open={shareDialogOpen}
        onClose={handleCloseShareDialog}
        onSendEmail={handleSendEmail}
        onExport={handleDirectDownload}
        fileName={uploadedFileName}
        projectId={projectId}
      />
    </>
  );
};

export default EditorPreview;

import React from 'react';
import styles from './CustomButton.module.css';
import { useTheme } from '../../../hooks/useTheme';

interface ButtonProps {
  type?: 'primary' | 'secondary' | 'header';
  variant?: 'submit' | 'reset';
  label?: string;
  onClick?: any;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  disabled?: boolean;
  style?: React.CSSProperties;
  buttonProps?: React.ButtonHTMLAttributes<HTMLButtonElement>;
  isActive?: boolean;
  colorPicker?: boolean;
}

const CustomButton: React.FC<ButtonProps> = ({
  type = 'primary',
  variant = 'submit',
  label,
  onClick,
  leftIcon,
  rightIcon,
  disabled = false,
  style,
  buttonProps,
  isActive = false,
  colorPicker = false,
}) => {
  const theme = useTheme();

  const getButtonStyle = () => {
    const baseStyle = { ...style };
    if (colorPicker) {
      baseStyle.color = theme.colors.common.white;
    }
    if (disabled) {
      baseStyle.backgroundColor = theme.colors.grey[300];
      baseStyle.cursor = 'not-allowed';
    }
    return baseStyle;
  };

  return (
    <button
      type={variant}
      className={`${styles.button} ${styles[type]} ${isActive ? styles.active : ''}`}
      onClick={onClick}
      disabled={disabled}
      style={getButtonStyle()}
      {...buttonProps}
    >
      {leftIcon && <span className={styles.icon}>{leftIcon}</span>}
      <span>{label}</span>
      {rightIcon && <span className={styles.icon}>{rightIcon}</span>}
    </button>
  );
};

export default CustomButton;

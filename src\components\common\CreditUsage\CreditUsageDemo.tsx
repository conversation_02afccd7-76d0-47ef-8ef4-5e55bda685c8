import React from 'react';
import { Box, Typography, Paper } from '@mui/material';
import CreditUsage from './CreditUsage';

/**
 * Demo component to test CreditUsage component
 * This can be used for testing and development purposes
 */
const CreditUsageDemo: React.FC = () => {
  return (
    <Paper sx={{ p: 3, m: 2 }}>
      <Typography variant="h6" gutterBottom>
        Credit Usage Component Demo
      </Typography>
      
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
        <Box>
          <Typography variant="subtitle2" gutterBottom>
            Default Credit Usage (with details):
          </Typography>
          <CreditUsage showDetails={true} />
        </Box>
        
        <Box>
          <Typography variant="subtitle2" gutterBottom>
            Simple Credit Usage (no details):
          </Typography>
          <CreditUsage showDetails={false} />
        </Box>
        
        <Box>
          <Typography variant="subtitle2" gutterBottom>
            Credit Usage in a header-like layout:
          </Typography>
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            gap: 2, 
            p: 1, 
            border: '1px solid #ddd',
            borderRadius: 1 
          }}>
            <Typography variant="h6">Chat Header</Typography>
            <Box sx={{ flexGrow: 1 }} />
            <CreditUsage showDetails={true} />
            <Typography variant="body2">Actions</Typography>
          </Box>
        </Box>
      </Box>
    </Paper>
  );
};

export default CreditUsageDemo;

import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  CircularProgress,
  Alert,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
} from '@mui/material';
import {
  CheckCircle,
  Error,
  Warning,
  Refresh,
  BugReport,
} from '@mui/icons-material';
import { runPaymentDiagnostics } from '../../utils/payment/paymentDiagnostics';

interface DiagnosticResult {
  test: string;
  status: 'success' | 'error' | 'warning';
  message: string;
  details?: any;
  timestamp: string;
}

interface DiagnosticReport {
  overall: 'healthy' | 'degraded' | 'unhealthy';
  results: DiagnosticResult[];
  recommendations: string[];
}

interface PaymentDiagnosticsProps {
  open: boolean;
  onClose: () => void;
  planId?: string;
  userId?: string;
}

const PaymentDiagnostics: React.FC<PaymentDiagnosticsProps> = ({
  open,
  onClose,
  planId,
  userId,
}) => {
  const [isRunning, setIsRunning] = useState(false);
  const [report, setReport] = useState<DiagnosticReport | null>(null);

  const runDiagnostics = async () => {
    setIsRunning(true);
    setReport(null);

    try {
      const diagnosticReport = await runPaymentDiagnostics(planId, userId);
      setReport(diagnosticReport);
    } catch (error) {
      console.error('Diagnostics failed:', error);
      setReport({
        overall: 'unhealthy',
        results: [
          {
            test: 'Diagnostics',
            status: 'error',
            message: 'Failed to run diagnostics',
            timestamp: new Date().toISOString(),
          },
        ],
        recommendations: ['Please try again or contact support'],
      });
    } finally {
      setIsRunning(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle color="success" />;
      case 'warning':
        return <Warning color="warning" />;
      case 'error':
        return <Error color="error" />;
      default:
        return <BugReport />;
    }
  };

  const getOverallColor = (overall: string) => {
    switch (overall) {
      case 'healthy':
        return 'success';
      case 'degraded':
        return 'warning';
      case 'unhealthy':
        return 'error';
      default:
        return 'default';
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Box display="flex" alignItems="center" gap={1}>
          <BugReport />
          Payment System Diagnostics
        </Box>
      </DialogTitle>

      <DialogContent>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          This tool helps diagnose payment connectivity issues. Click "Run
          Diagnostics" to test your connection to the payment service.
        </Typography>

        {!report && !isRunning && (
          <Box textAlign="center" py={3}>
            <Button
              variant="contained"
              onClick={runDiagnostics}
              startIcon={<Refresh />}
              size="large"
            >
              Run Diagnostics
            </Button>
          </Box>
        )}

        {isRunning && (
          <Box textAlign="center" py={3}>
            <CircularProgress size={40} />
            <Typography variant="body2" sx={{ mt: 2 }}>
              Running diagnostics...
            </Typography>
          </Box>
        )}

        {report && (
          <Box>
            {/* Overall Status */}
            <Box mb={2}>
              <Typography variant="h6" gutterBottom>
                Overall Status
              </Typography>
              <Chip
                label={report.overall.toUpperCase()}
                color={getOverallColor(report.overall) as any}
                variant="filled"
                size="medium"
              />
            </Box>

            <Divider sx={{ my: 2 }} />

            {/* Test Results */}
            <Typography variant="h6" gutterBottom>
              Test Results
            </Typography>
            <List dense>
              {report.results.map((result, index) => (
                <ListItem key={index}>
                  <ListItemIcon>{getStatusIcon(result.status)}</ListItemIcon>
                  <ListItemText
                    primary={result.test}
                    secondary={
                      <Box>
                        <Typography variant="body2" color="text.secondary">
                          {result.message}
                        </Typography>
                        {result.details && (
                          <Typography variant="caption" color="text.secondary">
                            {new Date(result.timestamp).toLocaleTimeString()}
                          </Typography>
                        )}
                      </Box>
                    }
                  />
                </ListItem>
              ))}
            </List>

            <Divider sx={{ my: 2 }} />

            {/* Recommendations */}
            <Typography variant="h6" gutterBottom>
              Recommendations
            </Typography>
            {report.recommendations.map((recommendation, index) => (
              <Alert
                key={index}
                severity={report.overall === 'healthy' ? 'success' : 'info'}
                sx={{ mb: 1 }}
              >
                {recommendation}
              </Alert>
            ))}

            {/* Retry Button */}
            <Box mt={2} textAlign="center">
              <Button
                variant="outlined"
                onClick={runDiagnostics}
                startIcon={<Refresh />}
                disabled={isRunning}
              >
                Run Again
              </Button>
            </Box>
          </Box>
        )}
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose}>Close</Button>
      </DialogActions>
    </Dialog>
  );
};

export default PaymentDiagnostics;

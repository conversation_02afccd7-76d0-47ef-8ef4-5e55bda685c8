.registrationContainer {
  position: fixed;
  background-color: white;
  background-image: url('../../assets/images/Login.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: right;
  overflow-y: auto;
  height: 100vh;
  width: 100vw;
}

.headingContainer {
  margin-top: 30px;
  margin-left: 132px;
  text-align: left;
  display: flex;
  flex-direction: column;
  width: 50%;
}

.heading {
  font-family: var(--font-family-primary);
  font-size: 48px;
  font-weight: var(--font-weight-semibold);
  color: #0e2f51;
}

.subHeading {
  font-family: var(--font-family-primary);
  font-size: 16px;
  font-weight: var(--font-weight-normal);
  color: #667085;
}

.formContainer {
  width: 30%;
  margin-left: 132px;
  margin-top: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.linkContainer {
  font-family: var(--font-family-primary);
  font-size: 14px;
  color: #0e2f51;
  font-weight: var(--font-weight-medium);
  margin-top: 5px;
  margin-bottom: 10px;
}

.link {
  color: #1b5ea1;
  font-size: 14px;
  font-weight: var(--font-weight-semibold);
  text-decoration: none;
  margin-left: 4px;
}

.link:hover {
  text-decoration: underline;
}

.vectorLineContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
}

.optionalText {
  font-family: var(--font-family-primary);
  font-size: 14px;
  font-weight: var(--font-weight-semibold);
  font-style: italic;
  color: #667085;
}

.error {
  color: var(--color-error-main);
  font-family: var(--font-family-primary);
  font-size: 14px;
  font-weight: var(--font-weight-normal);
}

.checkboxContainer {
  display: flex;
  gap: 10px;
}

/* Force light theme for registration page - completely override dark theme styles */
.registrationContainer [data-theme='dark'] .inputLabel,
.registrationContainer .inputLabel,
.registrationContainer [data-theme='dark'] .force-light-theme .inputLabel,
.registrationContainer .force-light-theme .inputLabel,
.registrationContainer [data-force-light-theme='true'] .inputLabel {
  color: #0e2f51 !important;
}

.registrationContainer [data-theme='dark'] .input,
.registrationContainer .input,
.registrationContainer [data-theme='dark'] .force-light-theme .input,
.registrationContainer .force-light-theme .input,
.registrationContainer [data-force-light-theme='true'] .input {
  background-color: #ffffff !important;
  border-color: #e0e0e0 !important;
  color: #0e2f51 !important;
}

.registrationContainer [data-theme='dark'] .input::placeholder,
.registrationContainer .input::placeholder,
.registrationContainer
  [data-theme='dark']
  .force-light-theme
  .input::placeholder,
.registrationContainer .force-light-theme .input::placeholder,
.registrationContainer [data-force-light-theme='true'] .input::placeholder {
  color: #667085 !important;
}

.registrationContainer [data-theme='dark'] .helperText,
.registrationContainer .helperText,
.registrationContainer [data-theme='dark'] .force-light-theme .helperText,
.registrationContainer .force-light-theme .helperText,
.registrationContainer [data-force-light-theme='true'] .helperText {
  color: #667085 !important;
}

.registrationContainer [data-theme='dark'] .eyeIcon,
.registrationContainer .eyeIcon,
.registrationContainer [data-theme='dark'] .force-light-theme .eyeIcon,
.registrationContainer .force-light-theme .eyeIcon,
.registrationContainer [data-force-light-theme='true'] .eyeIcon {
  color: #1b5ea1 !important;
}

.registrationContainer [data-theme='dark'] .required,
.registrationContainer .required,
.registrationContainer [data-theme='dark'] .force-light-theme .required,
.registrationContainer .force-light-theme .required,
.registrationContainer [data-force-light-theme='true'] .required {
  color: #d32f2f !important;
}

/* Focus and error states */
.registrationContainer [data-theme='dark'] .input:focus,
.registrationContainer .input:focus,
.registrationContainer [data-theme='dark'] .force-light-theme .input:focus,
.registrationContainer .force-light-theme .input:focus,
.registrationContainer [data-force-light-theme='true'] .input:focus {
  border-color: #1b5ea1 !important;
  box-shadow: 0 0 0 2px rgba(27, 94, 161, 0.1) !important;
}

.registrationContainer [data-theme='dark'] .inputError,
.registrationContainer .inputError,
.registrationContainer [data-theme='dark'] .force-light-theme .inputError,
.registrationContainer .force-light-theme .inputError,
.registrationContainer [data-force-light-theme='true'] .inputError {
  border-color: #d32f2f !important;
}

/* Checkbox styling to ensure light theme */
.registrationContainer [data-theme='dark'] input[type='checkbox'],
.registrationContainer input[type='checkbox'] {
  background-color: #ffffff !important;
  border-color: #e0e0e0 !important;
  accent-color: #174e86 !important;
}

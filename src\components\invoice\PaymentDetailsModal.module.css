.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
}

.dark .overlay {
  background: rgba(0, 0, 0, 0.7);
}

.modal {
  background: var(--card-background, #ffffff);
  border-radius: 1rem;
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  border: 1px solid var(--border-color, #e5e7eb);
}

.dark .modal {
  background: var(--dark-card-background, #2d2d2d);
  border-color: var(--dark-border, #404040);
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.3),
    0 10px 10px -5px rgba(0, 0, 0, 0.2);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color, #e5e7eb);
  background: var(--header-bg, #f9fafb);
}

.dark .header {
  border-bottom-color: var(--dark-border, #404040);
  background: var(--dark-header-bg, #374151);
}

.title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-color, #111827);
}

.dark .title {
  color: var(--dark-text, #f9fafb);
}

.closeButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  background: none;
  color: var(--text-secondary, #6b7280);
  cursor: pointer;
  border-radius: 0.5rem;
  transition: all 0.2s ease;
}

.closeButton:hover {
  background: var(--hover-bg, #f3f4f6);
  color: var(--text-color, #111827);
}

.dark .closeButton {
  color: var(--dark-text-secondary, #9ca3af);
}

.dark .closeButton:hover {
  background: var(--dark-hover-bg, #4b5563);
  color: var(--dark-text, #f9fafb);
}

.content {
  flex: 1;
  overflow-y: auto;
  padding: 1.5rem;
}

.section {
  margin-bottom: 2rem;
}

.section:last-child {
  margin-bottom: 0;
}

.sectionTitle {
  margin: 0 0 1rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-color, #111827);
  border-bottom: 1px solid var(--border-color, #e5e7eb);
  padding-bottom: 0.5rem;
}

.dark .sectionTitle {
  color: var(--dark-text, #f9fafb);
  border-bottom-color: var(--dark-border, #404040);
}

.detailsGrid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

.detailItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: var(--detail-bg, #f9fafb);
  border-radius: 0.5rem;
  border: 1px solid var(--border-color, #e5e7eb);
}

.dark .detailItem {
  background: var(--dark-detail-bg, #374151);
  border-color: var(--dark-border, #4b5563);
}

.label {
  font-weight: 500;
  color: var(--text-secondary, #6b7280);
  font-size: 0.875rem;
}

.dark .label {
  color: var(--dark-text-secondary, #9ca3af);
}

.value {
  font-weight: 600;
  color: var(--text-color, #111827);
  font-size: 0.875rem;
  text-align: right;
  word-break: break-all;
  max-width: 60%;
}

.dark .value {
  color: var(--dark-text, #f9fafb);
}

.timeline {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.timelineItem {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  background: var(--timeline-bg, #f9fafb);
  border-radius: 0.5rem;
  border-left: 4px solid var(--primary-color, #2563eb);
}

.dark .timelineItem {
  background: var(--dark-timeline-bg, #374151);
  border-left-color: var(--dark-primary, #60a5fa);
}

.timelineIcon {
  font-size: 1.25rem;
  line-height: 1;
  margin-top: 0.125rem;
}

.timelineContent {
  flex: 1;
}

.timelineTitle {
  font-weight: 600;
  color: var(--text-color, #111827);
  margin-bottom: 0.25rem;
  font-size: 0.875rem;
}

.dark .timelineTitle {
  color: var(--dark-text, #f9fafb);
}

.timelineDate {
  color: var(--text-secondary, #6b7280);
  font-size: 0.75rem;
}

.dark .timelineDate {
  color: var(--dark-text-secondary, #9ca3af);
}

.footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  border-top: 1px solid var(--border-color, #e5e7eb);
  background: var(--footer-bg, #f9fafb);
}

.dark .footer {
  border-top-color: var(--dark-border, #404040);
  background: var(--dark-footer-bg, #374151);
}

.downloadSection {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.formatSelect {
  padding: 0.5rem;
  border: 1px solid var(--border-color, #d1d5db);
  border-radius: 0.375rem;
  background: var(--input-background, #ffffff);
  color: var(--text-color, #374151);
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.formatSelect:focus {
  outline: none;
  border-color: var(--primary-color, #2563eb);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.dark .formatSelect {
  background: var(--dark-input-background, #374151);
  color: var(--dark-text, #f9fafb);
  border-color: var(--dark-border, #4b5563);
}

.dark .formatSelect:focus {
  border-color: var(--dark-primary, #60a5fa);
  box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1);
}

.downloadButton {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: var(--primary-color, #2563eb);
  color: white;
  border: none;
  border-radius: 0.5rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
  font-size: 0.875rem;
}

.downloadButton:hover {
  background: var(--primary-hover, #1d4ed8);
}

.dark .downloadButton {
  background: var(--dark-primary, #60a5fa);
  color: var(--dark-text, #1f2937);
}

.dark .downloadButton:hover {
  background: var(--dark-primary-hover, #3b82f6);
}

.closeButtonSecondary {
  padding: 0.75rem 1rem;
  background: var(--secondary-bg, #f3f4f6);
  color: var(--text-color, #374151);
  border: 1px solid var(--border-color, #d1d5db);
  border-radius: 0.5rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
}

.closeButtonSecondary:hover {
  background: var(--secondary-hover, #e5e7eb);
  border-color: var(--border-hover, #9ca3af);
}

.dark .closeButtonSecondary {
  background: var(--dark-secondary-bg, #4b5563);
  color: var(--dark-text, #f9fafb);
  border-color: var(--dark-border, #6b7280);
}

.dark .closeButtonSecondary:hover {
  background: var(--dark-secondary-hover, #6b7280);
  border-color: var(--dark-border-hover, #9ca3af);
}

/* Responsive Design */
@media (max-width: 768px) {
  .overlay {
    padding: 0.5rem;
  }

  .modal {
    max-height: 95vh;
  }

  .header,
  .content,
  .footer {
    padding: 1rem;
  }

  .detailItem {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .value {
    max-width: 100%;
    text-align: left;
  }

  .footer {
    flex-direction: column;
  }

  .downloadButton,
  .closeButtonSecondary {
    width: 100%;
    justify-content: center;
  }
}

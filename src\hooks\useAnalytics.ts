/**
 * Custom hook for Google Analytics 4 integration with React Router
 * Provides automatic page view tracking and user property management
 */

import { useEffect, useRef } from 'react';
import { useLocation } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { RootState } from '../store/store';
import {
  initializeGA4,
  trackPageView,
  setUserProperties,
  trackAuth,
  trackNavigation,
  trackContent,
  trackWorkflow,
  trackPayment,
  trackSearch,
  trackError,
} from '../utils/analytics';

/**
 * Hook for automatic page view tracking
 */
export const usePageTracking = (): void => {
  const location = useLocation();
  const { userDetails, isAuthenticated } = useSelector((state: RootState) => state.auth);
  const previousLocation = useRef<string>('');

  useEffect(() => {
    // Initialize GA4 on first load
    initializeGA4();
  }, []);

  useEffect(() => {
    const currentPath = location.pathname + location.search;
    
    // Avoid tracking the same page twice
    if (previousLocation.current === currentPath) {
      return;
    }

    // Get page title from document or generate from path
    const pageTitle = document.title || getPageTitleFromPath(location.pathname);
    const pageLocation = window.location.href;
    const pagePath = currentPath;

    // Track page view
    trackPageView(
      pageTitle,
      pageLocation,
      pagePath,
      isAuthenticated && userDetails?.id ? userDetails.id : undefined
    );

    // Update previous location
    previousLocation.current = currentPath;
  }, [location, isAuthenticated, userDetails]);

  // Update user properties when authentication state changes
  useEffect(() => {
    if (isAuthenticated && userDetails) {
      setUserProperties({
        user_id: userDetails.id,
        user_role: userDetails.role || 'user',
        subscription_status: userDetails.subscriptionStatus || 'free',
      });
    }
  }, [isAuthenticated, userDetails]);
};

/**
 * Generate page title from pathname
 */
const getPageTitleFromPath = (pathname: string): string => {
  const pathMap: Record<string, string> = {
    '/': 'Home - NEUQUIP',
    '/chat': 'Chat - NEUQUIP',
    '/my-projects': 'My Projects - NEUQUIP',
    '/library': 'Library - NEUQUIP',
    '/sketchbooklists': 'Sketchbooks - NEUQUIP',
    '/sketchbook': 'Sketchbook Editor - NEUQUIP',
    '/workflow': 'Workflow - NEUQUIP',
    '/awaiting-actions': 'Awaiting Actions - NEUQUIP',
    '/awaited-actions-view': 'Action Details - NEUQUIP',
    '/edit-profile': 'Edit Profile - NEUQUIP',
    '/payment': 'Payment - NEUQUIP',
    '/payment/success': 'Payment Success - NEUQUIP',
    '/payment/error': 'Payment Error - NEUQUIP',
    '/login': 'Login - NEUQUIP',
    '/signup': 'Sign Up - NEUQUIP',
    '/forgot-password': 'Forgot Password - NEUQUIP',
    '/reset-password': 'Reset Password - NEUQUIP',
    '/price': 'Pricing - NEUQUIP',
    '/dashboard': 'Admin Dashboard - NEUQUIP',
    '/invoices': 'Invoices - NEUQUIP',
    '/settings': 'Settings - NEUQUIP',
    '/billing': 'Billing - NEUQUIP',
  };

  return pathMap[pathname] || `${pathname.replace('/', '').replace('-', ' ')} - NEUQUIP`;
};

/**
 * Hook for analytics event tracking with user context
 */
export const useAnalyticsEvents = () => {
  const { userDetails, isAuthenticated } = useSelector((state: RootState) => state.auth);
  const location = useLocation();

  const getUserId = (): string | undefined => {
    return isAuthenticated && userDetails?.id ? userDetails.id : undefined;
  };

  const getCurrentPage = (): string => {
    return location.pathname;
  };

  return {
    // Authentication events
    trackLogin: (method: string = 'email') => {
      const userId = getUserId();
      if (userId) {
        trackAuth.login(method, userId);
      }
    },

    trackSignup: (method: string = 'email') => {
      trackAuth.signup(method);
    },

    trackLogout: () => {
      const userId = getUserId();
      if (userId) {
        trackAuth.logout(userId);
      }
    },

    // Navigation events
    trackMenuClick: (menuItem: string) => {
      trackNavigation.menuClick(menuItem, getUserId());
    },

    trackButtonClick: (buttonName: string) => {
      trackNavigation.buttonClick(buttonName, getCurrentPage(), getUserId());
    },

    // Content creation events
    trackCreateProject: (projectType: string = 'general') => {
      const userId = getUserId();
      if (userId) {
        trackContent.createProject(projectType, userId);
      }
    },

    trackCreateSketchbook: (templateType: string = 'blank') => {
      const userId = getUserId();
      if (userId) {
        trackContent.createSketchbook(templateType, userId);
      }
    },

    trackFileUpload: (fileType: string, fileSize: number) => {
      const userId = getUserId();
      if (userId) {
        trackContent.uploadFile(fileType, fileSize, userId);
      }
    },

    trackSendMessage: (messageType: string = 'text') => {
      const userId = getUserId();
      if (userId) {
        trackContent.sendMessage(messageType, userId);
      }
    },

    // Workflow events
    trackCreateWorkflow: (workflowType: string = 'approval') => {
      const userId = getUserId();
      if (userId) {
        trackWorkflow.createWorkflow(workflowType, userId);
      }
    },

    trackApproveWorkflow: (workflowId: string) => {
      const userId = getUserId();
      if (userId) {
        trackWorkflow.approveWorkflow(workflowId, userId);
      }
    },

    trackRejectWorkflow: (workflowId: string) => {
      const userId = getUserId();
      if (userId) {
        trackWorkflow.rejectWorkflow(workflowId, userId);
      }
    },

    // Payment events
    trackInitiatePayment: (amount: number, currency: string = 'USD') => {
      const userId = getUserId();
      if (userId) {
        trackPayment.initiatePayment(amount, currency, userId);
      }
    },

    trackCompletePayment: (amount: number, transactionId: string, currency: string = 'USD') => {
      const userId = getUserId();
      if (userId) {
        trackPayment.completePayment(amount, currency, transactionId, userId);
      }
    },

    trackPaymentError: (errorMessage: string) => {
      const userId = getUserId();
      if (userId) {
        trackPayment.paymentError(errorMessage, userId);
      }
    },

    // Search events
    trackSearch: (searchTerm: string, searchType: string = 'general') => {
      trackSearch.search(searchTerm, searchType, getUserId());
    },

    // Error tracking
    trackJSError: (errorMessage: string, errorStack?: string) => {
      trackError.jsError(errorMessage, errorStack, getUserId());
    },

    trackAPIError: (endpoint: string, statusCode: number, errorMessage: string) => {
      trackError.apiError(endpoint, statusCode, errorMessage, getUserId());
    },

    // Utility functions
    getUserId,
    getCurrentPage,
    isUserAuthenticated: () => isAuthenticated,
    getUserRole: () => userDetails?.role || 'user',
  };
};

/**
 * Hook for error boundary analytics tracking
 */
export const useErrorTracking = () => {
  const { trackJSError } = useAnalyticsEvents();

  useEffect(() => {
    const handleError = (event: ErrorEvent) => {
      trackJSError(
        event.message,
        event.error?.stack
      );
    };

    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      trackJSError(
        `Unhandled Promise Rejection: ${event.reason}`,
        event.reason?.stack
      );
    };

    window.addEventListener('error', handleError);
    window.addEventListener('unhandledrejection', handleUnhandledRejection);

    return () => {
      window.removeEventListener('error', handleError);
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    };
  }, [trackJSError]);
};

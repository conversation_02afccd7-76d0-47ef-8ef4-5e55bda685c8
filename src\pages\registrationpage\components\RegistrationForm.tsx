import React from 'react';
import { Controller } from 'react-hook-form';
import { Link, useNavigate } from 'react-router-dom';
import toast from 'react-hot-toast';
import CustomButton from '../../../components/common/button/CustomButton';
import CustomInput from '../../../components/common/input/CustomInput';
import { useRegistrationForm } from '../../../hooks/useAuthForm';
import { useSignupMutation } from '../../../services/authService';
import { SignupCredentials } from '../../../types/authtypes';
import { getErrorMessage } from '../../../utils/errorHandler';
import { useAnalyticsEvents } from '../../../hooks/useAnalytics';
import styles from '../../loginpage/LoginPage.module.css';
import axios from 'axios';
import { AI_BASE_URL } from '../../../services/config';

type FormData = {
  name: string;
  email: string;
  password: string;
  organization?: string;
  agreeTerms: boolean;
};

const RegistrationForm: React.FC = () => {
  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useRegistrationForm();

  const navigate = useNavigate();
  const [signup, { isLoading }] = useSignupMutation();
  const { trackSignup, trackJSError } = useAnalyticsEvents();

  const handleRegistration = async (formData: FormData) => {
    try {
      const signupData: SignupCredentials = {
        name: formData.name,
        email: formData.email,
        password: formData.password,
        organization: formData.organization ?? '',
      };

      const res = await signup(signupData).unwrap();
      axios.post(`${AI_BASE_URL}/import-templated-projects`, {
        user_id: res.data.id,
      });

      localStorage.setItem('hasSeenWelcome', 'false');

      navigate('/login');
      toast.success('Account Created Successfully');

      // Track successful signup
      trackSignup('email');
    } catch (err: any) {
      const errorMessage = getErrorMessage(err);
      toast.error(errorMessage);
      console.error('Registration failed:', err);

      // Track signup error
      trackJSError(`Registration failed: ${errorMessage}`);
    }
  };

  return (
    <div className={styles.formContainer}>
      <Controller
        name="name"
        control={control}
        render={({ field }) => (
          <CustomInput
            label="Name"
            type="text"
            placeholder="Enter Your Name"
            {...field}
            helperText={errors.name?.message}
            error={!!errors.name?.message}
            disableDarkTheme={true}
          />
        )}
      />
      <Controller
        name="email"
        control={control}
        render={({ field }) => (
          <CustomInput
            label="Email address"
            type="email"
            placeholder="Enter Your Email"
            {...field}
            helperText={errors.email?.message}
            error={!!errors.email?.message}
            disableDarkTheme={true}
          />
        )}
      />
      <Controller
        name="organization"
        control={control}
        render={({ field }) => (
          <CustomInput
            label={
              <>
                Organization{' '}
                <span className={styles.optionalText}>(If Any)</span>
              </>
            }
            type="text"
            placeholder="Enter Your Organization"
            {...field}
            helperText={errors.organization?.message}
            error={!!errors.organization?.message}
            disableDarkTheme={true}
          />
        )}
      />
      <Controller
        name="password"
        control={control}
        render={({ field }) => (
          <CustomInput
            label="Set Password"
            type="password"
            placeholder="Enter Your Password"
            {...field}
            helperText={errors.password?.message}
            error={!!errors.password?.message}
            disableDarkTheme={true}
          />
        )}
      />
      <div className={styles.checkboxContainer}>
        <Controller
          name="agreeTerms"
          control={control}
          render={({ field }) => (
            <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
              <input
                style={{
                  width: '10px',
                  margin: '0px',
                  zoom: 1.5,
                  accentColor: '#174E86',
                  backgroundColor: '#ffffff',
                  borderColor: '#e0e0e0',
                }}
                type="checkbox"
                checked={field.value || false}
                onChange={(e) => field.onChange(e.target.checked)}
              />
              <h6 className={styles.linkContainer} style={{ color: '#0e2f51' }}>
                I agree to the terms and conditions
              </h6>
              {errors.agreeTerms && (
                <span className={styles.error}>
                  {errors.agreeTerms.message}
                </span>
              )}
            </div>
          )}
        />
      </div>
      <CustomButton
        label={isLoading ? 'Signing up...' : 'Sign up'}
        type="primary"
        onClick={handleSubmit((data) => handleRegistration(data as FormData))}
        disabled={isLoading}
      />
      <div className={styles.linkContainer}>
        Already have an account?{' '}
        <Link to="/login" className={styles.link}>
          Login
        </Link>
      </div>
    </div>
  );
};

export default RegistrationForm;

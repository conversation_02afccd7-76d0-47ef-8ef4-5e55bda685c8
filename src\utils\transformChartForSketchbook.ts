// First, define the interface for the chart data
export interface ChartDataInput {
  chart_id: string;
  type_of_chart: string;
  chart_name: string;
  position: {
    x: number;
    y: number;
  };
  size: {
    width: number;
    height: number;
  };
  x_labels: (string | Date | number)[];
  values: number[] | [number, number, number][]; // Allow for both simple values and bubble chart coordinates
  options?: any; // For additional chart-specific options
}

interface GaugeChartData extends Omit<ChartDataInput, 'x_labels' | 'values'> {
  data: {
    percent: number;
  };
  chartType: string;
  options: {
    id: string;
    nrOfLevels: number;
    colors: string[];
    arcWidth: number;
    arcPadding: number;
    cornerRadius: number;
    needleColor: string;
    needleBaseColor: string;
    textColor: string;
    animate: boolean;
    animDelay: number;
    animateDuration: number;
    marginInPercent: number;
    formatTextValue: (value: number) => string;
    needleScale: number;
    responsive: boolean;
    maintainAspectRatio: boolean;
  };
}

interface TextAreaChartData
  extends Omit<ChartDataInput, 'x_labels' | 'values'> {
  data: {
    markdown: string;
  };
  chartType: string;
}

interface GanttChartData extends Omit<ChartDataInput, 'x_labels' | 'values'> {
  data: {
    tasks: Array<{
      id: string;
      name: string;
      start: string;
      end: string;
      progress: number;
      dependencies: string[];
      status: string;
    }>;
  };
  chartType: string;
}

interface ImageChartData extends Omit<ChartDataInput, 'x_labels' | 'values'> {
  data: {
    src: string;
    alt: string;
  };
  chartType: string;
}

export const transformChartDataForSketchbook = (chart: any) => {
  if (!chart) {
    console.warn('transformChartDataForSketchbook received no input');
    return null;
  }

  // Add specific handling for textarea type
  if (chart.type_of_chart === 'textarea') {
    return {
      chartType: chart.chartType,
      id: chart.chart_id,
      type: 'textarea',
      position: chart.position,
      size: chart.size,
      data: {
        markdown: chart.data.markdown,
        style: chart.data.style || {
          fontSize: '14px',
          lineHeight: '1.5',
          padding: '12px',
        },
      },
    };
  }

  // Type guard to check if it's a special chart type
  const isGaugeChart = (chart: any): chart is GaugeChartData =>
    'data' in chart && 'percent' in chart.data;
  const isTextAreaChart = (chart: any): chart is TextAreaChartData =>
    'data' in chart && 'markdown' in chart.data;
  const isGanttChart = (chart: any): chart is GanttChartData =>
    'data' in chart && 'tasks' in chart.data;
  const isImageChart = (chart: any): chart is ImageChartData =>
    chart.type_of_chart === 'image' && 'data' in chart;

  // Handle special chart types
  // debugger;
  if (
    isGaugeChart(chart) ||
    isTextAreaChart(chart) ||
    isGanttChart(chart) ||
    isImageChart(chart)
  ) {
    return {
      chartType: chart.chartType,
      id: chart.chart_id,
      type: chart.type_of_chart,
      position: chart.position,
      size: chart.size,
      data: chart.data,
      options: chart.options,
    };
  }

  let dataset = {
    label: chart.chart_name,
    data: chart.values,
  };

  switch (chart.type_of_chart) {
    case 'horizontal':
      return {
        chartType: chart.chartType,
        id: chart.id || chart.chart_id,
        type: 'bar',
        title: chart.title || 'Horizontal Bar Chart',
        position: chart.position || { x: 0, y: 0 },
        size: chart.size || { width: 400, height: 300 },
        data: {
          labels: chart.labels || chart.x_labels || [],
          datasets:
            Array.isArray(chart.datasets) && chart.datasets.length > 0
              ? chart.datasets.map((dataset: any) => ({
                  label: dataset.label || 'Dataset',
                  data: dataset.data || [],
                  backgroundColor:
                    dataset.backgroundColor || 'rgba(197, 170, 255, 0.7)',
                  borderColor: dataset.borderColor || 'rgba(197, 170, 255, 1)',
                  borderWidth: dataset.borderWidth || 1,
                  fill: dataset.fill || false,
                }))
              : [
                  {
                    label: chart.chart_name || 'Dataset',
                    data: chart.values || [],
                    backgroundColor:
                      chart.backgroundColor || 'rgba(197, 170, 255, 0.7)',
                    borderColor: chart.borderColor || 'rgba(197, 170, 255, 1)',
                    borderWidth: chart.borderWidth || 1,
                    fill: chart.fill || false,
                  },
                ],
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          indexAxis: 'y',
          plugins: {
            legend: {
              display: true,
              position: 'top',
            },
            title: {
              display: true,
              text: chart.title || 'Horizontal Bar Chart',
            },
          },
          scales: {
            x: {
              beginAtZero: true,
            },
            y: {
              ticks: {
                crossAlign: 'far',
              },
            },
          },
        },
      };

    case 'bubble':
      return {
        chartType: chart.chartType,
        id: chart.id || chart.chart_id,
        type: 'bubble',
        position: chart.position || { x: 0, y: 0 },
        size: chart.size || { width: 400, height: 300 },
        data: {
          labels: chart.labels || [],
          datasets: Array.isArray(chart.datasets)
            ? chart.datasets.map((dataset: any) => ({
                label: dataset.label || 'Dataset',
                data: Array.isArray(dataset.data)
                  ? dataset.data.map((point: any) => ({
                      x: point.x || 0,
                      y: point.y || 0,
                      r: point.r || 5,
                    }))
                  : [],
                backgroundColor:
                  dataset.backgroundColor || 'rgba(197, 170, 255, 0.7)',
                borderColor: dataset.borderColor || 'rgba(197, 170, 255, 1)',
                borderWidth: dataset.borderWidth || 1,
              }))
            : [
                {
                  label: chart.chart_name || 'Dataset',
                  data: Array.isArray(chart.values)
                    ? chart.values.map((point: any) => ({
                        x: point.x || 0,
                        y: point.y || 0,
                        r: point.r || 5,
                      }))
                    : [],
                  backgroundColor:
                    chart.backgroundColor || 'rgba(197, 170, 255, 0.7)',
                  borderColor: chart.borderColor || 'rgba(197, 170, 255, 1)',
                  borderWidth: chart.borderWidth || 1,
                },
              ],
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: true,
              position: 'top',
            },
            title: {
              display: true,
              text: chart.title || 'Bubble Chart',
            },
          },
          scales: {
            x: {
              type: 'linear',
              position: 'bottom',
              title: {
                display: true,
                text: 'X Axis',
              },
            },
            y: {
              type: 'linear',
              position: 'left',
              title: {
                display: true,
                text: 'Y Axis',
              },
            },
          },
        },
      };
    case 'polarArea':
      return {
        chartType: chart.chartType,
        id: chart.id || chart.chart_id,
        type: 'polarArea',
        position: chart.position || { x: 0, y: 0 },
        size: chart.size || { width: 400, height: 300 },
        data: {
          labels: chart.labels || [],
          datasets: Array.isArray(chart.datasets)
            ? chart.datasets.map((dataset: any) => ({
                label: dataset.label || 'Dataset',
                data: dataset.data || [],
                backgroundColor:
                  dataset.backgroundColor || 'rgba(197, 170, 255, 0.7)',
                borderColor: dataset.borderColor || 'rgba(197, 170, 255, 1)',
                borderWidth: dataset.borderWidth || 1,
                fill: dataset.fill || false,
              }))
            : [
                {
                  label: chart.chart_name || 'Dataset',
                  data: chart.values || [],
                  backgroundColor:
                    chart.backgroundColor || 'rgba(197, 170, 255, 0.7)',
                  borderColor: chart.borderColor || 'rgba(197, 170, 255, 1)',
                  borderWidth: chart.borderWidth || 1,
                  fill: chart.fill || false,
                },
              ],
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: true,
              position: 'top',
            },
            title: {
              display: true,
              text: chart.title || '',
            },
          },
        },
      };

    case 'scatter':
      return {
        chartType: chart.chartType,
        id: chart.id || chart.chart_id,
        type: 'scatter',
        position: chart.position || { x: 0, y: 0 },
        size: chart.size || { width: 400, height: 300 },
        data: {
          labels: chart.labels || [],
          datasets: Array.isArray(chart.datasets)
            ? chart.datasets.map((dataset: any) => ({
                label: dataset.label || 'Dataset',
                data: Array.isArray(dataset.data)
                  ? dataset.data.map((point: any) => ({
                      x: point.x || 0,
                      y: point.y || 0,
                    }))
                  : [],
                backgroundColor:
                  dataset.backgroundColor || 'rgba(197, 170, 255, 0.7)',
                borderColor: dataset.borderColor || 'rgba(197, 170, 255, 1)',
                borderWidth: dataset.borderWidth || 1,
                pointRadius: 5,
                pointHoverRadius: 8,
              }))
            : [
                {
                  label: chart.chart_name || 'Dataset',
                  data: Array.isArray(chart.values)
                    ? chart.values.map((point: any) => ({
                        x: point.x || 0,
                        y: point.y || 0,
                      }))
                    : [],
                  backgroundColor:
                    chart.backgroundColor || 'rgba(197, 170, 255, 0.7)',
                  borderColor: chart.borderColor || 'rgba(197, 170, 255, 1)',
                  borderWidth: chart.borderWidth || 1,
                  pointRadius: 5,
                  pointHoverRadius: 8,
                },
              ],
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: true,
              position: 'top',
            },
            title: {
              display: true,
              text: chart.title || 'Scatter Chart',
            },
          },
          scales: {
            x: {
              type: 'linear',
              position: 'bottom',
              title: {
                display: true,
                text: 'X Axis',
              },
            },
            y: {
              type: 'linear',
              position: 'left',
              title: {
                display: true,
                text: 'Y Axis',
              },
            },
          },
        },
      };

    case 'timeline':
      console.log('Processing timeline chart:', chart);
      const transformedData = {
        chartType: chart.chartType,
        id: chart.chart_id,
        type: 'line',
        position: chart.position || { x: 0, y: 0 },
        size: chart.size || { width: 400, height: 300 },
        data: {
          labels: chart.data?.labels || [],
          datasets: Array.isArray(chart.data?.datasets)
            ? chart.data.datasets.map((dataset: any) => {
                console.log('Processing dataset:', dataset);
                return {
                  label: dataset.label,
                  data: dataset.data.map((point: any) => {
                    console.log('Processing point:', point);
                    // Store dates as strings to avoid non-serializable Date objects
                    const xValue =
                      typeof point.x === 'string'
                        ? point.x
                        : typeof point.x === 'number'
                          ? new Date(point.x).toISOString().split('T')[0]
                          : new Date().toISOString().split('T')[0];

                    return {
                      x: xValue,
                      y: point.y,
                      duration: point.duration || 0,
                    };
                  }),
                  backgroundColor: dataset.backgroundColor,
                  borderColor: dataset.borderColor,
                  fill: dataset.fill,
                  borderWidth: dataset.borderWidth || 1,
                  borderDash: dataset.borderDash || [],
                  pointRadius: dataset.pointRadius || 4,
                  tension: dataset.tension || 0.4,
                  isTimeline: true,
                };
              })
            : [],
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          interaction: {
            mode: 'index',
            intersect: false,
          },
          scales: {
            x: {
              type: 'time',
              time: {
                unit: chart.options?.scales?.x?.time?.unit || 'day',
                displayFormats: {
                  day: 'MMM dd',
                },
                tooltipFormat: 'MMM dd',
              },
              title: {
                display: true,
                text: chart.options?.scales?.x?.title?.text || 'Date',
              },
              grid: {
                display: true,
              },
              ticks: {
                source: 'auto',
                autoSkip: true,
              },
            },
            y: {
              type: 'linear',
              display: true,
              beginAtZero: true,
              reverse: false,
              title: {
                display: true,
                text: chart.options?.scales?.y?.title?.text || 'Value',
              },
              grid: {
                display: true,
              },
              ticks: {
                precision: 0,
              },
            },
          },
          plugins: {
            legend: {
              display: chart.options?.plugins?.legend?.display ?? true,
              position: chart.options?.plugins?.legend?.position || 'top',
            },
            title: {
              display: chart.options?.plugins?.title?.display ?? true,
              text: chart.options?.plugins?.title?.text || chart.chart_name,
            },
            tooltip: {
              mode: 'index',
              intersect: false,
            },
          },
        },
      };
      console.log('Transformed timeline data:', transformedData);
      return transformedData;

    case 'burndown':
      console.log('Processing burndown chart:', chart);
      return {
        chartType: chart.chartType,
        id: chart.id || chart.chart_id,
        title: chart.title || 'Burndown Chart',
        type: 'line',
        position: chart.position || { x: 0, y: 0 },
        size: chart.size || { width: 400, height: 300 },
        data: {
          labels: chart.data?.labels || [],
          datasets: Array.isArray(chart.data?.datasets)
            ? chart.data.datasets.map((dataset: any) => ({
                label: dataset.label,
                data: Array.isArray(dataset.data)
                  ? dataset.data.map((point: any) => {
                      // Store dates as strings to avoid non-serializable Date objects
                      const xValue =
                        typeof point.x === 'string'
                          ? point.x
                          : typeof point.x === 'number'
                            ? new Date(point.x).toISOString().split('T')[0]
                            : new Date().toISOString().split('T')[0];

                      return {
                        x: xValue,
                        y: typeof point.y === 'number' ? point.y : 0,
                        duration: point.duration || 0,
                      };
                    })
                  : [],
                backgroundColor:
                  dataset.backgroundColor || 'rgba(54, 162, 235, 0.2)',
                borderColor: dataset.borderColor || 'rgba(54, 162, 235, 1)',
                fill: dataset.fill ?? dataset.label === 'Actual Burndown',
                borderWidth: dataset.borderWidth || 2,
                borderDash: Array.isArray(dataset.borderDash)
                  ? dataset.borderDash
                  : dataset.borderDash
                    ? dataset.borderDash
                    : [],
                pointRadius:
                  typeof dataset.pointRadius === 'number'
                    ? dataset.pointRadius
                    : 0,
                tension:
                  typeof dataset.tension === 'number'
                    ? dataset.tension
                    : dataset.label === 'Actual Burndown'
                      ? 0.4
                      : 0,
                isBurndown: true,
              }))
            : [],
          isBurndown: true,
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: chart.options?.plugins?.legend?.display ?? true,
              position: chart.options?.plugins?.legend?.position || 'top',
            },
            title: {
              display: chart.options?.plugins?.title?.display ?? true,
              text:
                chart.options?.plugins?.title?.text ||
                chart.title ||
                'Sprint Burndown Chart',
            },
            tooltip: {
              mode: 'index',
              intersect: false,
            },
          },
          scales: {
            x: {
              type: 'time',
              time: {
                unit: chart.options?.scales?.x?.time?.unit || 'day',
                displayFormats: {
                  day: 'MMM dd',
                },
              },
              title: {
                display: true,
                text: chart.options?.scales?.x?.title?.text || 'Sprint Days',
              },
            },
            y: {
              type: 'linear',
              display: true,
              beginAtZero: true,
              title: {
                display: true,
                text:
                  chart.options?.scales?.y?.title?.text ||
                  'Story Points Remaining',
              },
              min: 0,
              // Optionally, you can set max dynamically if needed
            },
          },
        },
      };

    case 'gantt':
      console.log('chart in gantt', chart);
      const tasks = chart.tasks || (chart.data && chart.data.tasks) || [];
      return {
        chartType: chart.chartType,
        id: chart.id || chart.chart_id,
        type: 'gantt',
        position: chart.position || { x: 0, y: 0 },
        size: chart.size || { width: 800, height: 400 },
        data: {
          tasks: Array.isArray(tasks)
            ? tasks.map((task: any) => ({
                id: task.id,
                name: task.name,
                start: task.start,
                end: task.end,
                progress: task.progress || 0,
                dependencies: task.dependencies || [],
                status: task.status || 'pending',
                project: task.project || null,
                type: task.type || 'task',
                hideChildren: task.hideChildren || false,
                displayOrder: task.displayOrder || 0,
              }))
            : [],
        },
        options: {
          ...(chart.options || {}),
          columnWidth: chart.options?.columnWidth || 60,
          barHeight: chart.options?.barHeight || 40,
          barCornerRadius: chart.options?.barCornerRadius || 3,
          fontSize: chart.options?.fontSize || '12px',
          showTaskList: chart.options?.showTaskList !== false,
          locale: chart.options?.locale || 'en-GB',
        },
      };

    case 'Line Chart':
    case 'line':
      Object.assign(dataset, {
        chartType: chart.chartType,
        type: 'line',
        fill: false,
        tension: 0.4,
        backgroundColor: 'rgba(197, 170, 255, 0.7)',
        borderColor: 'rgba(197, 170, 255, 1)',
        pointBackgroundColor: 'rgba(197, 170, 255, 1)',
        pointBorderColor: '#fff',
        pointHoverBackgroundColor: '#fff',
        pointHoverBorderColor: 'rgba(197, 170, 255, 1)',
        pointRadius: 4,
        borderWidth: 2,
      });
      break;

    case 'Area Chart':
    case 'area':
      Object.assign(dataset, {
        chartType: chart.chartType,
        type: 'line',
        fill: chart?.fill || true,
        tension: 0.4,
        backgroundColor: chart?.backgroundColor || 'rgba(197, 170, 255, 0.7)',
        borderColor: chart?.borderColor || 'rgba(197, 170, 255, 1)',
        pointBackgroundColor:
          chart?.pointBackgroundColor || 'rgba(197, 170, 255, 1)',
        pointBorderColor: chart?.pointBorderColor || '#fff',
        pointHoverBackgroundColor: '#fff',
        pointHoverBorderColor: chart?.borderColor || 'rgba(197, 170, 255, 1)',
        pointRadius: chart?.pointRadius || 4,
        borderWidth: chart?.borderWidth || 2,
      });
      break;

    case 'Pie Chart':
    case 'pie':
    case 'Doughnut Chart':
    case 'doughnut':
    case 'Polar Area Chart':
    case 'polar':
      Object.assign(dataset, {
        chartType: chart.chartType,
        backgroundColor: chart?.backgroundColor || 'rgba(197, 170, 255, 0.7)',
        borderColor: chart?.borderColor || 'rgba(197, 170, 255, 1)',
        borderWidth: chart?.borderWidth || 1,
        fill: chart?.fill || true,
        labels: chart.x_labels,
      });
      break;

    case 'Radar Chart':
    case 'radar':
      Object.assign(dataset, {
        chartType: chart.chartType,
        backgroundColor: chart?.backgroundColor || 'rgba(197, 170, 255, 0.3)',
        borderColor: chart?.borderColor || 'rgba(197, 170, 255, 1)',
        borderWidth: chart?.borderWidth || 1,
        pointBackgroundColor:
          chart?.pointBackgroundColor || 'rgba(197, 170, 255, 1)',
        pointBorderColor: chart?.pointBorderColor || '#fff',
        pointHoverBackgroundColor: '#fff',
        pointHoverBorderColor: chart?.borderColor || 'rgba(197, 170, 255, 1)',
      });
      break;

    default: // Bar Charts and others
      Object.assign(dataset, {
        chartType: chart.chartType,
        backgroundColor: chart?.backgroundColor || 'rgba(197, 170, 255, 0.7)',
        borderColor: chart?.borderColor || 'rgba(197, 170, 255, 1)',
        borderWidth: chart?.borderWidth || 1,
      });
  }

  const chartTypeMap: { [key: string]: string } = {
    'bar chart': 'bar',
    'horizontal bar chart': 'bar',
    'line chart': 'line',
    'area chart': 'line',
    'pie chart': 'pie',
    pie: 'pie',
    radar: 'radar',
    doughnut: 'doughnut',
    polar: 'polar',
    horizantalbar: 'bar',
    'doughnut chart': 'doughnut',
    'polar area chart': 'polarArea',
    'radar chart': 'radar',
    'bubble chart': 'bubble',
    'scatter chart': 'scatter',
    'timeline chart': 'line',
    text: 'text',
  };

  const chartType = chartTypeMap[chart.type_of_chart.toLowerCase()] || 'bar';

  // const chartType = chartTypeMap[chart.type_of_chart] || 'bar';
  const isHorizontal =
    chart.type_of_chart === 'Horizontal Bar Chart' ||
    chart.type_of_chart === 'horizantalbar';

  const baseConfig = {
    chartType: chart.chartType,
    id: chart.chart_id,
    type: chartType,
    position: chart.position || { x: 0, y: 0 },
    size: chart.size || { width: 400, height: 300 },
    data: {
      labels: chart.x_labels,
      datasets: [dataset],
    },
    options: {
      maintainAspectRatio: false,
      responsive: true,
      indexAxis: isHorizontal ? 'y' : 'x',
      plugins: {
        legend: {
          position: 'top' as const,
        },
        title: {
          display: true,
          text: chart.chart_name,
        },
      },
    },
  };

  // Add specific options for certain chart types

  // if (chart.type_of_chart === 'text') {
  // }

  return baseConfig;
};

export const transformChartDataForSketchbookArray = (charts: any[] = []) => {
  if (!Array.isArray(charts)) {
    return [];
  }

  return charts
    .map((chart) => transformChartDataForSketchbook(chart))
    .filter((chart) => chart !== null);
};

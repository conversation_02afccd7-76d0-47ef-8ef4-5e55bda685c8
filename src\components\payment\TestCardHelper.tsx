/**
 * Test Card Helper Component
 *
 * Displays test card numbers for easy testing when in development mode.
 * Only shows when using Stripe test keys.
 */

import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Chip,
  IconButton,
  Collapse,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Alert,
} from '@mui/material';
import {
  ExpandMore,
  ExpandLess,
  CreditCard,
  ContentCopy,
} from '@mui/icons-material';
import { STRIPE_PUBLISHABLE_KEY } from '../../services/config';

interface TestCard {
  number: string;
  description: string;
  expectedResult: string;
  type: 'success' | 'decline' | 'error';
}

const testCards: TestCard[] = [
  {
    number: '****************',
    description: 'Visa - Successful Payment',
    expectedResult: 'Payment succeeds',
    type: 'success',
  },
  {
    number: '****************',
    description: 'Visa - Generic Decline',
    expectedResult: 'Payment declined',
    type: 'decline',
  },
  {
    number: '****************',
    description: 'Visa - Insufficient Funds',
    expectedResult: 'Insufficient funds error',
    type: 'decline',
  },
  {
    number: '****************',
    description: 'Visa - Expired Card',
    expectedResult: 'Expired card error',
    type: 'error',
  },
  {
    number: '****************',
    description: 'Visa - Incorrect CVC',
    expectedResult: 'Incorrect CVC error',
    type: 'error',
  },
];

const TestCardHelper: React.FC = () => {
  const [expanded, setExpanded] = useState(false);

  // Only show in test mode
  const isTestMode = STRIPE_PUBLISHABLE_KEY?.startsWith('pk_test_');

  if (!isTestMode) {
    return null;
  }

  const handleCopyCard = (cardNumber: string) => {
    navigator.clipboard.writeText(cardNumber);
  };

  const getChipColor = (type: string) => {
    switch (type) {
      case 'success':
        return 'success';
      case 'decline':
        return 'warning';
      case 'error':
        return 'error';
      default:
        return 'default';
    }
  };

  return (
    <Paper
      elevation={1}
      sx={{
        p: 2,
        mb: 3,
        border: '1px solid',
        borderColor: 'info.main',
        backgroundColor: 'info.50',
      }}
    >
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <CreditCard color="info" />
          <Typography variant="h6" color="info.main">
            Test Mode - Test Cards Available
          </Typography>
          <Chip
            label="TEST MODE"
            color="info"
            size="small"
            variant="outlined"
          />
        </Box>
        <IconButton
          onClick={() => setExpanded(!expanded)}
          color="info"
          size="small"
        >
          {expanded ? <ExpandLess /> : <ExpandMore />}
        </IconButton>
      </Box>

      <Collapse in={expanded}>
        <Box sx={{ mt: 2 }}>
          <Alert severity="info" sx={{ mb: 2 }}>
            <Typography variant="body2">
              <strong>Test Mode Active:</strong> Use these test card numbers to
              safely test different payment scenarios. No real payments will be
              processed.
            </Typography>
          </Alert>

          <TableContainer>
            <Table size="small">
              <TableHead>
                <TableRow>
                  <TableCell>
                    <strong>Card Number</strong>
                  </TableCell>
                  <TableCell>
                    <strong>Description</strong>
                  </TableCell>
                  <TableCell>
                    <strong>Expected Result</strong>
                  </TableCell>
                  <TableCell>
                    <strong>Actions</strong>
                  </TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {testCards.map((card, index) => (
                  <TableRow key={index}>
                    <TableCell>
                      <Typography variant="body2" fontFamily="monospace">
                        {card.number}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {card.description}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={card.expectedResult}
                        color={getChipColor(card.type)}
                        size="small"
                        variant="outlined"
                      />
                    </TableCell>
                    <TableCell>
                      <IconButton
                        size="small"
                        onClick={() => handleCopyCard(card.number)}
                        title="Copy card number"
                      >
                        <ContentCopy fontSize="small" />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>

          <Box sx={{ mt: 2 }}>
            <Typography variant="body2" color="text.secondary">
              <strong>Additional Test Data:</strong>
            </Typography>
            <Typography variant="body2" color="text.secondary">
              • <strong>Expiry:</strong> Any future date (e.g., 12/25)
            </Typography>
            <Typography variant="body2" color="text.secondary">
              • <strong>CVC:</strong> Any 3 digits (e.g., 123)
            </Typography>
            <Typography variant="body2" color="text.secondary">
              • <strong>ZIP:</strong> Any 5 digits (e.g., 12345)
            </Typography>
          </Box>
        </Box>
      </Collapse>
    </Paper>
  );
};

export default TestCardHelper;

import React, { useState, forwardRef } from 'react';
import styles from './CustomInput.module.css';
import { FaEye, FaEyeSlash } from 'react-icons/fa';
import { useTheme } from '../../../hooks/useTheme';
import { useTheme as useThemeContext } from '../../../contexts/ThemeContext';

interface InputProps {
  label?: string | React.ReactNode;
  type?: 'email' | 'password' | 'text' | 'date';
  value?: string;
  placeholder?: string;
  onChange?: (event: React.ChangeEvent<HTMLInputElement>) => void;
  error?: boolean;
  helperText?: string;
  style?: React.CSSProperties;
  inputProps?: React.InputHTMLAttributes<HTMLInputElement>;
  disabled?: boolean;
  onKeyPress?: (e: React.KeyboardEvent) => void;
  required?: boolean;
  fullWidth?: boolean;
  className?: string;
  disableDarkTheme?: boolean;
}

const CustomInput = forwardRef<HTMLInputElement, InputProps>(
  (
    {
      label,
      type = 'text',
      value = '',
      placeholder = '',
      onChange,
      error = false,
      helperText,
      style,
      inputProps,
      disabled = false,
      onKeyPress,
      required = false,
      fullWidth = false,
      className = '',
      disableDarkTheme = false,
    },
    ref
  ) => {
    const theme = useTheme();
    const themeContext = useThemeContext();
    const [showPassword, setShowPassword] = useState<boolean>(false);

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      if (onChange) {
        onChange(e);
      }
    };

    const togglePasswordVisibility = () => {
      setShowPassword(!showPassword);
    };

    // Force light theme if disableDarkTheme is true
    const shouldUseDarkTheme = themeContext.isDarkMode && !disableDarkTheme;

    // Create a class name that prevents dark theme CSS from applying
    const containerClassName = `${styles.inputContainer} ${fullWidth ? styles.fullWidth : ''} ${className} ${disableDarkTheme ? 'force-light-theme' : ''}`;

    return (
      <div
        className={containerClassName}
        style={
          {
            '--primary-color': theme.colors.text.primary,
            '--secondary-color': theme.colors.primary.main,
            '--trinary-color': theme.colors.text.secondary,
            '--error-color': theme.colors.error.main,
            '--background-color': shouldUseDarkTheme ? '#2d2d2d' : '#ffffff',
            '--border-color': shouldUseDarkTheme ? '#424242' : '#e0e0e0',
            '--text-color': shouldUseDarkTheme ? '#ffffff' : '#0e2f51',
            '--placeholder-color': shouldUseDarkTheme ? '#b0bec5' : '#667085',
            ...style,
          } as React.CSSProperties
        }
        data-force-light-theme={disableDarkTheme ? 'true' : undefined}
      >
        {label && (
          <label className={styles.inputLabel} htmlFor={label as string}>
            {label}
            {required && <span className={styles.required}>*</span>}
          </label>
        )}
        <div className={styles.passwordContainer}>
          <input
            disabled={disabled}
            id={label as string}
            className={`${styles.input} ${error ? styles.inputError : ''}`}
            type={type === 'password' && showPassword ? 'text' : type}
            value={value}
            placeholder={placeholder}
            onChange={handleChange}
            ref={ref}
            required={required}
            {...inputProps}
            onKeyPress={onKeyPress}
          />
          {type === 'password' && (
            <span className={styles.eyeIcon} onClick={togglePasswordVisibility}>
              {showPassword ? <FaEyeSlash /> : <FaEye />}
            </span>
          )}
        </div>
        {helperText && (
          <span className={`${styles.helperText} ${error ? styles.error : ''}`}>
            {helperText}
          </span>
        )}
      </div>
    );
  }
);

export default CustomInput;

/* Force white background and black text for Toast UI Viewer regardless of theme */
.toast-viewer-wrapper {
  background-color: white !important;
}

.toast-viewer-wrapper .toastui-editor-contents {
  color: #000 !important;
  background-color: white !important;
}

.toast-viewer-wrapper .toastui-editor-contents h1,
.toast-viewer-wrapper .toastui-editor-contents h2,
.toast-viewer-wrapper .toastui-editor-contents h3,
.toast-viewer-wrapper .toastui-editor-contents h4,
.toast-viewer-wrapper .toastui-editor-contents h5,
.toast-viewer-wrapper .toastui-editor-contents h6 {
  color: #000 !important;
}

.toast-viewer-wrapper .toastui-editor-contents p,
.toast-viewer-wrapper .toastui-editor-contents li,
.toast-viewer-wrapper .toastui-editor-contents blockquote {
  color: #000 !important;
}

/* Ensure code blocks are readable */
.toast-viewer-wrapper .toastui-editor-contents pre,
.toast-viewer-wrapper .toastui-editor-contents code {
  background-color: #f0f0f0 !important;
  color: #000 !important;
}

/* Fix table styling */
.toast-viewer-wrapper .toastui-editor-contents th {
  background-color: #e0e0e0 !important;
  color: #000 !important;
}

.toast-viewer-wrapper .toastui-editor-contents td {
  color: #000 !important;
}

/* Override dark theme styles */
.toast-viewer-wrapper.dark-theme .toastui-editor-contents {
  color: #000 !important;
  background-color: white !important;
}

.toast-viewer-wrapper.dark-theme .toastui-editor-contents h1,
.toast-viewer-wrapper.dark-theme .toastui-editor-contents h2,
.toast-viewer-wrapper.dark-theme .toastui-editor-contents h3,
.toast-viewer-wrapper.dark-theme .toastui-editor-contents h4,
.toast-viewer-wrapper.dark-theme .toastui-editor-contents h5,
.toast-viewer-wrapper.dark-theme .toastui-editor-contents h6 {
  color: #000 !important;
}

.toast-viewer-wrapper.dark-theme .toastui-editor-contents p,
.toast-viewer-wrapper.dark-theme .toastui-editor-contents li,
.toast-viewer-wrapper.dark-theme .toastui-editor-contents blockquote {
  color: #000 !important;
}

/* KaTeX Math Styling - Override for dark mode visibility */
.toast-viewer-wrapper .katex,
.toast-viewer-wrapper .katex *,
.toast-viewer-wrapper .katex-display,
.toast-viewer-wrapper .katex-display *,
.toast-viewer-wrapper .katex-display-block .katex,
.toast-viewer-wrapper .katex-display-block .katex * {
  color: #000 !important;
}

/* Ensure KaTeX elements are visible in both light and dark modes */
.toast-viewer-wrapper.dark-theme .katex,
.toast-viewer-wrapper.dark-theme .katex *,
.toast-viewer-wrapper.dark-theme .katex-display,
.toast-viewer-wrapper.dark-theme .katex-display *,
.toast-viewer-wrapper.dark-theme .katex-display-block .katex,
.toast-viewer-wrapper.dark-theme .katex-display-block .katex * {
  color: #000 !important;
}

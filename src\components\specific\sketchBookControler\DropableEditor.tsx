import { useState, useCallback, useMemo, useEffect, useRef } from 'react';
import GridLayout from 'react-grid-layout';
import { Layout as LayoutType } from 'react-grid-layout';
import { debounce } from 'lodash';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';
import './DropableEditor.css';
import { TransformWrapper, TransformComponent } from 'react-zoom-pan-pinch';
import { toast } from 'react-hot-toast';
import { FaTimes, FaEllipsisV } from 'react-icons/fa';
import { Menu, MenuItem, Fab, Tooltip } from '@mui/material';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../../store/store';
import {
  updateCharts,
  updateLayout,
  updateFlowNodes,
  updateFlowEdges,
} from '../../../store/sketchbookSlice';
import { createSelector } from '@reduxjs/toolkit';
import { useTabletDetection } from '../../../hooks/useTabletDetection';
import { useGestures } from '../../../hooks/useGestures';
import { triggerHapticFeedback } from '../../../utils/touchUtils';
// import { Node, Edge } from 'reactflow';

// Chart imports
import {
  Bar,
  Line,
  Pie,
  Doughnut,
  Radar,
  PolarArea,
  Bubble,
  Scatter,
} from 'react-chartjs-2';
import GaugeChart from 'react-gauge-chart';
import 'chartjs-adapter-date-fns';
import { Chart as ChartJS } from 'chart.js';
import { TimeScale } from 'chart.js';

// Icons
import { FiZoomIn, FiZoomOut, FiMaximize2 } from 'react-icons/fi';
import { CgArrangeBack } from 'react-icons/cg';
import {
  MdContentCopy,
  MdOutlineGridOn,
  MdOutlineGridOff,
  MdAlignHorizontalCenter,
  MdAlignVerticalCenter,
} from 'react-icons/md';
import { IoIosUndo, IoIosRedo } from 'react-icons/io';
import { FaRegKeyboard } from 'react-icons/fa';
import { BsDistributeHorizontal, BsDistributeVertical } from 'react-icons/bs';

// Custom components
import TextArea from './components/TextArea';
import GanttChart from './components/GanttChart';
import ImageComponent from './components/ImageComponent';
import EditorControls from './components/EditorControls';

// Flow chart components
import ReactFlow, {
  Controls,
  Node,
  Edge,
  applyNodeChanges,
  applyEdgeChanges,
} from 'reactflow';
import 'reactflow/dist/style.css';

// Node types
import ProcessNode from './components/nodes/ProcessNode';
import DecisionNode from './components/nodes/DecisionNode';
import OrgNode from './components/nodes/OrgNode';
import StartNode from './components/nodes/StartNode';
import EndNode from './components/nodes/EndNode';
import DatabaseNode from './components/nodes/DatabaseNode';
import DocumentNode from './components/nodes/DocumentNode';
import CloudNode from './components/nodes/CloudNode';
import OperationNode from './components/nodes/OperationNode';

// Utilities
import { handleDropedCharts } from '../../../utils/handleDropedCharts';
import {
  useSaveCustomChartsMutation,
  useUpdateSketchbookLayoutMutation,
  useRemoveChartFromSketchbookMutation,
  useAddFlowNodeToSketchbookMutation,
  useAddFlowEdgeToSketchbookMutation,
  useMoveToAnotherPageMutation,
  useRemoveFlowNodeFromSketchbookMutation,
  useUpdateFlowNodePositionMutation,
  useRemoveFlowEdgeFromSketchbookMutation,
} from '../../../services/sketchbookServices';

// Define Layout type that includes the properties we use
type Layout = LayoutType & {
  i: string;
  x: number;
  y: number;
  w: number;
  h: number;
};

// Register the TimeScale with Chart.js
ChartJS.register(TimeScale);

type ChartType =
  | 'bar'
  | 'line'
  | 'pie'
  | 'doughnut'
  | 'radar'
  | 'polarArea'
  | 'bubble'
  | 'scatter'
  | 'area'
  | 'horizontal'
  | 'gauge'
  | 'timeline'
  | 'gantt'
  | 'burndown'
  | 'textarea'
  | 'image';

const ChartComponentMap: Record<ChartType, React.ComponentType<any>> = {
  bar: Bar,
  line: Line,
  pie: Pie,
  doughnut: Doughnut,
  radar: Radar,
  polarArea: PolarArea,
  bubble: Bubble,
  scatter: Scatter,
  area: Line,
  horizontal: Bar,
  gauge: (props) => {
    const options = {
      id: props.id || `gauge-${Date.now()}`,
      nrOfLevels: props.options?.nrOfLevels ?? 20,
      colors: props.options?.colors ?? ['#FF5F6D', '#FFC371'],
      arcWidth: props.options?.arcWidth ?? 0.3,
      arcPadding: props.options?.arcPadding ?? 0.05,
      cornerRadius: props.options?.cornerRadius ?? 6,
      needleColor: props.options?.needleColor ?? '#464A4F',
      textColor: props.options?.textColor ?? '#464A4F',
      animate: props.options?.animate ?? true,
      animDelay: props.options?.animDelay ?? 0,
      animateDuration: props.options?.animateDuration ?? 2000,
    };

    return (
      <div
        // className={styles.chartInnerWrapper}
        style={{
          width: '100%',
          height: '100%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          overflow: 'visible',
        }}
      >
        <GaugeChart {...options} percent={props.data?.percent ?? 0.5} />
        <div
          style={{
            position: 'absolute',
            bottom: '2px',
            color: options?.textColor || '#000000',
            fontSize: '0.5rem',
            fontWeight: '500',
            textAlign: 'center',
          }}
        >
          {props.data?.text || 'Your Text Here!'}
        </div>
      </div>
    );
  },
  timeline: (props) => {
    // Special handling for timeline chart
    // Ensure data is in the correct format for Line component
    const processedData = {
      labels: props.labels || props.data?.labels || [],
      datasets: Array.isArray(props.datasets)
        ? props.datasets.map((dataset: any) => ({
            ...dataset,
            data: Array.isArray(dataset.data)
              ? dataset.data.map((point: any) => {
                  // Parse the date string but return a formatted date string to avoid serialization issues
                  const date = new Date(point.x);
                  return {
                    x: date,
                    y: point.y,
                    duration: point.duration || 0,
                    // Store the original string format for serialization
                    _originalDateString: point.x,
                  };
                })
              : [],
          }))
        : Array.isArray(props.data?.datasets)
          ? props.data.datasets.map((dataset: any) => ({
              ...dataset,
              data: Array.isArray(dataset.data)
                ? dataset.data.map((point: any) => {
                    // Parse the date string but return a formatted date string to avoid serialization issues
                    const date = new Date(point.x);
                    return {
                      x: date,
                      y: point.y,
                      duration: point.duration || 0,
                      // Store the original string format for serialization
                      _originalDateString: point.x,
                    };
                  })
                : [],
            }))
          : [],
    };

    const options = {
      ...props.options,
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        x: {
          type: 'time',
          time: {
            unit: 'day',
            displayFormats: {
              day: 'MMM dd',
            },
          },
          title: {
            display: true,
            text: 'Date',
          },
        },
        y: {
          beginAtZero: true,
          title: {
            display: true,
            text: 'Value',
          },
        },
      },
    };

    return <Line data={processedData} options={options} />;
  },
  gantt: GanttChart,
  burndown: (props) => {
    // Special handling for burndown chart

    // Ensure data is in the correct format for Line component
    const processedData = {
      labels: props.labels || props.data?.labels || [],
      datasets: Array.isArray(props.datasets)
        ? props.datasets.map((dataset: any) => ({
            ...dataset,
            data: Array.isArray(dataset.data)
              ? dataset.data.map((point: any) => {
                  // Parse the date string but return a formatted date string to avoid serialization issues
                  const date = new Date(point.x);
                  return {
                    x: date,
                    y: point.y,
                    duration: point.duration || 0,
                    // Store the original string format for serialization
                    _originalDateString: point.x,
                  };
                })
              : [],
          }))
        : Array.isArray(props.data?.datasets)
          ? props.data.datasets.map((dataset: any) => ({
              ...dataset,
              data: Array.isArray(dataset.data)
                ? dataset.data.map((point: any) => {
                    // Parse the date string but return a formatted date string to avoid serialization issues
                    const date = new Date(point.x);
                    return {
                      x: date,
                      y: point.y,
                      duration: point.duration || 0,
                      // Store the original string format for serialization
                      _originalDateString: point.x,
                    };
                  })
                : [],
            }))
          : [],
    };

    const options = {
      ...props.options,
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        x: {
          type: 'time',
          time: {
            unit: 'day',
            displayFormats: {
              day: 'MMM dd',
            },
          },
          title: {
            display: true,
            text: 'Sprint Days',
          },
        },
        y: {
          beginAtZero: true,
          title: {
            display: true,
            text: 'Story Points Remaining',
          },
        },
      },
    };

    return <Line data={processedData} options={options} />;
  },
  textarea: TextArea,
  image: ImageComponent,
};

// Add node types mapping
const nodeTypes = {
  start: StartNode,
  end: EndNode,
  process: ProcessNode,
  decision: DecisionNode,
  database: DatabaseNode,
  document: DocumentNode,
  cloud: CloudNode,
  operation: OperationNode,
  organization: OrgNode,
};

const initialLayout = [
  { i: 'chart1', x: 0, y: 0, w: 3, h: 2 },
  { i: 'chart2', x: 3, y: 0, w: 3, h: 2 },
  { i: 'chart3', x: 0, y: 2, w: 6, h: 2 },
];

interface DropableEditorProps {
  sketchbookId: string;
  sketchbookData: any;
  chartData: {
    charts: any[]; // Replace 'any' with a more specific type if possible
  };
  activePage: string | number;
  pageSize: {
    value: string;
    label: string;
    width: number;
    height: number;
    orientation: 'portrait' | 'landscape';
  };
  onChartSelect: (chartId: string) => void;
  onMoveChart: (chartId: string, targetPageId: string) => void;
  pages: { id: string; name: string }[];
  dropableColor: string;
  // onUpdateThumbnail?: (sketchbookId: string) => void;
}

// Base selectors
const selectChartsState = (state: RootState) => state.sketchbook.charts;
const selectLayoutsState = (state: RootState) => state.sketchbook.layouts;
const selectPageEnabledState = (state: RootState) =>
  state.sketchbook.pageEnabled;
const selectFlowNodesState = (state: RootState) => state.sketchbook.flowNodes;
const selectFlowEdgesState = (state: RootState) => state.sketchbook.flowEdges;

// Memoized selectors with transformation logic
const makeSelectCharts = () =>
  createSelector(
    [selectChartsState, (_state: RootState, activePage: string) => activePage],
    (charts, activePage) => {
      const pageCharts = charts[activePage] || [];
      return pageCharts.map((chart: any) => ({
        ...chart,
        id: chart.id.toString(),
        processed: true,
      }));
    }
  );

const makeSelectLayout = () =>
  createSelector(
    [selectLayoutsState, (_state: RootState, activePage: string) => activePage],
    (layouts, activePage) => {
      const pageLayout = layouts[activePage] || initialLayout;
      return pageLayout.map((item: any) => ({
        ...item,
        processed: true,
        i: item.i.toString(),
      }));
    }
  );

const selectPageEnabled = createSelector(
  [selectPageEnabledState],
  (pageEnabled) => ({
    isEnabled: pageEnabled ?? true,
    lastUpdated: Date.now(),
  })
);

const selectFlowNodes = createSelector(
  [selectFlowNodesState],
  (flowNodes): Node[] =>
    flowNodes.map((node: any) => ({
      ...node,
      processed: true,
      position: node.position || { x: 0, y: 0 },
    }))
);

const selectFlowEdges = createSelector(
  [selectFlowEdgesState],
  (flowEdges): Edge[] =>
    flowEdges.map((edge: any) => ({
      ...edge,
      processed: true,
      id: edge.id.toString(),
    }))
);

const DropableEditor: React.FC<DropableEditorProps> = ({
  sketchbookId,
  activePage,
  pageSize,
  onChartSelect,
  onMoveChart,
  pages,
  dropableColor,
  // onUpdateThumbnail,
}) => {
  const dispatch = useDispatch();

  //API's
  const [saveCustomCharts] = useSaveCustomChartsMutation();
  const [updateSketchbookLayout] = useUpdateSketchbookLayoutMutation();
  const [removeChartFromSketchbook] = useRemoveChartFromSketchbookMutation();
  const [addFlowNodeToSketchbook] = useAddFlowNodeToSketchbookMutation();
  const [addFlowEdgeToSketchbook] = useAddFlowEdgeToSketchbookMutation();
  const [moveToAnotherPage] = useMoveToAnotherPageMutation();
  const [updateFlowNodePosition] = useUpdateFlowNodePositionMutation();
  const [removeFlowNodeFromSketchbook] =
    useRemoveFlowNodeFromSketchbookMutation();
  const [removeFlowEdgeFromSketchbook] =
    useRemoveFlowEdgeFromSketchbookMutation();

  // Create memoized selector instances
  const selectCharts = useMemo(() => makeSelectCharts(), []);
  const selectLayout = useMemo(() => makeSelectLayout(), []);

  // Use memoized selectors
  const charts = useSelector((state: RootState) =>
    selectCharts(state, activePage.toString())
  );
  const layout = useSelector((state: RootState) =>
    selectLayout(state, activePage.toString())
  );
  const { isEnabled: pageEnabled } = useSelector(selectPageEnabled);
  const flowNodes = useSelector(selectFlowNodes);
  const flowEdges = useSelector(selectFlowEdges);

  // Tablet detection and touch handling
  const {
    isTablet,
    isTouchDevice,
    isSamsungDevice,
    hasDragDropIssues,
    screenSize,
  } = useTabletDetection();

  // state
  const [isDraggingOrResizing, setIsDraggingOrResizing] = useState(false);
  const [selectedChart, setSelectedChart] = useState<string | null>(null);
  const [gridDensity, setGridDensity] = useState<number>(1); // Default grid density multiplier
  const [showGridLines, setShowGridLines] = useState<boolean>(true);
  const [showMinimap, setShowMinimap] = useState<boolean>(false);
  const [undoStack, setUndoStack] = useState<Layout[][]>([]);
  const [redoStack, setRedoStack] = useState<Layout[][]>([]);
  const [keyboardShortcutsModalOpen, setKeyboardShortcutsModalOpen] =
    useState<boolean>(false);
  const [zoom, setZoom] = useState(1);

  // Add a state variable to track the dynamic page height
  // Try to get the stored dynamic height from localStorage first
  const getStoredDynamicHeight = () => {
    if (sketchbookId && activePage) {
      const key = `dynamicHeight_${sketchbookId}_${activePage}`;
      const storedHeight = localStorage.getItem(key);
      if (storedHeight) {
        return parseFloat(storedHeight);
      }
    }
    return pageSize?.height || 0;
  };

  const [dynamicPageHeight, setDynamicPageHeight] = useState(
    getStoredDynamicHeight()
  );

  // Add ref to capture editor content for printing
  const printableContentRef = useRef<HTMLDivElement>(null);

  // Hide help tooltip after 5 seconds
  // useEffect(() => {
  //   if (showHelpTooltip) {
  //     const timer = setTimeout(() => {
  //       setShowHelpTooltip(false);
  //     }, 5000);

  //     return () => clearTimeout(timer);
  //   }
  // }, [showHelpTooltip]);

  // context menu state
  const [contextMenu, setContextMenu] = useState<{
    mouseX: number;
    mouseY: number;
    chartId: string;
    chartType: string;
  } | null>(null);

  // grid
  const baseGridCols = pageSize?.orientation === 'portrait' ? 12 : 20;
  const baseGridRows = pageSize?.orientation === 'portrait' ? 20 : 12;

  // Apply grid density to get actual grid dimensions
  const gridCols = Math.floor(baseGridCols * gridDensity);
  const gridRows = Math.floor(baseGridRows * gridDensity);

  // In the useEffect where we check changes to pageSize
  useEffect(() => {
    if (pageSize) {
      // Check if we have a stored height first
      const storedHeight = getStoredDynamicHeight();
      if (storedHeight > pageSize.height) {
        // If we have a stored height that's larger than the page size, use it
        setDynamicPageHeight(storedHeight);
      } else {
        // Otherwise use the page size height
        setDynamicPageHeight(pageSize.height);

        // Update localStorage with the new height
        if (sketchbookId && activePage) {
          const key = `dynamicHeight_${sketchbookId}_${activePage}`;
          localStorage.setItem(key, pageSize.height.toString());
        }
      }
    }
  }, [pageSize, sketchbookId, activePage]);

  // Touch gesture handlers
  const { gestureState, gestureHandlers } = useGestures({
    onPinchMove: (scale, deltaScale) => {
      if (isTablet && !isDraggingOrResizing) {
        setZoom(Math.max(0.1, Math.min(3, scale)));
      }
    },
    onDoubleTap: (x, y) => {
      if (isTablet && !isDraggingOrResizing) {
        triggerHapticFeedback('medium');
        // Reset zoom on double tap
        setZoom(1);
      }
    },
    onLongPress: (x, y) => {
      if (isTablet && !isDraggingOrResizing) {
        triggerHapticFeedback('heavy');
        // Find chart element at touch position and show context menu
        const element = document.elementFromPoint(x, y);
        const chartElement = element?.closest('[data-chart-id]');
        if (chartElement) {
          const chartId = chartElement.getAttribute('data-chart-id');
          if (chartId) {
            setContextMenu({
              mouseX: x,
              mouseY: y,
              chartId,
              chartType:
                charts.find((chart: any) => chart.id === chartId)?.chartType ||
                '',
            });
          }
        }
      }
    },
  });

  // handle drag start
  const onDragStart = useCallback(() => {
    setIsDraggingOrResizing(true);
    if (isTouchDevice) {
      triggerHapticFeedback('light');
    }
  }, [isTouchDevice]);

  // handle drag stop
  const onDragStop = useCallback(() => {
    setIsDraggingOrResizing(false);
    if (isTouchDevice) {
      triggerHapticFeedback('light');
    }
  }, [isTouchDevice]);

  // handle resize start
  const onResizeStart = useCallback(() => {
    setIsDraggingOrResizing(true);
    if (isTouchDevice) {
      triggerHapticFeedback('light');
    }
  }, [isTouchDevice]);

  // handle resize stop
  const onResizeStop = useCallback(() => {
    setIsDraggingOrResizing(false);
    if (isTouchDevice) {
      triggerHapticFeedback('light');
    }
  }, [isTouchDevice]);

  // handle layout change
  const handleLayoutChange = async (newLayout: Layout[]) => {
    if (!newLayout) return;

    // Only constrain the horizontal boundaries, allow vertical expansion
    const adjustedLayout = newLayout.map((item: Layout) => ({
      ...item,
      x: Math.max(0, Math.min(item.x, gridCols - item.w)),
      // Remove the vertical constraint to allow positioning beyond the initial grid
      // y: Math.max(0, Math.min(item.y, gridRows - item.h)),
      w: Math.min(item.w, gridCols),
      // Remove height constraint
      // h: Math.min(item.h, gridRows),
    }));

    // Calculate the new maximum bottom position to determine the dynamic height
    let maxBottom = 0;
    adjustedLayout.forEach((item) => {
      const bottom = (item.y + item.h) * (pageSize.height / gridRows);
      if (bottom > maxBottom) {
        maxBottom = bottom;
      }
    });

    // Add some padding to ensure there's space at the bottom
    const newPageHeight = Math.max(pageSize.height, maxBottom + 50);

    // Store the dynamic height in localStorage
    if (sketchbookId && activePage) {
      const key = `dynamicHeight_${sketchbookId}_${activePage}`;
      localStorage.setItem(key, newPageHeight.toString());
    }

    setDynamicPageHeight(newPageHeight);

    if (JSON.stringify(adjustedLayout) !== JSON.stringify(layout)) {
      dispatch(updateLayout({ pageId: activePage, layout: adjustedLayout }));

      if (isDraggingOrResizing) {
        setIsDraggingOrResizing(false);

        const cleanedLayouts = adjustedLayout.map(({ h, i, w, x, y }) => ({
          h,
          i,
          w,
          x,
          y,
        }));

        const payload = {
          pageIndex: Number(activePage) - 1,
          layouts: cleanedLayouts,
          pageId: activePage, // Ensure we're sending the correct page ID
          dynamicHeight: dynamicPageHeight, // Include the dynamic height
        };

        try {
          const response: any = await updateSketchbookLayout({
            id: sketchbookId,
            ...payload,
          }).unwrap();

          if (!response || response.status !== 200) {
            console.error('Layout update failed:', response);
            toast.error('Layout may not be saved properly');
          }
        } catch (layoutError) {
          console.error('Error updating layout:', layoutError);
          toast.error('Failed to save layout');
        }
      }
    }
  };

  // handle resize
  const debouncedHandleResize = useMemo(
    () =>
      debounce((layout: Layout[], oldItem: Layout, newItem: Layout) => {
        const updatedCharts = charts.map((chart: any) =>
          chart.id === newItem.i
            ? {
                ...chart,
                width: newItem.w * (pageSize.width / gridCols) - 10,
                height: newItem.h * (pageSize.height / gridRows) - 10,
              }
            : chart
        );
        dispatch(updateCharts({ pageId: activePage, charts: updatedCharts }));

        // Calculate the new maximum bottom position
        let maxBottom = 0;
        layout.forEach((item) => {
          const bottom = (item.y + item.h) * (pageSize.height / gridRows);
          if (bottom > maxBottom) {
            maxBottom = bottom;
          }
        });

        // Update the dynamic page height with some padding
        const newPageHeight = Math.max(pageSize.height, maxBottom + 50);

        // Store the dynamic height in localStorage
        if (sketchbookId && activePage) {
          const key = `dynamicHeight_${sketchbookId}_${activePage}`;
          localStorage.setItem(key, newPageHeight.toString());
        }

        setDynamicPageHeight(newPageHeight);
      }, 100),
    [pageSize, dispatch, activePage, charts, gridCols, gridRows, sketchbookId]
  );

  // handle resize
  const handleResize = useCallback(
    (layout: Layout[], oldItem: Layout, newItem: Layout) => {
      debouncedHandleResize(layout, oldItem, newItem);
    },
    [debouncedHandleResize]
  );

  // handle node change
  const handleNodeChange = useCallback(
    (nodeId: string, newValue: string) => {
      const updatedNodes = flowNodes.map((node: any) => {
        if (node.id === nodeId) {
          return {
            ...node,
            data: {
              ...node.data,
              label: newValue,
            },
          };
        }
        return node;
      });

      dispatch(updateFlowNodes(updatedNodes));
    },
    [flowNodes, dispatch]
  );

  // handle org node change
  const handleOrgNodeChange = useCallback(
    (nodeId: string, field: string, value: string) => {
      const updatedNodes = flowNodes.map((node: any) => {
        if (node.id === nodeId) {
          return {
            ...node,
            data: {
              ...node.data,
              [field]: value,
              onOrgNodeChange: handleOrgNodeChange,
            },
          };
        }
        return node;
      });
      dispatch(updateFlowNodes(updatedNodes));
    },
    [flowNodes, dispatch]
  );

  // handle node position
  const debouncedUpdateNodePosition = useMemo(
    () =>
      debounce(
        async (nodeIndex: number, position: { x: number; y: number }) => {
          try {
            const payload = {
              nodeIndex,
              position,
            };

            const response = await updateFlowNodePosition({
              id: sketchbookId,
              payload,
            }).unwrap();

            if (response.success) {
              console.log(
                `Position of node at index ${nodeIndex} updated successfully`
              );
            } else {
              console.error(
                `Failed to update position for node at index ${nodeIndex}`
              );
            }
          } catch (error) {
            console.error('Error updating node position:', error);
          }
        },
        500
      ), // Adjust the delay (in milliseconds) as needed
    [updateFlowNodePosition, sketchbookId]
  );

  // handle node change
  const onNodesChange = useCallback(
    async (changes: any) => {
      const updatedNodes = applyNodeChanges(changes, flowNodes);
      dispatch(updateFlowNodes(updatedNodes));

      // Extract nodes whose positions have changed
      const positionChanges = changes
        .filter(
          (change: any) =>
            change.type === 'position' || change.type === 'positionChange'
        )
        .map((change: any) => {
          const nodeIndex = flowNodes.findIndex(
            (node: Node) => node.id === change.id
          );
          const updatedNode = updatedNodes.find(
            (node: Node) => node.id === change.id
          );
          return updatedNode && nodeIndex !== -1
            ? {
                id: change.id,
                position: updatedNode.position,
                index: nodeIndex,
              }
            : null;
        })
        .filter(Boolean);

      // update positions using debounced function
      positionChanges.forEach((node: any) => {
        debouncedUpdateNodePosition(node.index, node.position);
      });
    },
    [flowNodes, dispatch, debouncedUpdateNodePosition]
  );

  // clean up the debounced function when component unmounts
  useEffect(() => {
    return () => {
      debouncedUpdateNodePosition.cancel();
    };
  }, [debouncedUpdateNodePosition]);

  // handle edge change
  const onEdgesChange = useCallback(
    (changes: any) => {
      const updatedEdges = applyEdgeChanges(changes, flowEdges);
      dispatch(updateFlowEdges(updatedEdges));
    },
    [flowEdges, dispatch]
  );

  // handle edge connect
  const onConnect = useCallback(
    async (params: any) => {
      const newEdge: Edge = {
        id: `edge-${Date.now()}`,
        source: params.source,
        target: params.target,
        sourceHandle: params.sourceHandle,
        targetHandle: params.targetHandle,
        type: 'default',
      };

      const payload = {
        id: newEdge.id,
        source: newEdge.source,
        target: newEdge.target,
        sourceHandle: newEdge.sourceHandle,
        targetHandle: newEdge.targetHandle,
        type: newEdge.type,
      };

      // Dispatch locally to update Redux state
      dispatch(updateFlowEdges([...flowEdges, newEdge]));

      // Call the API to persist the edge
      const response = await addFlowEdgeToSketchbook({
        id: sketchbookId,
        payload,
      }).unwrap();

      if (response.success) {
        toast.success('Edge added successfully');
      } else {
        toast.error('Failed to add edge');
      }
    },
    [flowEdges, dispatch, sketchbookId, addFlowEdgeToSketchbook]
  );

  // handle drop
  const handleDrop = useCallback(
    async (e: React.DragEvent<HTMLDivElement>) => {
      e.preventDefault();
      e.stopPropagation();

      try {
        // Enhanced data retrieval for better Samsung tablet compatibility
        let droppedData;

        // Try multiple data transfer formats for better compatibility
        const jsonData = e.dataTransfer.getData('application/json');
        const textData = e.dataTransfer.getData('text/plain');
        const htmlData = e.dataTransfer.getData('text/html');

        console.log('Drop event data:', {
          jsonData,
          textData,
          htmlData,
          types: e.dataTransfer.types,
        });

        if (jsonData) {
          droppedData = JSON.parse(jsonData);
        } else if (textData) {
          try {
            droppedData = JSON.parse(textData);
          } catch {
            // If text data is not JSON, create a fallback object
            droppedData = { type: textData, fallback: true };
          }
        } else {
          // Check for any available data types
          const availableTypes = Array.from(e.dataTransfer.types);
          console.log('Available data types:', availableTypes);

          for (const type of availableTypes) {
            const data = e.dataTransfer.getData(type);
            if (data) {
              try {
                droppedData = JSON.parse(data);
                break;
              } catch {
                console.log(`Failed to parse data from type: ${type}`);
              }
            }
          }
        }

        if (!droppedData) {
          const errorMessage = isSamsungDevice
            ? 'Samsung tablet detected: Try holding the chart element longer before dragging, or try using a different browser like Chrome or Firefox.'
            : hasDragDropIssues
              ? 'Android device detected: Drag and drop may have compatibility issues. Try using Chrome browser or refreshing the page.'
              : 'Invalid dropped data - no data could be retrieved';
          throw new Error(errorMessage);
        }

        if (droppedData.isFlowElement) {
          const rect = e.currentTarget.getBoundingClientRect();
          const position = {
            x: e.clientX - rect.left,
            y: e.clientY - rect.top,
          };

          const newNodeId = `${droppedData.type}-${Date.now()}`;
          const newNode: Node = {
            id: newNodeId,
            type: droppedData.type,
            position,
            data: {
              ...droppedData.defaultData,
              label: '',
              nodeId: newNodeId,
            },
            draggable: true,
          };

          const payload = {
            id: newNode.id,
            type: newNode.type,
            position: newNode.position,
            data: {
              ...newNode.data,
            },
            draggable: newNode.draggable,
          };
          dispatch(updateFlowNodes([...flowNodes, newNode]));

          const response = await addFlowNodeToSketchbook({
            id: sketchbookId,
            payload,
          }).unwrap();

          if (response.success) {
            toast.success('Node added successfully');
          } else {
            toast.error('Failed to add node');
          }
        } else {
          const result: any = handleDropedCharts({
            sketchbookId,
            droppedData,
            event: e,
            pageSize,
            gridCols,
            gridRows,
            activePage,
            charts,
            layout,
          });
          if (!result.success) {
            toast.error(result.error || 'Failed to add chart');
          } else {
            const chartType = droppedData.type;
            const saveResponse: any = await saveCustomCharts({
              payload: { ...result.payload, type: chartType },
              chartType,
            }).unwrap();
            if (saveResponse.success) {
              const payload = {
                pageIndex: Number(activePage) - 1,
                layouts: result.payload.layouts,
                pageId: activePage, // Ensure we're sending the correct page ID
              };
              // overwriting the local id with the id returned in the save chart api.
              payload.layouts[payload?.layouts?.length - 1] = {
                ...payload.layouts[payload?.layouts?.length - 1],
                i: saveResponse.data.id,
              };

              result.chartData[result?.chartData?.length - 1] = {
                ...result?.chartData[result?.chartData?.length - 1],
                id: saveResponse.data.id,
              };

              dispatch(
                updateCharts({
                  pageId: activePage,
                  charts: result.chartData,
                })
              );

              dispatch(
                updateLayout({ pageId: activePage, layout: payload.layouts })
              );
              try {
                // Include the dynamic height in the payload
                const updatedPayload = {
                  ...payload,
                  dynamicHeight: dynamicPageHeight,
                };

                const layoutResponse = await updateSketchbookLayout({
                  id: sketchbookId,
                  ...updatedPayload,
                }).unwrap();

                if (!layoutResponse || layoutResponse.status !== 200) {
                  toast.error('Chart added but layout may not be saved');
                }
              } catch (layoutError) {
                toast.error('Chart added but layout may not be saved');
              }
              toast.success('Chart added successfully');
            }
          }
        }
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : 'Failed to add item';

        if (isSamsungDevice || hasDragDropIssues) {
          toast.error(errorMessage, {
            duration: 6000,
            style: {
              maxWidth: '400px',
            },
          });
        } else {
          toast.error('Failed to add item');
        }
      }
    },
    [
      flowNodes,
      charts,
      layout,
      dispatch,
      activePage,
      handleNodeChange,
      handleOrgNodeChange,
      pageSize,
      gridCols,
      gridRows,
      saveCustomCharts,
    ]
  );

  // handle panning stop
  const handlePanningStop = useCallback(
    (e: React.MouseEvent<HTMLDivElement>) => {
      const target = e.target as HTMLElement;
      if (target.closest('.chart-container')) {
        e.stopPropagation();
      }
    },
    []
  );

  // handle chart click
  const handleChartClick = (chartId: string) => {
    setSelectedChart(chartId === selectedChart ? null : chartId);
    onChartSelect(chartId);
  };

  // handle chart removal
  const handleRemoveChart = async (chartId: string, chart: any) => {
    const payload = {
      pageIndex: Number(activePage) - 1,
      type: chart.chartType,
      id: chart.id,
    };

    const removeResponse: any = await removeChartFromSketchbook({
      payload,
      id: sketchbookId,
    }).unwrap();

    if (removeResponse.status === 200) {
      const payload = {
        pageIndex: Number(activePage) - 1,
        layouts: layout.filter((item: Layout) => item.i !== chartId),
      };

      // Include the dynamic height in the payload
      const updatedPayload = {
        ...payload,
        dynamicHeight: dynamicPageHeight,
      };

      const response: any = await updateSketchbookLayout({
        id: sketchbookId,
        ...updatedPayload,
      }).unwrap();
      toast.success('Chart removed successfully');
    }

    dispatch(
      updateCharts({
        pageId: activePage,
        charts: charts.filter((chart: any) => chart.id !== chartId),
      })
    );
    dispatch(
      updateLayout({
        pageId: activePage,
        layout: layout.filter((item: Layout) => item.i !== chartId),
      })
    );
    setSelectedChart(null);
  };

  // handle context menu
  const handleContextMenu = (event: React.MouseEvent, chartId: string) => {
    event.preventDefault();
    setContextMenu(
      contextMenu === null
        ? {
            mouseX: event.clientX - 2,
            mouseY: event.clientY - 4,
            chartId,
            chartType: charts.find((chart: any) => chart.id === chartId)
              ?.chartType,
          }
        : null
    );
  };

  // handle context menu close
  const handleClose = () => {
    setContextMenu(null);
  };

  // move chart to another page
  const handleMove = async (targetPageId: string) => {
    // Check if pages is valid
    if (!pages || pages.length === 0) {
      toast.error('No pages available to move chart to');
      handleClose();
      return;
    }

    try {
      const payload = {
        pageIndex: Number(activePage) - 1,
        moveToPageIndex: Number(targetPageId) - 1,
        type: contextMenu?.chartType,
        id: contextMenu?.chartId,
      };

      toast.loading('Moving chart...', { id: 'move-chart' });

      const response: any = await moveToAnotherPage({
        id: sketchbookId,
        payload,
      }).unwrap();

      if (response.status === 200) {
        toast.dismiss('move-chart');
        toast.success('Chart moved successfully');
        if (contextMenu) {
          onMoveChart(contextMenu.chartId, targetPageId);
          handleClose();
        }
      } else {
        toast.dismiss('move-chart');
        toast.error('Failed to move chart');
        handleClose();
      }
    } catch (error) {
      toast.dismiss('move-chart');
      toast.error('Failed to move chart');
      handleClose();
    }
  };

  // memoized charts
  const memoizedCharts = useMemo(() => {
    return charts.map((chart: any) => {
      const ChartComponent = ChartComponentMap[chart.type as ChartType] || Bar;
      const layoutItem = layout.find((l: Layout) => l.i === chart.id);

      if (!layoutItem) return null;

      // Ensure all datasets are valid
      const isValidData =
        chart.type === 'bar' ||
        chart.type === 'gauge' ||
        chart.type === 'gantt' ||
        chart.type === 'textarea' ||
        chart.type === 'image' ||
        chart.type === 'timeline' ||
        chart.type === 'burndown' ||
        (chart.data &&
          Array.isArray(chart.data.datasets) &&
          chart.data.datasets.every(
            (dataset: any) =>
              Array.isArray(dataset.data) && dataset.data.length > 0
          ));

      if (!isValidData) {
        return null;
      }

      return (
        <div
          key={chart.id}
          id={`chart-${chart.id}`}
          data-grid={layoutItem}
          data-chart-id={chart.id}
          data-chart-type={chart.type}
          className={`chart-container ${selectedChart === chart.id ? 'selected' : ''}`}
          onClick={() => handleChartClick(chart.id)}
          onContextMenu={(e) => handleContextMenu(e, chart.id)}
        >
          <div className="drag-handle">
            <span className="drag-icon">⋮</span>
          </div>
          {selectedChart === chart.id && (
            <>
              <button
                className="remove-chart"
                onClick={(e) => {
                  e.stopPropagation();
                  handleRemoveChart(chart.id, chart);
                }}
              >
                <FaTimes />
              </button>
              {isTouchDevice && (
                <button
                  className="context-menu-button"
                  onClick={(e) => {
                    e.stopPropagation();
                    triggerHapticFeedback('medium');
                    setContextMenu({
                      mouseX: e.currentTarget.getBoundingClientRect().left,
                      mouseY:
                        e.currentTarget.getBoundingClientRect().bottom + 10,
                      chartId: chart.id,
                      chartType: chart.type,
                    });
                  }}
                >
                  <FaEllipsisV />
                </button>
              )}
            </>
          )}
          <div className="chart-wrapper">
            {chart.type === 'image' ? (
              <ImageComponent
                data={{
                  src: chart.data.src,
                  alt: chart.data.alt || 'Image',
                }}
              />
            ) : (
              <ChartComponent
                key={`${chart.id}-${JSON.stringify(chart.data)}`}
                data={chart.data}
                options={{
                  ...chart.options,
                  responsive: true,
                  maintainAspectRatio: false,
                }}
              />
            )}
          </div>
        </div>
      );
    });
  }, [
    charts,
    layout,
    selectedChart,
    handleChartClick,
    handleContextMenu,
    handleRemoveChart,
  ]);

  useEffect(() => {
    // console.log('Charts updated:', charts);
  }, [charts]);

  // Add this function to calculate the appropriate initial scale based on page size
  const calculateInitialScale = useMemo(() => {
    const containerWidth = window.innerWidth * 0.8; // Assuming the container takes 80% of viewport width
    const containerHeight = window.innerHeight * 0.77; // Based on your 77vh setting

    const scaleX = containerWidth / pageSize?.width;
    const scaleY = containerHeight / pageSize?.height;

    // Use the smaller scale to ensure the entire page fits
    let scale = Math.min(scaleX, scaleY);

    // For A0, we might want to scale down a bit more initially
    if (pageSize?.width >= 2000) {
      scale = Math.min(scale, 0.15);
    }

    return scale;
  }, [pageSize?.width, pageSize?.height]);

  // Update node/edge deletion handlers
  const onNodesDelete = useCallback(
    async (nodesToDelete: Node[]) => {
      const updatedNodes = flowNodes.filter(
        (node: Node) => !nodesToDelete.find((n) => n.id === node.id)
      );
      dispatch(updateFlowNodes(updatedNodes));

      // Also remove any edges connected to these nodes
      const updatedEdges = flowEdges.filter(
        (edge: Edge) =>
          !nodesToDelete.find(
            (n) => n.id === edge.source || n.id === edge.target
          )
      );
      try {
        const response: any = await removeFlowNodeFromSketchbook({
          id: sketchbookId,
          index: nodesToDelete
            .map((nodeToDelete) =>
              flowNodes.findIndex((node: Node) => node.id === nodeToDelete.id)
            )
            .filter((index: number) => index !== -1),
        }).unwrap();
      } catch (error) {
        // Error removing flow node
      }
      dispatch(updateFlowEdges(updatedEdges));
    },
    [flowNodes, flowEdges, dispatch]
  );

  // update edge deletion handler
  const onEdgesDelete = useCallback(
    async (edgesToDelete: Edge[]) => {
      const updatedEdges = flowEdges.filter(
        (edge: Edge) => !edgesToDelete.find((e) => e.id === edge.id)
      );
      try {
        const response: any = await removeFlowEdgeFromSketchbook({
          id: sketchbookId,
          index: edgesToDelete
            .map((edgeToDelete) =>
              flowEdges.findIndex((edge: Edge) => edge.id === edgeToDelete.id)
            )
            .filter((index: number) => index !== -1),
        }).unwrap();
      } catch (error) {
        // Error removing flow edge
      }
      dispatch(updateFlowEdges(updatedEdges));
    },
    [flowEdges, dispatch]
  );

  // Update keyboard delete handler
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Delete') {
        const selectedNodes = flowNodes.filter((node: Node) => node.selected);
        const selectedEdges = flowEdges.filter((edge: Edge) => edge.selected);

        if (selectedNodes.length > 0) onNodesDelete(selectedNodes);
        if (selectedEdges.length > 0) onEdgesDelete(selectedEdges);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [flowNodes, flowEdges, onNodesDelete, onEdgesDelete]);

  // Save layout state for undo/redo
  const saveLayoutState = useCallback(() => {
    setUndoStack((prev) => [...prev, layout]);
    setRedoStack([]);
  }, [layout]);

  // Handle undo operation
  const handleUndo = useCallback(async () => {
    if (undoStack.length > 0) {
      const prevLayout = undoStack[undoStack.length - 1];
      setRedoStack((prev) => [...prev, layout]);
      setUndoStack((prev) => prev.slice(0, -1));
      dispatch(updateLayout({ pageId: activePage, layout: prevLayout }));

      // Save to backend
      try {
        const response = await updateSketchbookLayout({
          id: sketchbookId,
          pageIndex: Number(activePage) - 1,
          layouts: prevLayout,
          pageId: activePage, // Ensure we're sending the correct page ID
          dynamicHeight: dynamicPageHeight, // Include the dynamic height
        }).unwrap();

        if (!response || response.status !== 200) {
          console.error('Layout update failed:', response);
          toast.error('Layout may not be saved properly');
        }
      } catch (layoutError) {
        console.error('Error updating layout:', layoutError);
        toast.error('Failed to save layout');
      }
    }
  }, [
    undoStack,
    redoStack,
    layout,
    dispatch,
    activePage,
    sketchbookId,
    updateSketchbookLayout,
  ]);

  // Handle redo operation
  const handleRedo = useCallback(async () => {
    if (redoStack.length > 0) {
      const nextLayout = redoStack[redoStack.length - 1];
      setUndoStack((prev) => [...prev, layout]);
      setRedoStack((prev) => prev.slice(0, -1));
      dispatch(updateLayout({ pageId: activePage, layout: nextLayout }));

      // Save to backend
      try {
        const response = await updateSketchbookLayout({
          id: sketchbookId,
          pageIndex: Number(activePage) - 1,
          layouts: nextLayout,
          pageId: activePage, // Ensure we're sending the correct page ID
          dynamicHeight: dynamicPageHeight, // Include the dynamic height
        }).unwrap();

        if (!response || response.status !== 200) {
          console.error('Layout update failed:', response);
          toast.error('Layout may not be saved properly');
        }
      } catch (layoutError) {
        console.error('Error updating layout:', layoutError);
        toast.error('Failed to save layout');
      }
    }
  }, [
    undoStack,
    redoStack,
    layout,
    dispatch,
    activePage,
    sketchbookId,
    updateSketchbookLayout,
  ]);

  // Duplicate chart function
  const handleDuplicateChart = useCallback(
    async (chartId: string) => {
      // Save current layout for undo
      saveLayoutState();

      const chartToDuplicate = charts.find(
        (chart: any) => chart.id === chartId
      );
      if (!chartToDuplicate) {
        toast.error('Chart not found');
        return;
      }

      const layoutToDuplicate = layout.find(
        (item: Layout) => item.i === chartId
      );
      if (!layoutToDuplicate) {
        toast.error('Layout not found');
        return;
      }

      const newChartId = `${chartToDuplicate.type}-${Date.now()}`;

      // Create a deep clone of the chart (avoid reference issues)
      const newChart = JSON.parse(JSON.stringify(chartToDuplicate));
      newChart.id = newChartId;

      // Create a new layout position (offset slightly)
      const newLayoutItem = {
        ...layoutToDuplicate,
        i: newChartId,
        x: Math.min(layoutToDuplicate.x + 2, gridCols - layoutToDuplicate.w),
        y: Math.min(layoutToDuplicate.y + 2, gridRows - layoutToDuplicate.h),
      };

      // Update the layout and charts in Redux
      const newLayouts = [...layout, newLayoutItem];
      const newCharts = [...charts, newChart];

      dispatch(updateLayout({ pageId: activePage, layout: newLayouts }));
      dispatch(updateCharts({ pageId: activePage, charts: newCharts }));

      try {
        // Save the new chart to backend
        const saveResponse: any = await saveCustomCharts({
          payload: {
            pageIndex: Number(activePage) - 1,
            layouts: [newLayoutItem],
            chartData: newChart,
            type: chartToDuplicate.type,
          },
          chartType: chartToDuplicate.type,
        }).unwrap();

        if (saveResponse.success) {
          // Update the layout with the new ID from backend
          const updatedLayouts = newLayouts.map((item) =>
            item.i === newChartId ? { ...item, i: saveResponse.data.id } : item
          );

          // Update the charts with the new ID from backend
          const updatedCharts = newCharts.map((chart) =>
            chart.id === newChartId
              ? { ...chart, id: saveResponse.data.id }
              : chart
          );

          dispatch(
            updateLayout({ pageId: activePage, layout: updatedLayouts })
          );
          dispatch(updateCharts({ pageId: activePage, charts: updatedCharts }));

          await updateSketchbookLayout({
            id: sketchbookId,
            pageIndex: Number(activePage) - 1,
            layouts: updatedLayouts,
            dynamicHeight: dynamicPageHeight, // Include the dynamic height
          }).unwrap();

          toast.success('Chart duplicated successfully');
        }
      } catch (error) {
        console.error('Error duplicating chart:', error);
        toast.error('Failed to duplicate chart');
      }
    },
    [
      charts,
      layout,
      gridCols,
      gridRows,
      activePage,
      dispatch,
      saveCustomCharts,
      updateSketchbookLayout,
      sketchbookId,
      saveLayoutState,
    ]
  );

  // Align charts horizontally and vertically
  const alignCharts = useCallback(
    async (
      direction: 'left' | 'center' | 'right' | 'top' | 'middle' | 'bottom'
    ) => {
      // Save current layout for undo
      saveLayoutState();

      if (layout.length <= 1) {
        toast.error('Need at least two charts to align');
        return;
      }

      const selectedCharts = selectedChart
        ? layout.filter((item: Layout) => item.i === selectedChart)
        : layout;

      if (selectedCharts.length === 0) {
        toast.error('Please select at least one chart');
        return;
      }

      let targetValue: number;

      const newLayout = [...layout];

      switch (direction) {
        case 'left':
          targetValue = Math.min(
            ...selectedCharts.map((item: Layout) => item.x)
          );
          newLayout.forEach((item: Layout) => {
            if (selectedChart ? item.i === selectedChart : true) {
              item.x = targetValue;
            }
          });
          break;

        case 'center':
          // Find average center position
          targetValue = Math.round(
            selectedCharts.reduce(
              (sum: number, item: Layout) => sum + item.x + item.w / 2,
              0
            ) / selectedCharts.length
          );
          newLayout.forEach((item: Layout) => {
            if (selectedChart ? item.i === selectedChart : true) {
              item.x = Math.max(
                0,
                Math.min(gridCols - item.w, targetValue - item.w / 2)
              );
            }
          });
          break;

        case 'right':
          targetValue = Math.max(
            ...selectedCharts.map((item: Layout) => item.x + item.w)
          );
          newLayout.forEach((item: Layout) => {
            if (selectedChart ? item.i === selectedChart : true) {
              item.x = Math.max(0, targetValue - item.w);
            }
          });
          break;

        case 'top':
          targetValue = Math.min(
            ...selectedCharts.map((item: Layout) => item.y)
          );
          newLayout.forEach((item: Layout) => {
            if (selectedChart ? item.i === selectedChart : true) {
              item.y = targetValue;
            }
          });
          break;

        case 'middle':
          // Find average middle position
          targetValue = Math.round(
            selectedCharts.reduce(
              (sum: number, item: Layout) => sum + item.y + item.h / 2,
              0
            ) / selectedCharts.length
          );
          newLayout.forEach((item: Layout) => {
            if (selectedChart ? item.i === selectedChart : true) {
              item.y = Math.max(
                0,
                Math.min(gridRows - item.h, targetValue - item.h / 2)
              );
            }
          });
          break;

        case 'bottom':
          targetValue = Math.max(
            ...selectedCharts.map((item: Layout) => item.y + item.h)
          );
          newLayout.forEach((item: Layout) => {
            if (selectedChart ? item.i === selectedChart : true) {
              item.y = Math.max(0, targetValue - item.h);
            }
          });
          break;
      }

      dispatch(updateLayout({ pageId: activePage, layout: newLayout }));
      try {
        const response = await updateSketchbookLayout({
          id: sketchbookId,
          pageIndex: Number(activePage) - 1,
          layouts: newLayout,
          pageId: activePage, // Ensure we're sending the correct page ID
          dynamicHeight: dynamicPageHeight, // Include the dynamic height
        }).unwrap();

        if (!response || response.status !== 200) {
          console.error('Layout update failed:', response);
          toast.error('Layout may not be saved properly');
        }
      } catch (layoutError) {
        console.error('Error updating layout:', layoutError);
        toast.error('Failed to save layout');
      }

      toast.success(`Charts aligned ${direction}`);
    },
    [
      layout,
      selectedChart,
      dispatch,
      activePage,
      updateSketchbookLayout,
      sketchbookId,
      gridCols,
      gridRows,
      saveLayoutState,
    ]
  );

  // Distribute charts
  const distributeCharts = useCallback(
    async (direction: 'horizontal' | 'vertical') => {
      // Save current layout for undo
      saveLayoutState();

      if (layout.length < 3) {
        toast.error('Need at least three charts to distribute');
        return;
      }

      const selectedItems = selectedChart
        ? layout.filter((item: Layout) => item.i === selectedChart)
        : [...layout];

      if (selectedItems.length < 3) {
        toast.error('Need at least three charts to distribute');
        return;
      }

      const newLayout = [...layout];

      if (direction === 'horizontal') {
        // Sort by x position
        selectedItems.sort((a: Layout, b: Layout) => a.x - b.x);

        const firstItem = selectedItems[0];
        const lastItem = selectedItems[selectedItems.length - 1];
        const totalWidth = lastItem.x + lastItem.w - firstItem.x;
        const itemCount = selectedItems.length;

        // Calculate spacing
        const spacing =
          (totalWidth -
            selectedItems.reduce(
              (sum: number, item: Layout) => sum + item.w,
              0
            )) /
          (itemCount - 1);

        let currentX = firstItem.x;
        selectedItems.forEach((item: Layout, index: number) => {
          if (index === 0) return; // Skip first item

          const prevItem = selectedItems[index - 1];
          currentX = prevItem.x + prevItem.w + spacing;

          // Update the item in the newLayout
          const itemIndex = newLayout.findIndex((l) => l.i === item.i);
          if (itemIndex !== -1) {
            newLayout[itemIndex] = {
              ...newLayout[itemIndex],
              x: Math.min(gridCols - item.w, Math.max(0, currentX)),
            };
          }
        });
      } else {
        // Sort by y position
        selectedItems.sort((a: Layout, b: Layout) => a.y - b.y);

        const firstItem = selectedItems[0];
        const lastItem = selectedItems[selectedItems.length - 1];
        const totalHeight = lastItem.y + lastItem.h - firstItem.y;
        const itemCount = selectedItems.length;

        // Calculate spacing
        const spacing =
          (totalHeight -
            selectedItems.reduce(
              (sum: number, item: Layout) => sum + item.h,
              0
            )) /
          (itemCount - 1);

        let currentY = firstItem.y;
        selectedItems.forEach((item: Layout, index: number) => {
          if (index === 0) return; // Skip first item

          const prevItem = selectedItems[index - 1];
          currentY = prevItem.y + prevItem.h + spacing;

          // Update the item in the newLayout
          const itemIndex = newLayout.findIndex((l: Layout) => l.i === item.i);
          if (itemIndex !== -1) {
            newLayout[itemIndex] = {
              ...newLayout[itemIndex],
              y: Math.min(gridRows - item.h, Math.max(0, currentY)),
            };
          }
        });
      }

      dispatch(updateLayout({ pageId: activePage, layout: newLayout }));
      try {
        const response = await updateSketchbookLayout({
          id: sketchbookId,
          pageIndex: Number(activePage) - 1,
          layouts: newLayout,
          pageId: activePage, // Ensure we're sending the correct page ID
          dynamicHeight: dynamicPageHeight, // Include the dynamic height
        }).unwrap();

        if (!response || response.status !== 200) {
          console.error('Layout update failed:', response);
          toast.error('Layout may not be saved properly');
        }
      } catch (layoutError) {
        console.error('Error updating layout:', layoutError);
        toast.error('Failed to save layout');
      }

      toast.success(`Charts distributed ${direction}`);
    },
    [
      layout,
      selectedChart,
      dispatch,
      activePage,
      updateSketchbookLayout,
      sketchbookId,
      gridCols,
      gridRows,
      saveLayoutState,
    ]
  );

  // Start of the autoArrangeLayout function
  const autoArrangeLayout = async () => {
    // Save current layout for undo
    saveLayoutState();

    // Sort items by area (largest first)
    const sortedItems = layout
      .slice()
      .sort(
        (a: Layout, b: Layout) =>
          (b.w || 1) * (b.h || 1) - (a.w || 1) * (a.h || 1)
      );

    // Store occupied positions using a Set
    const occupied = new Set<string>();

    const isPositionValid = (x: number, y: number, w: number, h: number) => {
      if (x < 0 || y < 0 || x + w > gridCols || y + h > gridRows) return false;

      for (let dy = 0; dy < h; dy++) {
        for (let dx = 0; dx < w; dx++) {
          if (occupied.has(`${x + dx},${y + dy}`)) return false;
        }
      }
      return true;
    };

    const markPosition = (x: number, y: number, w: number, h: number) => {
      for (let dy = 0; dy < h; dy++) {
        for (let dx = 0; dx < w; dx++) {
          occupied.add(`${x + dx},${y + dy}`);
        }
      }
    };

    // **Find the first available position from top-left to right**
    const findBestPosition = (w: number, h: number) => {
      for (let y = 0; y < gridRows; y++) {
        for (let x = 0; x < gridCols; x++) {
          if (isPositionValid(x, y, w, h)) return { x, y };
        }
      }
      return { x: 0, y: 0 }; // Fallback
    };

    // Arrange items
    const arrangedLayout = sortedItems.map((item: Layout) => {
      const { w, h } = {
        w: Math.min(item.w || 1, gridCols),
        h: Math.min(item.h || 1, gridRows),
      };
      const { x, y } = findBestPosition(w, h);

      markPosition(x, y, w, h);
      return { ...item, x, y, w, h };
    });

    // **Compact Layout (Move up & left where possible)**
    const compactLayout = arrangedLayout.map((item: Layout) => {
      let { x, y, w, h } = item;

      while (y > 0 && isPositionValid(x, y - 1, w, h)) y--; // Move up
      while (x > 0 && isPositionValid(x - 1, y, w, h)) x--; // Move left

      return { ...item, x, y };
    });

    // Prevent unnecessary calls if layout is unchanged
    if (JSON.stringify(compactLayout) === JSON.stringify(layout)) {
      toast.error('No changes to arrange');
      return;
    }

    // Update Redux store & backend
    dispatch(updateLayout({ pageId: activePage, layout: compactLayout }));
    try {
      const response = await updateSketchbookLayout({
        id: sketchbookId,
        pageIndex: Number(activePage) - 1,
        layouts: compactLayout,
        pageId: activePage, // Ensure we're sending the correct page ID
        dynamicHeight: dynamicPageHeight, // Include the dynamic height
      }).unwrap();

      if (!response || response.status !== 200) {
        console.error('Layout update failed:', response);
        toast.error('Layout may not be saved properly');
      }
    } catch (layoutError) {
      console.error('Error updating layout:', layoutError);
      toast.error('Failed to save layout');
    }

    toast.success('Charts auto-arranged');
  };

  // Keyboard shortcuts info modal
  const KeyboardShortcutsModal = () => (
    <div
      className="keyboard-shortcuts-modal"
      style={{
        position: 'fixed',
        top: '50%',
        left: '50%',
        transform: 'translate(-50%, -50%)',
        backgroundColor: 'white',
        padding: '20px',
        borderRadius: '8px',
        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.15)',
        zIndex: 1000,
        maxWidth: '600px',
        width: '90%',
      }}
    >
      <h3 style={{ marginTop: 0 }}>Keyboard Shortcuts</h3>
      <div
        style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '10px' }}
      >
        <div>
          <h4>Layout Controls</h4>
          <ul style={{ paddingLeft: '20px' }}>
            <li>
              <strong>Ctrl+Z</strong>: Undo layout change
            </li>
            <li>
              <strong>Ctrl+Y</strong> or <strong>Ctrl+Shift+Z</strong>: Redo
              layout change
            </li>
            <li>
              <strong>G</strong>: Toggle grid lines
            </li>
            <li>
              <strong>Ctrl++</strong>: Increase grid density
            </li>
            <li>
              <strong>Ctrl+-</strong>: Decrease grid density
            </li>
          </ul>
        </div>
        <div>
          <h4>Chart Controls</h4>
          <ul style={{ paddingLeft: '20px' }}>
            <li>
              <strong>Ctrl+D</strong>: Duplicate selected chart
            </li>
            <li>
              <strong>Ctrl+Delete</strong>: Remove selected chart
            </li>
            <li>
              <strong>Ctrl+Wheel</strong>: Zoom in/out
            </li>
            <li>
              <strong>Ctrl+Drag</strong>: Pan around
            </li>
          </ul>
        </div>
      </div>
      <button
        onClick={() => setKeyboardShortcutsModalOpen(false)}
        style={{
          padding: '8px 16px',
          backgroundColor: '#174E86',
          color: 'white',
          border: 'none',
          borderRadius: '4px',
          marginTop: '15px',
          cursor: 'pointer',
        }}
      >
        Close
      </button>
    </div>
  );

  // Minimap component
  const Minimap = () => {
    const scale = 0.1;
    return (
      <div
        style={{
          position: 'absolute',
          bottom: '20px',
          right: '20px',
          width: `${pageSize.width * scale}px`,
          height: `${pageSize.height * scale}px`,
          border: '1px solid rgba(0, 0, 0, 0.2)',
          backgroundColor: 'rgba(255, 255, 255, 0.9)',
          zIndex: 100,
          overflow: 'hidden',
          borderRadius: '4px',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
        }}
      >
        {layout.map((item: Layout) => (
          <div
            key={`minimap-${item.i}`}
            style={{
              position: 'absolute',
              left: `${item.x * scale * (pageSize.width / gridCols)}px`,
              top: `${item.y * scale * (pageSize.height / gridRows)}px`,
              width: `${item.w * scale * (pageSize.width / gridCols)}px`,
              height: `${item.h * scale * (pageSize.height / gridRows)}px`,
              backgroundColor: item.i === selectedChart ? '#007bff' : '#ccc',
              border:
                item.i === selectedChart
                  ? '1px solid #0056b3'
                  : '1px solid #bbb',
            }}
          />
        ))}
      </div>
    );
  };

  // Add new state variables
  const [preventCollision, setPreventCollision] = useState<boolean>(false);
  const [compactType, setCompactType] = useState<
    'vertical' | 'horizontal' | null
  >('vertical');

  // Add this near your other useMemo hooks
  const printableAreaStyle = useMemo(() => {
    const pageWidth = pageSize?.width || 0;
    const pageHeight = pageSize?.height || 0;

    // Standard page sizes in pixels (at 96 DPI)
    const standardSizes = {
      A5: { width: 559, height: 794 }, // 148mm × 210mm
      A4: { width: 794, height: 1123 }, // 210mm × 297mm
      Letter: { width: 816, height: 1056 }, // 8.5" × 11"
      Legal: { width: 816, height: 1344 }, // 8.5" × 14"
    };

    // A4 dimensions for custom size reference
    const a4Size = standardSizes['A4'];

    // Check if current size matches any standard size (in either orientation)
    const isStandardSize = Object.values(standardSizes).some(
      (size) =>
        (Math.abs(pageWidth - size.width) <= 1 &&
          Math.abs(pageHeight - size.height) <= 1) ||
        (Math.abs(pageWidth - size.height) <= 1 &&
          Math.abs(pageHeight - size.width) <= 1)
    );

    if (isStandardSize) {
      // For standard sizes, use the actual page dimensions
      return {
        width: `${pageWidth}px`,
        height: `${pageHeight}px`,
        position: 'absolute' as const,
        top: '0',
        left: '0',
        border: '2px dashed rgba(0, 0, 0, 0.2)',
        pointerEvents: 'none' as const,
        zIndex: 1000,
      };
    } else {
      // For custom sizes, use A4 dimensions centered in the page
      const isLandscape = pageWidth > pageHeight;
      const printableWidth = isLandscape ? a4Size.height : a4Size.width;
      const printableHeight = isLandscape ? a4Size.width : a4Size.height;

      // Center the A4 printable area
      const leftOffset = Math.max(0, (pageWidth - printableWidth) / 2);
      const topOffset = Math.max(0, (pageHeight - printableHeight) / 2);

      return {
        width: `${printableWidth}px`,
        height: `${printableHeight}px`,
        position: 'absolute' as const,
        top: `${topOffset}px`,
        left: `${leftOffset}px`,
        border: '2px dashed rgba(0, 0, 0, 0.2)',
        pointerEvents: 'none' as const,
        zIndex: 1000,
      };
    }
  }, [pageSize]);

  return (
    <div
      className="dropable-container"
      style={{ backgroundColor: dropableColor }}
    >
      {!pageEnabled ? (
        <div
          style={{
            width: '100%',
            height: '78vh',
          }}
          className="react-flow-container"
          onDrop={handleDrop}
          onDragOver={(e) => e.preventDefault()}
        >
          <ReactFlow
            nodes={flowNodes.map((node: Node, index: number) => {
              return {
                ...node,
                data: {
                  ...node.data,
                  onNodeChange: handleNodeChange,
                  onOrgNodeChange: handleOrgNodeChange,
                  index: index,
                  sketchbookId: sketchbookId,
                },
              };
            })}
            // nodes={flowNodes}
            edges={flowEdges}
            onNodesChange={onNodesChange}
            onEdgesChange={onEdgesChange}
            onConnect={onConnect}
            nodeTypes={nodeTypes}
            onNodesDelete={onNodesDelete}
            onEdgesDelete={onEdgesDelete}
            deleteKeyCode={['Delete']}
            fitView
          >
            {/* <Background /> */}
            <Controls />
          </ReactFlow>
        </div>
      ) : (
        // Chart Editor with TransformWrapper
        <TransformWrapper
          initialScale={calculateInitialScale}
          initialPositionX={0}
          initialPositionY={0}
          minScale={0.02}
          maxScale={2}
          centerOnInit={true}
          panning={{
            disabled:
              isDraggingOrResizing || (isTouchDevice && gestureState.isPanning),
            velocityDisabled: true,
          }}
          wheel={{
            disabled: isDraggingOrResizing,
            step: pageSize?.width >= 2000 ? 0.05 : 0.1,
            wheelDisabled: false,
            activationKeys: isTouchDevice ? [] : ['Control'],
          }}
          doubleClick={{
            disabled: isTouchDevice, // Disable double-click zoom on touch devices
          }}
          {...(isTouchDevice ? gestureHandlers : {})}
        >
          {({ zoomIn, zoomOut, resetTransform }) => (
            <>
              {/* {showHelpTooltip && (
                <div className="help-tooltip">
                  <p>📌 Editor controls are now available at the top left!</p>
                  <button onClick={() => setShowHelpTooltip(false)}>
                    Got it!
                </button>
              </div>
              )} */}

              {/* Editor Controls - Place at the beginning of the component */}
              <EditorControls
                showGridLines={showGridLines}
                setShowGridLines={setShowGridLines}
                gridDensity={gridDensity}
                setGridDensity={setGridDensity}
                selectedChart={selectedChart}
                undoStack={undoStack}
                redoStack={redoStack}
                zoomIn={zoomIn}
                zoomOut={zoomOut}
                resetTransform={resetTransform}
                handleUndo={handleUndo}
                handleRedo={handleRedo}
                handleDuplicateChart={handleDuplicateChart}
                alignCharts={alignCharts}
                distributeCharts={distributeCharts}
                autoArrangeLayout={autoArrangeLayout}
                gridCols={gridCols}
                gridRows={gridRows}
                layoutLength={layout.length}
                handleRemoveChart={handleRemoveChart}
                charts={charts}
                // onPrint={printEditor}
                preventCollision={preventCollision}
                setPreventCollision={setPreventCollision}
                compactType={compactType}
                setCompactType={setCompactType}
              />

              <Tooltip title="Auto Arrange Charts on Page" placement="right">
                <div
                  className="auto-arrange-button"
                  onClick={autoArrangeLayout}
                >
                  <CgArrangeBack size={16} />
                </div>
              </Tooltip>

              <TransformComponent
                wrapperStyle={{
                  width: '100%',
                  height: '78vh',
                  overflow: 'auto',
                }}
                wrapperClass={`transform-wrapper dotted-container ${showGridLines ? 'show-grid' : ''}`}
                contentClass="transform-content"
              >
                <div
                  className="page-container"
                  style={{
                    width: `${pageSize?.width}px`,
                    minHeight: `${pageSize?.height}px`,
                    // Add dynamic height based on content
                    height: `${dynamicPageHeight}px`,
                    minWidth: '100%',
                    maxHeight: '100%',
                    // Adjust grid size to maintain consistent sizing with the dynamic height
                    backgroundSize: showGridLines
                      ? `${pageSize.width / gridCols}px ${pageSize.height / gridRows}px`
                      : 'initial',
                    backgroundImage: showGridLines
                      ? 'linear-gradient(to right, rgba(0, 0, 0, 0.05) 1px, transparent 1px), linear-gradient(to bottom, rgba(0, 0, 0, 0.05) 1px, transparent 1px)'
                      : 'none',
                    // Ensure the background extends to fill the dynamic height
                    backgroundRepeat: 'repeat',
                    transition: 'height 0.2s ease-out',
                  }}
                  onDrop={handleDrop}
                  onDragOver={(e) => e.preventDefault()}
                  onMouseDown={handlePanningStop}
                  {...(isTouchDevice
                    ? {
                        onTouchStart: (e: React.TouchEvent) => {
                          // Store touch start for potential drop simulation
                          const touch = e.touches[0];
                          if (touch) {
                            (e.currentTarget as any).touchStartData = {
                              x: touch.clientX,
                              y: touch.clientY,
                              timestamp: Date.now(),
                            };
                          }
                        },
                        onTouchEnd: (e: React.TouchEvent) => {
                          // Check if this might be a drop gesture
                          const touchStartData = (e.currentTarget as any)
                            .touchStartData;
                          if (touchStartData && e.changedTouches[0]) {
                            const touch = e.changedTouches[0];
                            const deltaTime =
                              Date.now() - touchStartData.timestamp;
                            const deltaX = Math.abs(
                              touch.clientX - touchStartData.x
                            );
                            const deltaY = Math.abs(
                              touch.clientY - touchStartData.y
                            );

                            // If touch was held for a reasonable time and didn't move much,
                            // it might be a drop gesture from a dragged element
                            if (deltaTime > 200 && deltaX < 50 && deltaY < 50) {
                              console.log('Potential touch drop detected');
                              if (isSamsungDevice) {
                                toast(
                                  'Try dragging chart elements from the left panel',
                                  {
                                    icon: '📱',
                                    duration: 3000,
                                  }
                                );
                              }
                            }
                          }
                        },
                      }
                    : {})}
                >
                  {/* Add a hidden div that contains the printable content without transformations */}
                  <div
                    ref={printableContentRef}
                    style={{
                      position: 'absolute',
                      left: '-9999px',
                      top: '-9999px',
                    }}
                  >
                    {charts.map((chart: any) => {
                      const layoutItem = layout.find(
                        (l: Layout) => l.i === chart.id
                      );
                      if (!layoutItem) return null;

                      // Calculate exact pixel positions for print
                      // Scale coordinates to match A4 dimensions
                      const a4Width =
                        pageSize.orientation === 'landscape' ? 1123 : 794;
                      const widthRatio = a4Width / pageSize.width;

                      const left =
                        layoutItem.x * (pageSize.width / gridCols) * widthRatio;
                      const top =
                        layoutItem.y *
                        (pageSize.height / gridRows) *
                        widthRatio;
                      const width =
                        (layoutItem.w * (pageSize.width / gridCols) - 4) *
                        widthRatio; // Reduced padding
                      const height =
                        (layoutItem.h * (pageSize.height / gridRows) - 4) *
                        widthRatio; // Reduced padding

                      // Prepare chart data for non-image, non-textarea charts
                      const isChartType =
                        chart.type !== 'image' && chart.type !== 'textarea';

                      // If this is a chart, prepare the data to be serialized
                      let chartDataStr = '';
                      if (isChartType) {
                        try {
                          // Create a simplified version of the chart data for reconstruction
                          const printChartData = {
                            type: chart.type,
                            data: chart.data,
                            options: {
                              responsive: true,
                              maintainAspectRatio: false,
                              animation: false,
                              plugins: {
                                legend: {
                                  display: true,
                                  position: 'top',
                                  labels: {
                                    boxWidth: 10,
                                    font: { size: 9 },
                                  },
                                },
                                tooltip: { enabled: false },
                              },
                              layout: {
                                padding: {
                                  right: 5,
                                  left: 5,
                                  top: 5,
                                  bottom: 5,
                                },
                              },
                            },
                          };

                          // Merge with chart's original options if available
                          if (chart.options) {
                            printChartData.options = {
                              ...printChartData.options,
                              ...chart.options,
                            };
                          }

                          chartDataStr = JSON.stringify(printChartData);
                        } catch (e) {
                          console.error(
                            'Failed to serialize chart data for printing',
                            e
                          );
                        }
                      }

                      // Fixed style typing
                      const commonStyles: React.CSSProperties = {
                        position: 'absolute',
                        left: `${left}px`,
                        top: `${top}px`,
                        width: `${width}px`,
                        height: `${height}px`,
                        zIndex: 1000 - layoutItem.y,
                      };

                      if (isChartType) {
                        // For chart types, use canvas
                        return (
                          <div
                            key={`print-${chart.id}`}
                            className="chart-container"
                            style={commonStyles}
                            data-chart-type={chart.type}
                            data-chart-data={chartDataStr}
                          >
                            <div className="chart-wrapper">
                              <canvas
                                width={width}
                                height={height}
                                style={{ width: '100%', height: '100%' }}
                              />
                            </div>
                          </div>
                        );
                      } else if (chart.type === 'image') {
                        // For images
                        return (
                          <div
                            key={`print-${chart.id}`}
                            className="chart-container"
                            style={commonStyles}
                            data-chart-type="image"
                          >
                            <div className="chart-wrapper">
                              <img
                                src={chart.data.src}
                                alt={chart.data.alt || 'Image'}
                                style={{
                                  width: '100%',
                                  height: '100%',
                                  objectFit: 'contain',
                                }}
                              />
                            </div>
                          </div>
                        );
                      } else {
                        // For text content
                        return (
                          <div
                            key={`print-${chart.id}`}
                            className="chart-container"
                            style={commonStyles}
                            data-chart-type="textarea"
                          >
                            <div className="chart-wrapper">
                              <div
                                dangerouslySetInnerHTML={{
                                  __html:
                                    chart.data.content || chart.data.text || '',
                                }}
                              />
                            </div>
                          </div>
                        );
                      }
                    })}
                  </div>

                  <div style={printableAreaStyle}>
                    <div className="corner-marker top-left" />
                    <div className="corner-marker top-right" />
                    <div className="corner-marker bottom-left" />
                    <div className="corner-marker bottom-right" />
                  </div>

                  <GridLayout
                    className="layout"
                    layout={layout}
                    cols={Math.floor(gridCols * gridDensity)}
                    rowHeight={
                      Math.floor(
                        (pageSize.height / gridRows) * (1 / gridDensity)
                      ) - 6
                    }
                    width={pageSize?.width}
                    onLayoutChange={handleLayoutChange}
                    onResize={handleResize}
                    onDragStart={() => {
                      onDragStart();
                      saveLayoutState(); // Save state for undo when starting to drag
                    }}
                    onDragStop={() => {
                      onDragStop();
                    }}
                    onResizeStart={() => {
                      onResizeStart();
                      saveLayoutState(); // Save state for undo when starting to resize
                    }}
                    onResizeStop={() => {
                      onResizeStop();
                    }}
                    draggableHandle=".drag-handle"
                    isResizable={true}
                    isDraggable={true}
                    compactType={compactType}
                    preventCollision={preventCollision}
                    margin={[5, 5]}
                    useCSSTransforms={true}
                    transformScale={calculateInitialScale}
                    resizeHandles={['se', 'sw', 'ne', 'nw']}
                    autoSize={true}
                  >
                    {memoizedCharts}
                  </GridLayout>

                  {showMinimap && <Minimap />}
                </div>
              </TransformComponent>
            </>
          )}
        </TransformWrapper>
      )}

      <Menu
        open={contextMenu !== null}
        onClose={handleClose}
        anchorReference="anchorPosition"
        anchorPosition={
          contextMenu !== null
            ? { top: contextMenu.mouseY, left: contextMenu.mouseX }
            : undefined
        }
      >
        <MenuItem
          onClick={() => {
            if (contextMenu?.chartId) {
              handleDuplicateChart(contextMenu.chartId);
              handleClose();
            }
          }}
        >
          Duplicate Chart
        </MenuItem>
        <MenuItem disabled>Move to page:</MenuItem>
        {pages && pages.length > 0 ? (
          pages.map((page) => (
            <MenuItem
              key={page.id}
              onClick={() => handleMove(page.id)}
              disabled={page.id === activePage}
            >
              {page.name} {page.id === activePage ? '(current)' : ''}
            </MenuItem>
          ))
        ) : (
          <MenuItem disabled>No other pages available</MenuItem>
        )}
      </Menu>

      {keyboardShortcutsModalOpen && <KeyboardShortcutsModal />}
    </div>
  );
};

export default DropableEditor;

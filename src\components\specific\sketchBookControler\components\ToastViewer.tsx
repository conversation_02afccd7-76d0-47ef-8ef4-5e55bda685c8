import React, { useRef, useEffect } from 'react';
import { Viewer } from '@toast-ui/react-editor';
import '@toast-ui/editor/dist/toastui-editor-viewer.css';
import '@toast-ui/editor/dist/theme/toastui-editor-dark.css';
import './ToastViewer.css';
import './ToastViewerOverride.css';
import katex from 'katex';
import 'katex/dist/katex.min.css';

// Import plugins if needed
// import codeSyntaxHighlight from '@toast-ui/editor-plugin-code-syntax-highlight';
// import Prism from 'prismjs';
// import tableMergedCell from '@toast-ui/editor-plugin-table-merged-cell';

interface ToastViewerProps {
  content: string;
  theme?: 'light' | 'dark';
  style?: React.CSSProperties;
}

const ToastViewer: React.FC<ToastViewerProps> = ({
  content,
  theme = 'light',
  style,
}) => {
  const viewerRef = useRef<any>(null);

  // Update content when it changes
  useEffect(() => {
    if (viewerRef.current) {
      const instance = viewerRef.current.getInstance();
      // Ensure content is a string
      const markdownContent = typeof content === 'string' ? content : '';

      // Set the markdown content first
      instance.setMarkdown(markdownContent);

      // Process KaTeX after DOM is updated
      setTimeout(() => {
        const viewerElement = viewerRef.current?.getRootElement();
        if (viewerElement) {
          const contentElement = viewerElement.querySelector(
            '.toastui-editor-contents'
          );
          if (contentElement) {
            // Find all text nodes and process them for math expressions
            const walker = document.createTreeWalker(
              contentElement,
              NodeFilter.SHOW_TEXT,
              null
            );

            const textNodes: Text[] = [];
            let node;
            while ((node = walker.nextNode())) {
              textNodes.push(node as Text);
            }

            // --- Robust display math replacement in HTML ---
            // Replace <p>$$<br>...<br>$$</p> blocks as inline
            let html = contentElement.innerHTML;
            const paragraphDisplayMathRegex =
              /<p[^>]*>\$\$<br\s*\/?>([\s\S]*?)<br\s*\/?>(.*?)\$\$<\/p>/g;
            html = html.replace(
              paragraphDisplayMathRegex,
              (match: string, mathContent1: string, mathContent2: string) => {
                let mathContent = (mathContent1 + '\n' + mathContent2)
                  .replace(/<br\s*\/?>(\n)?/g, '\n')
                  .trim();
                try {
                  // Render as inline
                  return `<span class="katex-inline-block">${katex.renderToString(mathContent, { throwOnError: false, displayMode: false })}</span>`;
                } catch (err) {
                  return match;
                }
              }
            );
            // Fallback: replace any remaining $$...$$ in HTML as inline
            const fallbackDisplayMathRegex = /\$\$([\s\S]*?)\$\$/g;
            html = html.replace(
              fallbackDisplayMathRegex,
              (match: string, mathContent: string) => {
                try {
                  return `<span class="katex-inline-block">${katex.renderToString(mathContent.trim(), { throwOnError: false, displayMode: false })}</span>`;
                } catch (err) {
                  return match;
                }
              }
            );
            contentElement.innerHTML = html;

            // Then process inline math $...$
            setTimeout(() => {
              const walker = document.createTreeWalker(
                contentElement,
                NodeFilter.SHOW_TEXT,
                null
              );

              const textNodes: Text[] = [];
              let node;
              while ((node = walker.nextNode())) {
                textNodes.push(node as Text);
              }

              textNodes.forEach((textNode) => {
                const content = textNode.textContent || '';
                if (content.includes('$') && !content.includes('$$')) {
                  // Only process single $ (inline math)
                  let hasChanges = false;
                  let processed = content;

                  // Process inline math $...$
                  processed = processed.replace(
                    /\$([^$\n]+)\$/g,
                    (match, mathContent) => {
                      try {
                        hasChanges = true;
                        return katex.renderToString(mathContent, {
                          throwOnError: false,
                          displayMode: false,
                        });
                      } catch (err) {
                        return match;
                      }
                    }
                  );

                  if (hasChanges) {
                    // Create a temporary element to hold the processed HTML
                    const tempDiv = document.createElement('div');
                    tempDiv.innerHTML = processed;

                    // Replace the text node with the processed content
                    const fragment = document.createDocumentFragment();
                    while (tempDiv.firstChild) {
                      fragment.appendChild(tempDiv.firstChild);
                    }

                    if (textNode.parentNode) {
                      textNode.parentNode.replaceChild(fragment, textNode);
                    }
                  }
                }
              });
            }, 50); // Small delay to ensure display math is processed first
          }
        }
      }, 150); // Increased delay to ensure DOM is fully rendered
    }
  }, [content]);

  return (
    <div
      className={`toast-viewer-wrapper ${theme === 'dark' ? 'dark-theme' : ''}`}
      style={style}
    >
      <Viewer
        ref={viewerRef}
        initialValue={content}
        theme={theme}
        extendedAutolinks={true}
        linkAttributes={{ target: '_blank' }}
      />
    </div>
  );
};

export default ToastViewer;

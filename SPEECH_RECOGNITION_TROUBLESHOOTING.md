# Speech Recognition Troubleshooting Guide

## Problem Summary
Speech-to-text functionality works in localhost but fails in production environment.

## Root Cause Analysis

### Primary Issues Identified:

1. **HTTPS Requirement**: Web Speech API requires a secure context (HTTPS)
2. **Browser Compatibility**: Limited support across different browsers
3. **Missing Error Handling**: No user feedback when speech recognition fails
4. **Permission Issues**: Microphone access not properly handled

## Solutions Implemented

### 1. Enhanced VoiceInput Component (`src/components/chat/VoiceInput.tsx`)

**Key Improvements:**
- ✅ Browser support detection
- ✅ HTTPS/secure context validation
- ✅ Microphone permission checking
- ✅ Comprehensive error handling
- ✅ User-friendly error messages
- ✅ Visual feedback for different states

**Features Added:**
- Automatic detection of speech recognition support
- Real-time permission status monitoring
- Specific error messages for different failure scenarios
- Disabled state when not supported

### 2. Speech Recognition Utilities (`src/utils/speechRecognitionUtils.ts`)

**Diagnostic Functions:**
- `checkSpeechRecognitionSupport()`: Detects API availability
- `checkSecureContext()`: Validates HTTPS requirement
- `checkMicrophonePermission()`: Checks permission status
- `diagnoseSpeechRecognition()`: Comprehensive system check
- `getSpeechRecognitionErrorMessage()`: User-friendly error messages

### 3. Enhanced ChatInput Component (`src/components/specific/ChatInput/ChatInput.tsx`)

**Improvements:**
- ✅ Conditional rendering based on browser support
- ✅ Better integration with speech recognition state
- ✅ Graceful fallback when not supported

### 4. Diagnostic Components

**SpeechRecognitionDiagnostics Component:**
- Visual diagnostic interface
- Real-time status checking
- Issue identification and recommendations

**SpeechRecognitionTest Page (`/test-speech`):**
- Comprehensive testing interface
- Environment information display
- Step-by-step troubleshooting guide

## Testing and Debugging

### Access the Test Page
Navigate to: `http://your-domain/test-speech`

This page provides:
- Browser support status
- Current environment details
- Voice input testing
- Diagnostic tools
- Troubleshooting recommendations

### Console Diagnostics
The system automatically logs detailed diagnostics in development mode:
```javascript
// Check browser console for:
🎤 Speech Recognition Diagnostics
├── Browser: Chrome/Firefox/Safari/Edge
├── API Supported: Yes/No
├── Secure Context: Yes (HTTPS) / No (HTTP)
├── Microphone Permission: Granted/Denied/Unknown
├── Issues found: [list of issues]
└── Recommendations: [list of solutions]
```

## Common Issues and Solutions

### Issue 1: "Speech recognition requires HTTPS"
**Cause:** Production site is using HTTP instead of HTTPS
**Solution:** 
- Configure SSL certificate for production domain
- Use HTTPS for all production traffic
- Temporary: Test on localhost or use ngrok for HTTPS tunnel

### Issue 2: "Speech recognition not supported in this browser"
**Cause:** Browser doesn't support Web Speech API
**Solution:**
- Use Chrome, Edge, or Safari (recommended)
- Firefox has limited support
- Update browser to latest version

### Issue 3: "Microphone permission denied"
**Cause:** User denied microphone access
**Solution:**
- Click microphone icon in address bar
- Allow microphone access
- Refresh page after granting permission

### Issue 4: "Network error" or "Service not available"
**Cause:** Internet connectivity or Google's speech service issues
**Solution:**
- Check internet connection
- Try again later
- Verify firewall/proxy settings

## Production Deployment Checklist

### ✅ Pre-Deployment
- [ ] Ensure production domain has SSL certificate
- [ ] Verify HTTPS is properly configured
- [ ] Test speech recognition on staging environment
- [ ] Confirm microphone permissions work correctly

### ✅ Post-Deployment
- [ ] Test speech recognition on production domain
- [ ] Verify error messages display correctly
- [ ] Check browser console for any errors
- [ ] Test on different browsers (Chrome, Safari, Edge)

## Browser Compatibility Matrix

| Browser | Support Level | Notes |
|---------|---------------|-------|
| Chrome | ✅ Full | Best support, recommended |
| Edge | ✅ Full | Good support |
| Safari | ✅ Good | Works well on macOS/iOS |
| Firefox | ⚠️ Limited | Basic support, may have issues |
| Opera | ✅ Good | Based on Chromium |

## Security Requirements

### HTTPS Mandatory
- Web Speech API requires secure context
- HTTP will always fail (except localhost)
- Self-signed certificates may cause issues

### Permissions
- Microphone permission required
- Permission persists per domain
- Users can revoke permission anytime

## Error Handling Flow

```
User clicks microphone button
├── Check browser support
│   ├── Not supported → Show browser error
│   └── Supported → Continue
├── Check HTTPS
│   ├── HTTP → Show HTTPS error
│   └── HTTPS → Continue
├── Check permissions
│   ├── Denied → Show permission error
│   └── Granted → Start recognition
└── Handle runtime errors
    ├── Network error → Show network message
    ├── No speech → Show speech prompt
    └── Other → Show generic error
```

## Quick Fixes for Production

### Immediate Solutions:
1. **Enable HTTPS**: Most critical fix
2. **Update error messages**: Guide users to supported browsers
3. **Add permission prompts**: Help users grant microphone access

### Long-term Improvements:
1. **Fallback options**: Consider alternative speech services
2. **Progressive enhancement**: App works without speech recognition
3. **User education**: Tooltips and help text

## Testing Commands

```bash
# Test in development
npm run dev
# Navigate to http://localhost:5173/test-speech

# Test production build
npm run build
npm run preview
# Navigate to http://localhost:4173/test-speech
```

## Support Resources

- [Web Speech API Documentation](https://developer.mozilla.org/en-US/docs/Web/API/Web_Speech_API)
- [Browser Compatibility](https://caniuse.com/speech-recognition)
- [HTTPS Setup Guide](https://letsencrypt.org/)

## Contact Information

For additional support with speech recognition issues:
1. Check browser console for detailed error messages
2. Use the `/test-speech` page for diagnostics
3. Verify HTTPS configuration
4. Test with different browsers

.container {
  width: 100%;
}

.tableWrapper {
  overflow-x: auto;
  border-radius: 0.5rem;
}

.table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.875rem;
}

.table thead {
  background-color: var(--table-header-bg, #f9fafb);
}

.dark .table thead {
  background-color: var(--dark-table-header-bg, #374151);
}

.table th {
  padding: 1rem;
  text-align: left;
  font-weight: 600;
  color: var(--text-color, #374151);
  border-bottom: 1px solid var(--border-color, #e5e7eb);
  white-space: nowrap;
}

.dark .table th {
  color: var(--dark-text, #f3f4f6);
  border-bottom-color: var(--dark-border, #4b5563);
}

.table td {
  padding: 1rem;
  border-bottom: 1px solid var(--border-color, #f3f4f6);
  vertical-align: top;
}

.dark .table td {
  border-bottom-color: var(--dark-border, #374151);
}

.table tbody tr:hover {
  background-color: var(--hover-bg, #f9fafb);
}

.dark .table tbody tr:hover {
  background-color: var(--dark-hover-bg, #374151);
}

.paymentId {
  display: flex;
  flex-direction: column;
}

.idText {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.75rem;
  color: var(--text-secondary, #6b7280);
  background-color: var(--code-bg, #f3f4f6);
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.dark .idText {
  color: var(--dark-text-secondary, #9ca3af);
  background-color: var(--dark-code-bg, #4b5563);
}

.dateCell {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.primaryDate {
  font-weight: 500;
  color: var(--text-color, #374151);
}

.dark .primaryDate {
  color: var(--dark-text, #f3f4f6);
}

.secondaryDate {
  font-size: 0.75rem;
  color: var(--text-secondary, #6b7280);
}

.dark .secondaryDate {
  color: var(--dark-text-secondary, #9ca3af);
}

.amount {
  font-weight: 600;
  color: var(--success-color, #059669);
  font-size: 0.9rem;
}

.dark .amount {
  color: var(--dark-success-color, #10b981);
}

.planId {
  font-size: 0.75rem;
  color: var(--text-secondary, #6b7280);
  background-color: var(--tag-bg, #e5e7eb);
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-weight: 500;
}

.dark .planId {
  color: var(--dark-text-secondary, #9ca3af);
  background-color: var(--dark-tag-bg, #4b5563);
}

.actions {
  display: flex;
  gap: 0.5rem;
}

.actionButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: 1px solid var(--border-color, #d1d5db);
  background-color: var(--button-bg, #ffffff);
  color: var(--text-secondary, #6b7280);
  border-radius: 0.375rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.actionButton:hover {
  background-color: var(--button-hover-bg, #f3f4f6);
  color: var(--primary-color, #2563eb);
  border-color: var(--primary-color, #2563eb);
}

.dark .actionButton {
  border-color: var(--dark-border, #4b5563);
  background-color: var(--dark-button-bg, #374151);
  color: var(--dark-text-secondary, #9ca3af);
}

.dark .actionButton:hover {
  background-color: var(--dark-button-hover-bg, #4b5563);
  color: var(--dark-primary, #60a5fa);
  border-color: var(--dark-primary, #60a5fa);
}

.pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-top: 1px solid var(--border-color, #e5e7eb);
  background-color: var(--pagination-bg, #f9fafb);
}

.dark .pagination {
  border-top-color: var(--dark-border, #4b5563);
  background-color: var(--dark-pagination-bg, #374151);
}

.paginationInfo {
  font-size: 0.875rem;
  color: var(--text-secondary, #6b7280);
}

.dark .paginationInfo {
  color: var(--dark-text-secondary, #9ca3af);
}

.paginationControls {
  display: flex;
  gap: 0.25rem;
}

.paginationButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border: 1px solid var(--border-color, #d1d5db);
  background-color: var(--button-bg, #ffffff);
  color: var(--text-color, #374151);
  border-radius: 0.375rem;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.paginationButton:hover {
  background-color: var(--button-hover-bg, #f3f4f6);
  border-color: var(--primary-color, #2563eb);
}

.paginationButton.active {
  background-color: var(--primary-color, #2563eb);
  color: white;
  border-color: var(--primary-color, #2563eb);
}

.dark .paginationButton {
  border-color: var(--dark-border, #4b5563);
  background-color: var(--dark-button-bg, #374151);
  color: var(--dark-text, #f3f4f6);
}

.dark .paginationButton:hover {
  background-color: var(--dark-button-hover-bg, #4b5563);
  border-color: var(--dark-primary, #60a5fa);
}

.dark .paginationButton.active {
  background-color: var(--dark-primary, #60a5fa);
  color: var(--dark-text, #1f2937);
  border-color: var(--dark-primary, #60a5fa);
}

.paginationEllipsis {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  color: var(--text-secondary, #6b7280);
  font-size: 0.875rem;
}

.dark .paginationEllipsis {
  color: var(--dark-text-secondary, #9ca3af);
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: var(--text-secondary, #6b7280);
}

.dark .loadingContainer {
  color: var(--dark-text-secondary, #9ca3af);
}

.loadingSpinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--border-color, #e5e7eb);
  border-top: 3px solid var(--primary-color, #2563eb);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

.dark .loadingSpinner {
  border-color: var(--dark-border, #4b5563);
  border-top-color: var(--dark-primary, #60a5fa);
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
}

.emptyIcon {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.emptyState h3 {
  margin: 0 0 0.5rem 0;
  color: var(--text-color, #374151);
  font-size: 1.125rem;
  font-weight: 600;
}

.dark .emptyState h3 {
  color: var(--dark-text, #f3f4f6);
}

.emptyState p {
  margin: 0;
  color: var(--text-secondary, #6b7280);
  max-width: 400px;
}

.dark .emptyState p {
  color: var(--dark-text-secondary, #9ca3af);
}

/* Responsive Design */
@media (max-width: 768px) {
  .table {
    font-size: 0.75rem;
  }

  .table th,
  .table td {
    padding: 0.75rem 0.5rem;
  }

  .pagination {
    flex-direction: column;
    gap: 1rem;
    padding: 1rem;
  }

  .paginationControls {
    flex-wrap: wrap;
    justify-content: center;
  }

  .idText {
    max-width: 80px;
  }

  .actions {
    flex-direction: column;
    gap: 0.25rem;
  }
}

/**
 * Google Analytics 4 (GA4) utility functions for tracking user interactions
 * and page views in the NEUQUIP application.
 */

declare global {
  interface Window {
    gtag: (...args: any[]) => void;
    dataLayer: any[];
  }
}

// GA4 Configuration
const GA4_MEASUREMENT_ID = import.meta.env.VITE_GA4_MEASUREMENT_ID;
const isDevelopment = import.meta.env.DEV;

/**
 * Initialize Google Analytics 4
 */
export const initializeGA4 = (): void => {
  if (!GA4_MEASUREMENT_ID) {
    console.warn('GA4 Measurement ID not found in environment variables');
    return;
  }

  if (typeof window !== 'undefined') {
    // Load the GA4 script if not already loaded
    if (
      !document.querySelector(
        `script[src*="googletagmanager.com/gtag/js?id=${GA4_MEASUREMENT_ID}"]`
      )
    ) {
      const script = document.createElement('script');
      script.async = true;
      script.src = `https://www.googletagmanager.com/gtag/js?id=${GA4_MEASUREMENT_ID}`;
      script.onload = () => {
        configureGA4();
      };
      script.onerror = () => {
        console.error('Failed to load GA4 script');
      };
      document.head.appendChild(script);
    } else {
      configureGA4();
    }
  }
};

/**
 * Configure GA4 after script is loaded
 */
const configureGA4 = (): void => {
  if (typeof window !== 'undefined' && window.gtag) {
    // Configure GA4 with the measurement ID
    window.gtag('config', GA4_MEASUREMENT_ID, {
      // Disable tracking in development mode
      debug_mode: isDevelopment,
      send_page_view: false, // We'll handle page views manually
      // Privacy settings
      anonymize_ip: true,
      allow_google_signals: false,
      allow_ad_personalization_signals: false,
    });
  }
};

/**
 * Track page views
 */
export const trackPageView = (
  page_title: string,
  page_location: string,
  page_path: string,
  user_id?: string
): void => {
  if (!GA4_MEASUREMENT_ID || typeof window === 'undefined' || !window.gtag) {
    return;
  }

  const pageViewData: any = {
    page_title,
    page_location,
    page_path,
    send_to: GA4_MEASUREMENT_ID,
  };

  // Add user ID if available (for authenticated users)
  if (user_id) {
    pageViewData.user_id = user_id;
  }

  window.gtag('event', 'page_view', pageViewData);
};

/**
 * Track custom events
 */
export const trackEvent = (
  action: string,
  parameters?: {
    event_category?: string;
    event_label?: string;
    value?: number;
    user_id?: string;
    custom_parameters?: Record<string, any>;
  }
): void => {
  if (!GA4_MEASUREMENT_ID || typeof window === 'undefined' || !window.gtag) {
    return;
  }

  const eventData: any = {
    send_to: GA4_MEASUREMENT_ID,
    ...parameters?.custom_parameters,
  };

  // Add standard parameters if provided
  if (parameters?.event_category) {
    eventData.event_category = parameters.event_category;
  }
  if (parameters?.event_label) {
    eventData.event_label = parameters.event_label;
  }
  if (parameters?.value !== undefined) {
    eventData.value = parameters.value;
  }
  if (parameters?.user_id) {
    eventData.user_id = parameters.user_id;
  }

  window.gtag('event', action, eventData);
};

/**
 * Set user properties
 */
export const setUserProperties = (properties: {
  user_id?: string;
  user_role?: string;
  subscription_status?: string;
  custom_properties?: Record<string, any>;
}): void => {
  if (!GA4_MEASUREMENT_ID || typeof window === 'undefined' || !window.gtag) {
    return;
  }

  const userProperties: any = {
    send_to: GA4_MEASUREMENT_ID,
    ...properties.custom_properties,
  };

  // Add standard user properties
  if (properties.user_role) {
    userProperties.user_role = properties.user_role;
  }
  if (properties.subscription_status) {
    userProperties.subscription_status = properties.subscription_status;
  }

  window.gtag('set', userProperties);

  // Set user ID separately if provided
  if (properties.user_id) {
    window.gtag('config', GA4_MEASUREMENT_ID, {
      user_id: properties.user_id,
    });
  }
};

// Predefined event tracking functions for common actions

/**
 * Track user authentication events
 */
export const trackAuth = {
  login: (method: string, user_id: string) => {
    trackEvent('login', {
      event_category: 'authentication',
      event_label: method,
      user_id,
      custom_parameters: { method },
    });
  },

  signup: (method: string) => {
    trackEvent('sign_up', {
      event_category: 'authentication',
      event_label: method,
      custom_parameters: { method },
    });
  },

  logout: (user_id: string) => {
    trackEvent('logout', {
      event_category: 'authentication',
      user_id,
    });
  },
};

/**
 * Track navigation events
 */
export const trackNavigation = {
  menuClick: (menu_item: string, user_id?: string) => {
    trackEvent('menu_click', {
      event_category: 'navigation',
      event_label: menu_item,
      user_id,
      custom_parameters: { menu_item },
    });
  },

  buttonClick: (
    button_name: string,
    page_location: string,
    user_id?: string
  ) => {
    trackEvent('button_click', {
      event_category: 'interaction',
      event_label: button_name,
      user_id,
      custom_parameters: { button_name, page_location },
    });
  },
};

/**
 * Track content creation events
 */
export const trackContent = {
  createProject: (project_type: string, user_id: string) => {
    trackEvent('create_project', {
      event_category: 'content',
      event_label: project_type,
      user_id,
      custom_parameters: { project_type },
    });
  },

  createSketchbook: (template_type: string, user_id: string) => {
    trackEvent('create_sketchbook', {
      event_category: 'content',
      event_label: template_type,
      user_id,
      custom_parameters: { template_type },
    });
  },

  uploadFile: (file_type: string, file_size: number, user_id: string) => {
    trackEvent('file_upload', {
      event_category: 'content',
      event_label: file_type,
      value: file_size,
      user_id,
      custom_parameters: { file_type, file_size },
    });
  },

  sendMessage: (message_type: string, user_id: string) => {
    trackEvent('send_message', {
      event_category: 'communication',
      event_label: message_type,
      user_id,
      custom_parameters: { message_type },
    });
  },
};

/**
 * Track workflow events
 */
export const trackWorkflow = {
  createWorkflow: (workflow_type: string, user_id: string) => {
    trackEvent('create_workflow', {
      event_category: 'workflow',
      event_label: workflow_type,
      user_id,
      custom_parameters: { workflow_type },
    });
  },

  approveWorkflow: (workflow_id: string, user_id: string) => {
    trackEvent('approve_workflow', {
      event_category: 'workflow',
      user_id,
      custom_parameters: { workflow_id },
    });
  },

  rejectWorkflow: (workflow_id: string, user_id: string) => {
    trackEvent('reject_workflow', {
      event_category: 'workflow',
      user_id,
      custom_parameters: { workflow_id },
    });
  },
};

/**
 * Track payment events
 */
export const trackPayment = {
  initiatePayment: (amount: number, currency: string, user_id: string) => {
    trackEvent('begin_checkout', {
      event_category: 'ecommerce',
      value: amount,
      user_id,
      custom_parameters: { currency, amount },
    });
  },

  completePayment: (
    amount: number,
    currency: string,
    transaction_id: string,
    user_id: string
  ) => {
    trackEvent('purchase', {
      event_category: 'ecommerce',
      value: amount,
      user_id,
      custom_parameters: {
        currency,
        transaction_id,
        amount,
      },
    });
  },

  paymentError: (error_message: string, user_id: string) => {
    trackEvent('payment_error', {
      event_category: 'ecommerce',
      event_label: error_message,
      user_id,
      custom_parameters: { error_message },
    });
  },
};

/**
 * Track search events
 */
export const trackSearch = {
  search: (search_term: string, search_type: string, user_id?: string) => {
    trackEvent('search', {
      event_category: 'search',
      event_label: search_term,
      user_id,
      custom_parameters: { search_term, search_type },
    });
  },
};

/**
 * Track errors
 */
export const trackError = {
  jsError: (error_message: string, error_stack?: string, user_id?: string) => {
    trackEvent('exception', {
      event_category: 'error',
      event_label: error_message,
      user_id,
      custom_parameters: {
        description: error_message,
        fatal: false,
        error_stack,
      },
    });
  },

  apiError: (
    endpoint: string,
    status_code: number,
    error_message: string,
    user_id?: string
  ) => {
    trackEvent('api_error', {
      event_category: 'error',
      event_label: `${endpoint} - ${status_code}`,
      value: status_code,
      user_id,
      custom_parameters: {
        endpoint,
        status_code,
        error_message,
      },
    });
  },
};

/* Additional CSS for CreditUsage component if needed */
.creditUsage {
  /* Base styles are handled by styled components */
}

.creditUsage:hover {
  /* Hover effects are handled by styled components */
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .creditUsage {
    font-size: 0.7rem;
  }
}

/* Dark theme specific adjustments if needed */
[data-theme="dark"] .creditUsage {
  /* Additional dark theme styles if needed */
}

/* Light theme specific adjustments if needed */
[data-theme="light"] .creditUsage {
  /* Additional light theme styles if needed */
}

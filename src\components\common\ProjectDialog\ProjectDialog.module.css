.dialogOverlay {
  position: fixed;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: grid;
  place-items: center;
  z-index: 1000;
}

.dialog {
  margin-top: 30px;
  background: var(--color-background-modal);
  border-radius: 10px;
  width: 75%;
  max-width: 600px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 15px 20px -5px rgba(0, 0, 0, 0.1);
  color: var(--color-text-primary);
  position: relative;
  transition:
    background-color 0.3s ease,
    color 0.3s ease;
}

.dialogHeader {
  padding: 10px 16px;
  border-bottom: 1px solid var(--color-border);
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: border-color 0.3s ease;
}

.dialogHeader h2 {
  margin: 0;
  font-size: 15px;
  font-weight: 600;
}

.closeButton {
  border: none;
  background: none;
  font-size: 18px;
  cursor: pointer;
  color: var(--color-text-secondary);
  padding: 2px 6px;
  transition: color 0.3s ease;
}

.dialogContent {
  padding: 12px;
  height: 350px;
}

.twoColumnLayout {
  display: grid;
  grid-template-columns: 1fr 1.5fr;
  gap: 12px;
  height: 100%;
}

.leftColumn {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.titleInput {
  width: 100%;
  padding: 6px 8px;
  border: 1px solid var(--color-border);
  border-radius: 6px;
  font-size: 13px;
  background-color: var(--color-background-secondary);
  color: var(--color-text-primary);
  transition:
    background-color 0.3s ease,
    color 0.3s ease,
    border-color 0.3s ease;
}

.titleInput:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(var(--color-primary-rgb), 0.1);
}

.uploadSection {
  flex: 1;
}

.dropZone {
  height: 100%;
  border: 2px dashed var(--color-border);
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 6px;
  cursor: pointer;
  padding: 12px;
  color: var(--color-text-secondary);
  transition:
    all 0.2s,
    background-color 0.3s ease,
    color 0.3s ease,
    border-color 0.3s ease;
  background-color: var(--color-background-tertiary);
}

.dropZone:hover {
  border-color: var(--color-primary);
  background-color: rgba(var(--color-primary-rgb), 0.05);
}

.dragActive {
  border-color: var(--color-primary);
  background-color: rgba(var(--color-primary-rgb), 0.1);
}

.dropText {
  margin: 0;
  font-size: 13px;
}

.supportedFormats {
  font-size: 11px;
  color: var(--color-text-secondary);
  transition: color 0.3s ease;
}

.filePreview {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px;
  background-color: var(--color-background-tertiary);
  border-radius: 6px;
  border: 1px solid var(--color-border);
  transition:
    background-color 0.3s ease,
    border-color 0.3s ease;
}

.fileName {
  flex: 1;
  font-size: 13px;
  color: var(--color-text-primary);
  transition: color 0.3s ease;
}

.removeButton {
  border: none;
  background: none;
  color: var(--color-text-secondary);
  cursor: pointer;
  padding: 2px 6px;
  font-size: 16px;
  transition: color 0.3s ease;
}

.rightColumn {
  overflow: hidden;
}

.promptSection {
  height: 100%;
}

.promptHeading {
  text-align: center;
  font-family:
    system-ui,
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    Oxygen,
    Ubuntu,
    Cantarell,
    'Open Sans',
    'Helvetica Neue',
    sans-serif;
  font-size: small;
  font-weight: 500;
}

.promptCards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
  gap: 6px;
  height: 100%;
  overflow-y: auto;
  height: 175px;
  margin-top: 10px;
  margin-bottom: 10px;
}

.promptCard {
  border: 1px solid var(--color-border);
  border-radius: 8px;
  padding: 6px;
  cursor: pointer;
  transition:
    all 0.2s,
    background-color 0.3s ease,
    color 0.3s ease,
    border-color 0.3s ease;
  background-color: var(--color-background-tertiary);
}

.promptCard:hover {
  border-color: var(--color-primary);
  background-color: rgba(var(--color-primary-rgb), 0.1);
}

.promptCard h4 {
  margin: 0 0 4px 0;
  font-size: 13px;
  font-weight: 600;
}

.promptText {
  margin: 0;
  font-size: 12px;
  color: var(--color-text-primary);
  line-height: 1.4;
  transition: color 0.3s ease;
}

.customPromptInput {
  margin-top: 5px;
  font-size: 14px;
  border: 1px solid var(--color-border);
  resize: none;
  background-color: var(--color-background-secondary);
  color: var(--color-text-primary);
  transition:
    background-color 0.3s ease,
    color 0.3s ease,
    border-color 0.3s ease;
}

.selectedCard {
  border-color: var(--color-primary);
  background-color: rgba(var(--color-primary-rgb), 0.2);
}

.dialogFooter {
  padding: 10px 16px;
  border-top: 1px solid var(--color-border);
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  transition: border-color 0.3s ease;
}

.primaryButton,
.secondaryButton {
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.primaryButton {
  background-color: var(--color-primary);
  color: var(--color-primary-contrast);
  border: none;
  transition:
    background-color 0.3s ease,
    color 0.3s ease;
}

.primaryButton:hover {
  background-color: var(--color-primary-dark);
}

.primaryButton:disabled {
  background-color: var(--color-primary-light);
  opacity: 0.6;
  cursor: not-allowed;
}

.secondaryButton {
  background-color: var(--color-background-secondary);
  color: var(--color-text-secondary);
  border: 1px solid var(--color-border);
  transition:
    background-color 0.3s ease,
    color 0.3s ease,
    border-color 0.3s ease;
}

.secondaryButton:hover {
  background-color: var(--color-background-tertiary);
  color: var(--color-text-primary);
}

.loadingOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 10px;
  z-index: 1001;
  backdrop-filter: blur(2px);
}

/* Dark theme support for loading overlay */
[data-theme='dark'] .loadingOverlay {
  background-color: rgba(0, 0, 0, 0.8);
}

@media (max-width: 768px) {
  .twoColumnLayout {
    grid-template-columns: 1fr;
  }

  .dialogContent {
    height: auto;
    max-height: 70vh;
    overflow-y: auto;
  }
}

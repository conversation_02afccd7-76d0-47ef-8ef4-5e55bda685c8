import React, { useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  <PERSON>,
  Typo<PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Divider,
  Chip,
} from '@mui/material';
import { CheckCircle, Receipt, Home } from '@mui/icons-material';
import { PaymentRecord } from '../../types/payment';
import { formatCurrency } from '../../utils/payment/stripeHelpers';
import { usePayment } from '../../hooks/payment/usePayment';
import styles from './PaymentSuccess.module.css';

const PaymentSuccess: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { clearPaymentError, clearPaymentSuccess } = usePayment();
  const paymentRecord = location.state?.paymentRecord as PaymentRecord;

  // Clear any previous payment states when component mounts
  useEffect(() => {
    clearPaymentError();
    clearPaymentSuccess();
  }, [clearPaymentError, clearPaymentSuccess]);

  // Validate payment record has required fields
  const isValidPaymentRecord = (
    record: PaymentRecord | undefined
  ): record is PaymentRecord => {
    return !!(
      record &&
      record.id &&
      record.amount &&
      record.currency &&
      record.status &&
      record.planName
    );
  };

  const handleContinue = () => {
    navigate('/');
  };

  const handleViewHistory = () => {
    navigate('/payment/history');
  };

  if (!isValidPaymentRecord(paymentRecord)) {
    return (
      <Box className={styles.container}>
        <Card className={styles.errorCard}>
          <CardContent>
            <Typography
              variant="h6"
              color="error"
              className={styles.errorTitle}
            >
              Payment Information Not Found
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              We couldn't find your payment details. This might happen if you
              navigated here directly or if your session expired.
            </Typography>
            <Box className={styles.errorActions}>
              <Button
                variant="contained"
                onClick={handleContinue}
                startIcon={<Home />}
                className={styles.button}
              >
                Return to Home
              </Button>
              <Button
                variant="outlined"
                onClick={handleViewHistory}
                startIcon={<Receipt />}
                className={styles.button}
              >
                View Payment History
              </Button>
            </Box>
          </CardContent>
        </Card>
      </Box>
    );
  }

  return (
    <Box className={styles.container}>
      <Card className={styles.successCard}>
        <CardContent className={styles.content}>
          {/* Success Icon */}
          <div className={styles.iconContainer}>
            <CheckCircle className={styles.successIcon} />
          </div>

          {/* Success Message */}
          <Typography variant="h4" className={styles.title}>
            Payment Successful!
          </Typography>

          <Typography variant="body1" className={styles.subtitle}>
            Thank you for your purchase. Your payment has been processed
            successfully.
          </Typography>

          <Divider className={styles.divider} />

          {/* Payment Details */}
          <div className={styles.details}>
            <Typography variant="h6" className={styles.detailsTitle}>
              <Receipt className={styles.receiptIcon} />
              Payment Details
            </Typography>

            <div className={styles.detailRow}>
              <Typography variant="body2" className={styles.label}>
                Plan:
              </Typography>
              <Typography variant="body2" className={styles.value}>
                {paymentRecord.planName}
              </Typography>
            </div>

            <div className={styles.detailRow}>
              <Typography variant="body2" className={styles.label}>
                Amount:
              </Typography>
              <Typography variant="body2" className={styles.value}>
                {formatCurrency(paymentRecord.amount, paymentRecord.currency)}
              </Typography>
            </div>

            <div className={styles.detailRow}>
              <Typography variant="body2" className={styles.label}>
                Payment ID:
              </Typography>
              <Typography variant="body2" className={styles.value}>
                {paymentRecord.id}
              </Typography>
            </div>

            <div className={styles.detailRow}>
              <Typography variant="body2" className={styles.label}>
                Date:
              </Typography>
              <Typography variant="body2" className={styles.value}>
                {new Date(paymentRecord.createdAt).toLocaleDateString()}
              </Typography>
            </div>

            <div className={styles.detailRow}>
              <Typography variant="body2" className={styles.label}>
                Status:
              </Typography>
              <Chip
                label="Successful"
                color="success"
                size="small"
                className={styles.statusChip}
              />
            </div>
          </div>

          <Divider className={styles.divider} />

          {/* Next Steps */}
          <div className={styles.nextSteps}>
            <Typography variant="h6" className={styles.nextStepsTitle}>
              What's Next?
            </Typography>

            <ul className={styles.stepsList}>
              <li>
                You now have access to all {paymentRecord.planName} plan
                features
              </li>
              <li>A confirmation email has been sent to your email address</li>
              <li>
                You can view your payment history in your account settings
              </li>
              <li>Need help? Contact our support team anytime</li>
            </ul>
          </div>

          {/* Action Buttons */}
          <div className={styles.actions}>
            <Button
              variant="outlined"
              onClick={handleViewHistory}
              className={styles.secondaryButton}
            >
              View Payment History
            </Button>

            <Button
              variant="contained"
              onClick={handleContinue}
              startIcon={<Home />}
              className={styles.primaryButton}
            >
              Go to Dashboard
            </Button>
          </div>

          {/* Support Info */}
          <Typography variant="body2" className={styles.supportInfo}>
            Need help? Contact us at{' '}
            <a href="mailto:<EMAIL>" className={styles.supportLink}>
              <EMAIL>
            </a>
          </Typography>
        </CardContent>
      </Card>
    </Box>
  );
};

export default PaymentSuccess;

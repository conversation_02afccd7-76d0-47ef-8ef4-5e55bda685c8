import React from 'react';
import { Controller } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';
import toast from 'react-hot-toast';
import CustomButton from '../../../components/common/button/CustomButton';
import CustomInput from '../../../components/common/input/CustomInput';
import { useLoginForm, useResetPasswordForm } from '../../../hooks/useAuthForm';
import styles from '../LoginPage.module.css';
import { useResetPasswordMutation } from '../../../services';

interface ResetPasswordFormProps {
  token: string;
}

interface ResetPasswordData {
  password: string;
  confirmPassword: string;
}

const ResetPasswordForm: React.FC<ResetPasswordFormProps> = ({ token }) => {
  const [resetPassword] = useResetPasswordMutation();
  const navigate = useNavigate();

  const {
    control,
    handleSubmit,
    formState: { errors },
    watch,
  } = useResetPasswordForm();

  const password = watch('password');

  const handleResetPassword = async (data: ResetPasswordData) => {
    if (data.password !== data.confirmPassword) {
      toast.error('Passwords do not match');
      return;
    }
    try {
      await resetPassword({
        token,
        newPassword: data.confirmPassword,
      }).unwrap();
      toast.success('Password reset successful');
      navigate('/login');
    } catch (error) {
      toast.error('Failed to reset password');
    }
  };

  return (
    <form
      className={styles.formContainer}
      onSubmit={handleSubmit(handleResetPassword)}
    >
      <Controller
        name="password"
        control={control}
        rules={{
          required: 'Password is required',
          minLength: {
            value: 8,
            message: 'Password must be at least 8 characters',
          },
        }}
        render={({ field }) => (
          <CustomInput
            {...field}
            label="New Password"
            type="password"
            placeholder="Enter your new password"
            error={!!errors.password?.message}
            helperText={errors.password?.message}
            required
            disableDarkTheme={true}
          />
        )}
      />

      <Controller
        name="confirmPassword"
        control={control}
        rules={{
          required: 'Please confirm your password',
          validate: (value) => value === password || 'Passwords do not match',
        }}
        render={({ field }) => (
          <CustomInput
            {...field}
            label="Confirm Password"
            type="password"
            placeholder="Confirm your new password"
            error={!!errors.confirmPassword?.message}
            helperText={errors.confirmPassword?.message}
            required
            disableDarkTheme={true}
          />
        )}
      />

      <CustomButton label="Reset Password" type="primary" variant="submit" />
    </form>
  );
};

export default ResetPasswordForm;

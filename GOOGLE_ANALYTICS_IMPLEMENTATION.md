# Google Analytics 4 (GA4) Implementation Guide

## Overview

This document outlines the complete implementation of Google Analytics 4 tracking in the NEUQUIP React application. The implementation provides comprehensive tracking of user interactions, page views, and key business events.

## Features Implemented

### 1. **Automatic Page View Tracking**
- Tracks all route changes automatically using React Router
- Includes page title, URL, and user ID (for authenticated users)
- Handles both authenticated and non-authenticated sessions

### 2. **User Authentication Tracking**
- Login events with method tracking
- Signup events with method tracking
- Logout events
- User property tracking (role, subscription status)

### 3. **Navigation and Interaction Tracking**
- Sidebar menu clicks
- Button clicks with context
- Form submissions
- Search events

### 4. **Content Creation Tracking**
- Project creation events
- Sketchbook creation with template types
- File uploads with file type and size
- Chat message sending

### 5. **Workflow Management Tracking**
- Workflow creation events
- Approval/rejection actions
- Team collaboration events

### 6. **Payment and E-commerce Tracking**
- Payment initiation events
- Successful payment completion
- Payment error tracking
- Transaction details with amounts and currencies

### 7. **Error Tracking**
- JavaScript errors with stack traces
- API errors with endpoint and status codes
- User context for debugging

## File Structure

```
src/
├── utils/
│   └── analytics.ts                 # Core analytics functions
├── hooks/
│   └── useAnalytics.ts             # React hooks for analytics
├── components/
│   └── common/
│       └── analytics/
│           └── AnalyticsWrapper.tsx # Wrapper components
└── App.tsx                         # Analytics initialization
```

## Configuration

### Environment Variables

Add to your `.env` file:
```bash
VITE_GA4_MEASUREMENT_ID=G-12345678
```

Replace `G-12345678` with your actual Google Analytics 4 measurement ID.

### HTML Setup

The GA4 script is dynamically loaded in `index.html` using the environment variable:

```html
<!-- Google Analytics 4 -->
<script>
  const GA4_MEASUREMENT_ID = '%VITE_GA4_MEASUREMENT_ID%';
  if (GA4_MEASUREMENT_ID && GA4_MEASUREMENT_ID !== '%VITE_GA4_MEASUREMENT_ID%') {
    // Dynamic script loading and initialization
  }
</script>
```

## Usage Examples

### Basic Page Tracking
Page tracking is automatic when you use the `usePageTracking()` hook in your main App component:

```typescript
import { usePageTracking } from './hooks/useAnalytics';

function App() {
  usePageTracking(); // Automatically tracks all page views
  return <YourAppContent />;
}
```

### Event Tracking
```typescript
import { useAnalyticsEvents } from './hooks/useAnalytics';

function MyComponent() {
  const { trackButtonClick, trackCreateProject } = useAnalyticsEvents();

  const handleCreateProject = () => {
    trackCreateProject('data-analysis');
    // Your project creation logic
  };

  return (
    <button onClick={handleCreateProject}>
      Create Project
    </button>
  );
}
```

### Wrapper Components
For automatic tracking without manual event calls:

```typescript
import { AnalyticsClickWrapper } from './components/common/analytics/AnalyticsWrapper';

function MyButton() {
  return (
    <AnalyticsClickWrapper eventName="create_project_button">
      <button>Create Project</button>
    </AnalyticsClickWrapper>
  );
}
```

## Tracked Events

### Authentication Events
- `login` - User login with method
- `sign_up` - User registration with method
- `logout` - User logout

### Navigation Events
- `menu_click` - Sidebar menu navigation
- `button_click` - Button interactions
- `page_view` - Page navigation

### Content Events
- `create_project` - Project creation
- `create_sketchbook` - Sketchbook creation
- `file_upload` - File uploads
- `send_message` - Chat messages

### Workflow Events
- `create_workflow` - Workflow creation
- `approve_workflow` - Workflow approval
- `reject_workflow` - Workflow rejection

### E-commerce Events
- `begin_checkout` - Payment initiation
- `purchase` - Successful payment
- `payment_error` - Payment failures

### Error Events
- `exception` - JavaScript errors
- `api_error` - API failures

## User Properties

The system automatically tracks:
- `user_id` - Unique user identifier
- `user_role` - User role (admin, user, etc.)
- `subscription_status` - Subscription tier

## Privacy and Compliance

The implementation includes privacy-focused settings:
- IP anonymization enabled
- Google Signals disabled
- Ad personalization disabled
- Debug mode for development

## Development vs Production

### Development Mode
- Debug logging enabled
- Console output for all events
- Detailed error information

### Production Mode
- Silent operation
- Optimized performance
- Error tracking without console spam

## Testing

### Verify Implementation
1. Open browser developer tools
2. Check console for GA4 debug messages (development mode)
3. Use Google Analytics DebugView
4. Verify events in GA4 Real-time reports

### Debug Commands
```javascript
// Check if GA4 is loaded
console.log(window.gtag);
console.log(window.dataLayer);

// Manual event testing
window.gtag('event', 'test_event', {
  event_category: 'testing',
  event_label: 'manual_test'
});
```

## Components with Analytics

### Already Implemented
- ✅ Login/Registration forms
- ✅ Chat functionality (message sending, file uploads)
- ✅ Payment forms
- ✅ Sidebar navigation
- ✅ Error boundaries

### Recommended Additions
- Project creation buttons
- Sketchbook template selection
- Workflow actions
- Search functionality
- Export/download actions

## Best Practices

1. **Event Naming**: Use consistent, descriptive event names
2. **User Context**: Always include user ID when available
3. **Error Handling**: Gracefully handle analytics failures
4. **Performance**: Analytics should never block UI interactions
5. **Privacy**: Respect user privacy settings and regulations

## Troubleshooting

### Common Issues

1. **Events not appearing in GA4**
   - Check measurement ID is correct
   - Verify environment variable is set
   - Check browser console for errors

2. **Development mode not working**
   - Ensure `VITE_DEV=true` in environment
   - Check console for debug messages

3. **User properties not updating**
   - Verify user authentication state
   - Check Redux store for user data

### Debug Checklist
- [ ] GA4 measurement ID configured
- [ ] Environment variables loaded
- [ ] gtag function available globally
- [ ] User authentication working
- [ ] Console shows debug messages (dev mode)

## Future Enhancements

1. **Enhanced E-commerce Tracking**
   - Product views
   - Cart interactions
   - Subscription management

2. **Custom Dimensions**
   - Feature usage patterns
   - User journey mapping
   - Performance metrics

3. **Advanced Segmentation**
   - User cohorts
   - Behavioral analysis
   - Conversion funnels

## Support

For issues or questions regarding the analytics implementation:
1. Check this documentation
2. Review console errors
3. Test in GA4 DebugView
4. Verify environment configuration
